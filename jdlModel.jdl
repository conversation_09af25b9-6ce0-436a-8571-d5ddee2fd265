entity Siren {
    sirenNum String maxlength(9) required,
    companyName String maxlength(100) required,
    entityType TypeEntiteDestinataire required,
    modificationDate Instant required
}

entity Siret {
    siretNum String maxlength(14) required,
    principalEstablishment TypeEntity required,
    label String maxlength(100) required,
    addressLigne1 String maxlength(109) required,
    addressLigne2 String maxlength(38) required,
    addressLigne3 String maxlength(26) required,
    town String maxlength(100) required,
    postalCode Integer required,
    country String maxlength(100) required,
    modificationDate Instant required
}

entity RoutingCode {
    routingCode String maxlength(100) required,
    label String maxlength(100) required,
    type Type required,
    modificationDate Instant required
}

entity AddressLine {
    addressLineCode String maxlength(125) required,
    applicationDate Instant,
    codeTypeModification TypeModificationCode required,
    modificationId String maxlength(50) required,
    entityType TypeEntiteDestinataire required,
    invoicingLineStatus Statut required,
    platformType PlatformType required,
    platformReceptionRegNumber Integer required,
    pdpSocialRaison String maxlength(100) required,
    pdpCommercialName String maxlength(100),
    platformReceptionDataContact  String maxlength(100),
    platformPeriodStart Instant,
    platformPeriodEnd Instant,
    platformReceptionState Statut required,
    managementLegalEngagement Boolean,
    managementService Boolean,
    managementServiceOrLegal Boolean,
    moa Boolean,
    moaOnly Boolean,
    managementStatePayment Boolean,
	modificationDate Instant required
}

enum PlatformType {
    PPF,
    PDP
}

enum TypeModificationCode {
    A,
    M,
    I
}

enum TypeEntity {
    P,
    S
}

enum Type {
    DUNS ("0060"),
    GLN ("0088"),
    ODETTE ("0177"),
    CODESERVICE ("0224")
}

enum Statut {
    ACTIF,
    INACTIF
}

enum TypeEntiteDestinataire {
    PUBLIQUE,
    ASSUJETTI
}

// defining multiple OneToMany relationships with comments
relationship OneToMany {
    Siren{pkSiret} to Siret {fkSiretSiren},
    Siret {pkRoutingCode} to RoutingCode {fkRoutingCodeSiret},
}

relationship OneToMany {
    Siren {pkAddressLine} to AddressLine{fkAddressLineSiren},
    Siret {pkAddressLine} to AddressLine{fkAddressLineSiret}
}

relationship OneToOne {
    AddressLine{pkRoutingCode} to RoutingCode{fkRoutingCodeAddressLine}
}


// Set pagination options
paginate Siren, Siret, RoutingCode, AddressLine with pagination

// Use Data Transfer Objects (DTO)
dto * with mapstruct

// Set service options to all except few
service all with serviceImpl

// Set an angular suffix
// angularSuffix * with mySuffix

filter Siren, Siret, RoutingCode, AddressLine


