{"generator-jhipster": {"applicationType": "microservice", "authenticationType": "oauth2", "baseName": "legalReferential", "blueprints": [], "buildTool": "maven", "cacheProvider": "ehcache", "clientFramework": "no", "creationTimestamp": *************, "databaseType": "sql", "devDatabaseType": "postgresql", "dtoSuffix": "DTO", "enableGradleEnterprise": false, "enableHibernateCache": true, "enableSwaggerCodegen": true, "enableTranslation": true, "entities": ["<PERSON><PERSON>", "<PERSON><PERSON>", "RoutingCode", "AddressLine", "Platform"], "entitySuffix": "", "jhiPrefix": "jhi", "jhipsterVersion": "7.9.3", "jwtSecretKey": "NDI1Y2M1YzY2YzgwMmNiNmY5Njc2MGM0ZTBlNThjOTdhZTA4ZGZiN2MxOTI3N2E2M2Q3MDgxYWRkNGJhMGIzZGRlZTY5YjZhNzc4YjIyYjNhMzIzZDk5ZjY1Y2M4YTRlMDVjNDZiMTBkNDRhZTI1ODRmOTZhOWE0MzdmODU4MmY=", "languages": ["fr", "en"], "lastLiquibaseTimestamp": *************, "messageBroker": false, "microfrontend": false, "microfrontends": [], "nativeLanguage": "fr", "otherModules": [], "packageName": "com.generix.legalreferential", "pages": [], "prodDatabaseType": "postgresql", "reactive": false, "searchEngine": false, "serverPort": "8080", "serverSideOptions": ["enableSwaggerCodegen:true"], "serviceDiscoveryType": "no", "skipCheckLengthOfIdentifier": false, "skipClient": true, "skipFakeData": false, "skipUserManagement": true, "testFrameworks": [], "websocket": false, "withAdminUi": false}}