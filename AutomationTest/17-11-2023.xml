<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project id="e892e457-0eaf-4d0d-9283-8e4f627aa374" activeEnvironment="Default" name="Legal referential" resourceRoot="" soapui-version="5.7.2" abortOnError="false" runType="SEQUENTIAL" xmlns:con="http://eviware.com/soapui/config"><con:settings/><con:interface xsi:type="con:RestService" id="5fc0c3c0-feb5-47a5-b845-9db483d9e328" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="" path="/api/v1/ligne-annuaire/recherche" id="993a8e60-f948-4d45-976a-17a3ca14e954"><con:settings/><con:parameters><con:parameter><con:name>siren-destinataire</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>format</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>est-rech-code-routage-stricte</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="SirenNumSearch" id="71a2909a-5d0a-4d2f-899d-6bed293b97b5" method="POST"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 400</con:status><con:params/><con:element xmlns:rec="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche">rec:Fault</con:element></con:representation><con:representation type="FAULT"><con:mediaType>application/json</con:mediaType><con:status>500 404</con:status><con:params/><con:element xmlns:rec="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche">rec:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/x-www-form-urlencoded</con:mediaType><con:params/></con:representation><con:representation type="RESPONSE"><con:mediaType>text/html</con:mediaType><con:status>200</con:status><con:params/><con:element>html</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401 500 404 502</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>multipart/form-data</con:mediaType><con:params/></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/><con:element>Response</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="Siren" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJMOEtCMURyTGJfNDVVTkdhVmFxMlZWVXBtLTlqSU9mNjc2LTlwbUV6bDh3In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ELnViOekWIw2Iavpa8oyum4Bj1zy0uK0cnjSqRXrdIWsK6WFOiMdT_afJQZe1Y0TxgTCz3UgYur3JpBOIapIDJWuBXEFXQ_7Cwl_nPg3gkGOlXtxou2RtEYbEgxk5HOkTPPCZqlJbw8gGL_lkK2e5lAmxeyW3jE7U-STdNdQda3tnd3fUOO7P5VaIWRNWkMYB_NohgJkEoUzoFsuz3tD8t4kwsuUe0LY2oX_WOTG2aza47p9_602fFU6VgBUqtact5NECkOuhTNGand0fLxsQkvsYszz3dkQCKJFDdFoHvghOcxp3XZyh1ORZCuOnN19XLkwq1v-RUkfK9nxEnpyvA"/>&#13;
  &lt;con:entry key="tenant" value="legalreferential"/>&#13;
  &lt;con:entry key="accept" value="*/*"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="1234"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="007c3382-7f95-4f2b-a377-d045580b04bc" wadlVersion="http://wadl.dev.java.net/2009/02" name="https://auth.staging.apps.generix.biz" type="rest" basePath="" definitionUrl="C:\Users\<USER>\OneDrive - NUMERYX\Desktop\_1.wadl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart="file:/C:/Users/<USER>/OneDrive%20-%20NUMERYX/Desktop/_1.wadl"><con:part><con:url>file:/C:/Users/<USER>/OneDrive%20-%20NUMERYX/Desktop/_1.wadl</con:url><con:content><![CDATA[<application xmlns="http://wadl.dev.java.net/2009/02">
  <doc xml:lang="en" title="https://auth.staging.apps.generix.biz"/>
  <resources base="https://auth.staging.apps.generix.biz">
    <resource path="auth/realms/legalreferential/protocol/openid-connect/token" id="Token">
      <doc xml:lang="en" title="Token"/>
      <param name="client_id" type="xs:string" required="false" default="" style="query" xmlns:xs="http://www.w3.org/2001/XMLSchema"/>
      <param name="username" type="xs:string" required="false" default="" style="query" xmlns:xs="http://www.w3.org/2001/XMLSchema"/>
      <param name="password" type="xs:string" required="false" default="" style="query" xmlns:xs="http://www.w3.org/2001/XMLSchema"/>
      <param name="grant_type" type="xs:string" required="false" default="" style="query" xmlns:xs="http://www.w3.org/2001/XMLSchema"/>
      <param name="client_secret" type="xs:string" required="false" default="" style="query" xmlns:xs="http://www.w3.org/2001/XMLSchema"/>
      <method name="POST" id="Token 1">
        <doc xml:lang="en" title="Token 1"/>
        <request>
          <representation mediaType="application/x-www-form-urlencoded"/>
        </request>
        <response status="200">
          <representation mediaType="application/json"/>
        </response>
        <response status="">
          <representation mediaType="application/x-www-form-urlencoded"/>
        </response>
        <response status="400 401">
          <representation mediaType="application/json"/>
        </response>
      </method>
    </resource>
  </resources>
</application>]]></con:content><con:type>http://wadl.dev.java.net/2009/02</con:type></con:part></con:definitionCache><con:endpoints><con:endpoint>https://auth.staging.apps.generix.biz</con:endpoint></con:endpoints><con:resource name="Token" path="/realms/${#Project#Tenant}/protocol/openid-connect/token" id="31f9d221-9721-4365-af01-60bef8a680fd"><con:settings/><con:parameters><con:parameter><con:name>client_id</con:name><con:value/><con:style>QUERY</con:style><con:default/></con:parameter><con:parameter><con:name>username</con:name><con:value/><con:style>QUERY</con:style><con:default/></con:parameter><con:parameter><con:name>password</con:name><con:value/><con:style>QUERY</con:style><con:default/></con:parameter><con:parameter><con:name>grant_type</con:name><con:value/><con:style>QUERY</con:style><con:default/></con:parameter><con:parameter><con:name>client_secret</con:name><con:value/><con:style>QUERY</con:style><con:default/></con:parameter></con:parameters><con:method name="Token 1" id="a82aecb6-3f44-48ce-9a57-d50a63419c83" method="POST"><con:settings/><con:parameters/><con:representation type="REQUEST" id=""><con:mediaType>application/x-www-form-urlencoded</con:mediaType><con:params/><con:element xsi:nil="true"/><con:description xsi:nil="true"/></con:representation><con:representation type="RESPONSE" id=""><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/><con:element xsi:nil="true"/><con:description xsi:nil="true"/></con:representation><con:representation type="RESPONSE" id=""><con:mediaType>application/x-www-form-urlencoded</con:mediaType><con:status/><con:params/><con:element xsi:nil="true"/><con:description xsi:nil="true"/></con:representation><con:representation type="FAULT" id=""><con:mediaType>application/json</con:mediaType><con:status>400 401</con:status><con:params/><con:element xsi:nil="true"/><con:description xsi:nil="true"/></con:representation><con:request name="Request 1" id="7ca57809-dc7d-4bee-910a-163df3f91083" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="6RXBGheyXWzjovNWg27JDaM8txiQsAXf"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="931a0c5f-e28e-47a9-a7e8-495500355816" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Recherche" path="/api/v1/ligne-annuaire/recherche" id="b15a25ae-308d-4b3f-9bd8-687cb0811b13"><con:settings/><con:parameters><con:parameter><con:name>format</con:name><con:value>JSON</con:value><con:style>QUERY</con:style><con:default>JSON</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>siret-destinataire</con:name><con:value>22</con:value><con:style>QUERY</con:style><con:default>22</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>est-rech-code-routage-stricte</con:name><con:value>true</con:value><con:style>QUERY</con:style><con:default>true</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="SiretNum" id="f3ac5714-d716-4914-b3ed-d3c3809fc51f" method="POST"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>404 401 400</con:status><con:params/><con:element xmlns:rec="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche">rec:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/><con:element>Response</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="22"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="b71569bb-dbb5-4ca9-a851-0ca1c84875fd" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Recherche" path="/api/v1/ligne-annuaire/recherche" id="8437b4a5-0d38-43d6-8111-a20e9c6039f9"><con:settings/><con:parameters><con:parameter><con:name>format</con:name><con:value>JSON</con:value><con:style>QUERY</con:style><con:default>JSON</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>code-routage-destinataire</con:name><con:value>123</con:value><con:style>QUERY</con:style><con:default>123</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>est-rech-code-routage-stricte</con:name><con:value>true</con:value><con:style>QUERY</con:style><con:default>true</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="RoutingCode" id="614ffb57-2ea0-4903-859c-9fddb7890227" method="POST"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 404 400</con:status><con:params/><con:element xmlns:rec="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche">rec:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/><con:element>Response</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="RoutingCode" id="dea67ace-36be-4ae6-bcfd-7a12055f063b" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="code-routage-destinataire" value="123"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="2c8685b2-b974-44c8-a5e7-e3de28c64de8" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Recherche" path="/api/v1/ligne-annuaire/recherche" id="7f0eaaeb-e639-4244-b445-d774bbce6609"><con:settings/><con:parameters><con:parameter><con:name>format</con:name><con:value>JSON</con:value><con:style>QUERY</con:style><con:default>JSON</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>raison-sociale-destinataire</con:name><con:value>Company XYZ</con:value><con:style>QUERY</con:style><con:default>Company XYZ</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>est-rech-code-routage-stricte</con:name><con:value>true</con:value><con:style>QUERY</con:style><con:default>true</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="CompanyName" id="4a34f73f-eb78-4541-bdb1-1e8c4943f14b" method="POST"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 400</con:status><con:params/><con:element xmlns:rec="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche">rec:Fault</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/><con:element>Response</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="CompanyName" id="206b2ab6-887e-4890-9b73-54aea639f5f0" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="format" value="JSON"/>
  <con:entry key="raison-sociale-destinataire" value="Company XYZ"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>raison-sociale-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="321a262c-868a-4548-8480-14a83c0e5d22" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="12345-6789" path="/api/v1" id="571666a5-bb45-479c-aa28-c0afed1cdd5b"><con:settings/><con:parameters><con:parameter><con:name>ligne-annuaire</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="AdressLine" id="2f0551ee-ee16-4e9c-acfc-bea96168d551" method="GET"><con:settings/><con:parameters/><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/><con:element xmlns:ns="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/12345-6789">ns:Response</con:element></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401</con:status><con:params/><con:element xmlns:l="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/l">l:Fault</con:element></con:representation><con:representation type="FAULT"><con:mediaType>application/json</con:mediaType><con:status>404</con:status><con:params/><con:element xmlns:ns="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/12345-6789">ns:Fault</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="AdressLine" id="af8548a4-e2c5-49c8-ae0c-c16c53a929cc" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/l</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="ligne-annuaire" value="12345-6789" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>ligne-annuaire</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="9425319e-2890-41f6-86f4-057814c8d2b8" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Address-lines" path="/api/address-lines" id="01288ef5-6826-4824-a658-162355be710b"><con:settings/><con:parameters/><con:method name="Address-lines 1" id="975e89b4-6f1a-418a-bb27-58043d93acf5" method="POST"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 400 500 415</con:status><con:params/><con:element xmlns:add="http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines">add:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/x-www-form-urlencoded</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/json</con:mediaType><con:status>500 404</con:status><con:params/><con:element xmlns:add="http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines">add:Fault</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>201</con:status><con:params/><con:element xmlns:add="http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines">add:Response</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="Request 1" id="26e3c2dc-be7f-4bc6-8444-ffb8db7c49f6" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "id": 0,
  "addressLineCode": "A1234",
  "applicationDate": "2023-10-31T08:02:27.213Z",
  "codeTypeModification": "A",
  "modificationId": "string",
  "entityType": "PUBLIQUE",
  "invoicingLineStatus": "ACTIF",
  "platformType": "PPF",
  "platformReceptionRegNumber": 0,
  "pdpSocialRaison": "string",
  "pdpCommercialName": "string",
  "platformReceptionDataContact": "string",
  "platformPeriodStart": "2023-10-31T08:02:27.213Z",
  "platformPeriodEnd": "2023-10-31T08:02:27.213Z",
  "platformReceptionState": "ACTIF",
  "managementLegalEngagement": true,
  "managementService": true,
  "managementServiceOrLegal": true,
  "moa": true,
  "moaOnly": true,
  "managementStatePayment": true,
  "modificationDate": "2023-10-31T08:02:27.213Z",
  "pkRoutingCode": {
    "id": 0,
    "routingCode": "string",
    "label": "string",
    "type": "DUNS",
    "modificationDate": "2023-10-31T08:02:27.213Z",
    "fkRoutingCodeSiret": {
      "id": 0,
      "siretNum": "string",
      "principalEstablishment": "P",
      "label": "string",
      "addressLigne1": "string",
      "addressLigne2": "string",
      "addressLigne3": "string",
      "town": "string",
      "postalCode": 0,
      "country": "string",
      "modificationDate": "2023-10-31T08:02:27.213Z",
      "fkSiretSiren": {
        "id": 0,
        "sirenNum": "string",
        "companyName": "string",
        "entityType": "PUBLIQUE",
        "modificationDate": "2023-10-31T08:02:27.213Z"
      }
    }
  },
  "fkAddressLineSiren": {
    "id": 0,
    "sirenNum": "string",
    "companyName": "string",
    "entityType": "PUBLIQUE",
    "modificationDate": "2023-10-31T08:02:27.213Z"
  },
  "fkAddressLineSiret": {
    "id": 0,
    "siretNum": "string",
    "principalEstablishment": "P",
    "label": "string",
    "addressLigne1": "string",
    "addressLigne2": "string",
    "addressLigne3": "string",
    "town": "string",
    "postalCode": 0,
    "country": "string",
    "modificationDate": "2023-10-31T08:02:27.213Z",
    "fkSiretSiren": {
      "id": 0,
      "sirenNum": "string",
      "companyName": "string",
      "entityType": "PUBLIQUE",
      "modificationDate": "2023-10-31T08:02:27.213Z"
    }
  }
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="4018b6ee-98c6-4057-a6a0-93ab9c23e7ee" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Sirets" path="/api/sirets" id="624f5190-79f5-4113-a0ad-b14019ab8c9f"><con:settings/><con:parameters/><con:method name="Sirets 1" id="ee333e0b-584f-400f-abad-4ea54181e461" method="POST"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 400</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirets">sir:Fault</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>201</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirets">sir:Response</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="CreateSiret" id="68b6b5d0-2534-43cd-9508-4614923504aa" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "id": 0,
  "siretNum": "string",
  "principalEstablishment": "P",
  "label": "string",
  "addressLigne1": "string",
  "addressLigne2": "string",
  "addressLigne3": "string",
  "town": "string",
  "postalCode": 0,
  "country": "string",
  "modificationDate": "2023-11-10T09:09:07.132Z",
  "fkSiretSiren": {
    "id": 0,
    "sirenNum": "string",
    "companyName": "string",
    "entityType": "PUBLIQUE",
    "modificationDate": "2023-11-10T09:09:07.132Z"
  }
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirets</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="51a10441-60ea-4a05-a31f-363e27298709" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Routing-codes" path="/api/routing-codes" id="39973e49-bd81-45f1-874d-ad9bb366b750"><con:settings/><con:parameters/><con:method name="Routing-codes 1" id="8d607ac6-883f-404d-8bca-388448a84695" method="POST"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 500</con:status><con:params/><con:element xmlns:rout="http://legal-referential.chassagne-qa.generixgroup.com/api/routing-codes">rout:Fault</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>201</con:status><con:params/><con:element xmlns:rout="http://legal-referential.chassagne-qa.generixgroup.com/api/routing-codes">rout:Response</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="CreateRoutungCode" id="05923bd2-4ec0-46f8-af3d-a39510c8e5fe" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "id": 0,
  "routingCode": "string",
  "label": "string",
  "type": "DUNS",
  "modificationDate": "2023-11-10T09:16:36.103Z",
  "fkRoutingCodeSiret": {
    "id": 0,
    "siretNum": "string",
    "principalEstablishment": "P",
    "label": "string",
    "addressLigne1": "string",
    "addressLigne2": "string",
    "addressLigne3": "string",
    "town": "string",
    "postalCode": 0,
    "country": "string",
    "modificationDate": "2023-11-10T09:16:36.103Z",
    "fkSiretSiren": {
      "id": 0,
      "sirenNum": "string",
      "companyName": "string",
      "entityType": "PUBLIQUE",
      "modificationDate": "2023-11-10T09:16:36.103Z"
    }
  }
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/routing-codes</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="56d7ecd3-3788-444a-8420-754015e17111" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Address-lines" path="/api/address-lines" id="08b2afad-88bb-4d27-8f8a-f7b5e171ac32"><con:settings/><con:parameters/><con:method name="Address-lines 1" id="345ef73e-6635-4aa9-940e-1792633dfb3c" method="POST"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401</con:status><con:params/><con:element xmlns:add="http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines">add:Fault</con:element></con:representation><con:request name="CreateAdressLine" id="37f76b19-0068-4927-93cf-4136d3943ac6" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  
  "addressLineCode": "string",
  "applicationDate": "2023-11-10T08:33:52.579Z",
  "codeTypeModification": "A",
  "modificationId": "string",
  "entityType": "PUBLIQUE",
  "invoicingLineStatus": "ACTIF",
  "platformType": "PPF",
  "platformReceptionRegNumber": 0,
  "pdpSocialRaison": "string",
  "pdpCommercialName": "string",
  "platformReceptionDataContact": "string",
  "platformPeriodStart": "2023-11-10T08:33:52.579Z",
  "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
  "platformReceptionState": "ACTIF",
  "managementLegalEngagement": true,
  "managementService": true,
  "managementServiceOrLegal": true,
  "moa": true,
  "moaOnly": true,
  "managementStatePayment": true,
  "modificationDate": "2023-11-10T08:33:52.579Z",
  
  "fkAddressLineSiren": {
    "id": 0,
    "sirenNum": "string",
    "companyName": "string",
    "entityType": "PUBLIQUE",
    "modificationDate": "2023-11-10T08:33:52.579Z"
  },</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="c362ad0c-1d60-41f6-a3fd-03a4d806d63c" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Sirens" path="/api/sirens" id="2c129573-6745-47f6-87ea-04294ce8dfcf"><con:settings/><con:parameters/><con:method name="Sirens 1" id="a1e8bcb0-ab37-454b-b4b7-350077b7fff1" method="POST"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 400 500 415</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens">sir:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>201</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens">sir:Response</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401 404</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType>application/json</con:mediaType><con:status>404</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens">sir:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/x-www-form-urlencoded</con:mediaType><con:params/></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/html</con:mediaType><con:status>502</con:status><con:params/><con:element>html</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="Request 1" id="74e36145-da5c-437e-9b4f-264a72a5b692" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "id": 0,
  "sirenNum": "string",
  "companyName": "string",
  "entityType": "PUBLIQUE",
  "modificationDate": "2023-11-10T08:21:42.875Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="69098fcc-5601-489a-93e6-e7e4697e5731" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Id" path="/api/v1/ligne-annuaire/{id}" id="df5a5625-181d-49ab-b2ef-28699dc0ba84"><con:settings/><con:parameters><con:parameter><con:name>id</con:name><con:value>id</con:value><con:style>TEMPLATE</con:style><con:default>id</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="Id 1" id="66e6d839-e389-4c4d-b733-d6245355d7b5" method="GET"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 404</con:status><con:params/><con:element xmlns:lig="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/">lig:Fault</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType>application/json</con:mediaType><con:status>404</con:status><con:params/><con:element xmlns:lig="http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/">lig:Fault</con:element></con:representation><con:request name="Request 1" id="cc040eb3-8197-4536-8f19-86e1c5605165" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="e70069b9-8fab-4b08-a6c4-34a7c4db465f" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Sirens" path="/api/sirens/" id="2375b1c3-46c9-49b7-bdd0-f7225e0975e0"><con:settings/><con:parameters><con:parameter><con:name>id</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="Sirens 1" id="51c1ba29-685c-410c-a8a4-8c4569b79fb3" method="DELETE"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 405</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/">sir:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/x-www-form-urlencoded</con:mediaType><con:params/></con:representation><con:request name="DeleteSirenByID" id="0c6073f3-a3f6-49b7-a0dd-cb1b4fe569af" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/><con:parameterOrder><con:entry>id</con:entry><con:entry/></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="025d7aa5-210b-44e9-9a41-da16f70568e1" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Address-lines" path="/api/address-lines/" id="b138f5bf-729b-487c-b8be-85f93fd3c3f3"><con:settings/><con:parameters><con:parameter><con:name>ID</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="Address-lines 1" id="fa56ad73-adda-4e31-81ad-18acf565dc15" method="DELETE"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>405</con:status><con:params/><con:element xmlns:add="http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines/">add:Fault</con:element></con:representation><con:request name="DeleteAdressLIneWithID" id="b8ae7152-e9b4-4cbe-a3d3-77bb1298d8aa" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/><con:parameterOrder><con:entry>ID</con:entry><con:entry/></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="2bc1f8ae-e0c5-4b0e-b718-235727b8e0bc" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Sirens" path="/api/sirens" id="1a8a8b6c-52ec-495b-8a2e-aefaf83a2d4c"><con:settings/><con:parameters><con:parameter><con:name>id</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="Sirens 1" id="6c8e3b00-eaa6-42c6-87c5-190a365504fc" method="GET"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens">sir:Fault</con:element></con:representation></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="2672cce8-caa3-4424-8bd8-b994cc1c5854" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="GetSiren" path="/api/sirens/{id}" id="d93883bf-aef5-42a8-9b86-8b8344bf9715"><con:settings/><con:parameters><con:parameter><con:name>id</con:name><con:style>TEMPLATE</con:style></con:parameter></con:parameters><con:method name="GetSirenByID" id="90b83521-ca3d-4eb2-ac00-c89f3837ab14" method="GET"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/json</con:mediaType><con:status>500</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/">sir:Fault</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>400 401</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/">sir:Fault</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/><con:element>Response</con:element></con:representation><con:request name="Request 1" id="5becf1d0-e532-4404-84d0-c022ac986bad" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="}" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry><con:entry/></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="40f13dac-3191-4385-82f6-3342f57ef296" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="DeleteSirenByID" path="/api/sirens/{id}" id="17dd2158-4a73-48d8-85c9-32c16352d7a3"><con:settings/><con:parameters><con:parameter><con:name>id</con:name><con:style>TEMPLATE</con:style></con:parameter></con:parameters><con:method name="DeleteSiren" id="4df05fd0-7b12-4a75-b589-99a4b36ad262" method="DELETE"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 405 500 400</con:status><con:params/><con:element xmlns:ns="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/1224">ns:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/x-www-form-urlencoded</con:mediaType><con:params/></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="Request 1" id="8fba604a-a061-4fea-95c7-af7cba3db176" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/1224</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="{#Testcase#SirenID}" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry><con:entry/></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="d7eefeb6-7c65-4885-9b58-8ee0dab12187" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="PUT Siren" path="/api/sirens/{id}" id="dbfaf47b-8298-47f8-9c58-4535944f5689"><con:settings/><con:parameters><con:parameter><con:name>id</con:name><con:value/><con:style>TEMPLATE</con:style><con:default/></con:parameter></con:parameters><con:method name="PUT Siren" id="ddbf924d-0cb0-482e-acc2-1c5481520fce" method="PUT"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 400 405</con:status><con:params/><con:element xmlns:b="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/%7B%23Testcase%23SirenID%7D">b:Fault</con:element></con:representation><con:representation type="FAULT"><con:mediaType>application/json</con:mediaType><con:status>405</con:status><con:params/><con:element xmlns:b="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/%7B%23Testcase%23SirenID%7D">b:Fault</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/><con:element xmlns:b="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/%7B%23Testcase%23SirenID%7D">b:Response</con:element></con:representation><con:representation type="FAULT"><con:mediaType>text/plain; charset=utf-8</con:mediaType><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="Put Siren" id="22dd9eca-9132-4547-a2cd-818373c3d1ad" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value=""/>&#13;
  &lt;con:entry key="Tenant" value=""/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
 
  "sirenNum": "string",
  "companyName": "string",
  "entityType": "PUBLIQUE",
  "modificationDate": "2023-11-14T09:43:05.868Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/%7B%23Testcase%23SirenID%7D</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="${#TestCase#SirenID}" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="9699b273-ccc9-4dc2-a92b-bf87860c25a0" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="PATCH - siren" path="/api/sirens/{id}" id="e97348a7-9342-47bb-a3c1-272d6d53d5fe"><con:settings/><con:parameters><con:parameter><con:name>id</con:name><con:value>id</con:value><con:style>TEMPLATE</con:style><con:default>id</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="Patch-siren" id="6b16d07e-243f-4daa-bb52-5ce4e2756e13" method="PATCH"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 400 500</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/">sir:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:request name="PATCH-Siren" id="4c50d386-e89b-4154-8b26-481d7a1125bd" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "id": 0,
  "sirenNum": "string",
  "companyName": "string",
  "entityType": "PUBLIQUE",
  "modificationDate": "2023-11-16T08:18:10.470Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="${#TestCase#SirenID}" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="0ad388d9-f144-4569-863d-0abed0ef5238" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Sirens" path="/api/sirens" id="4139f0c0-b689-421e-ae8e-3a756f065ea4"><con:settings/><con:parameters><con:parameter><con:name>id.lessThan</con:name><con:value>1780</con:value><con:style>QUERY</con:style><con:default>1780</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>page</con:name><con:value>0</con:value><con:style>QUERY</con:style><con:default>0</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>size</con:name><con:value>20</con:value><con:style>QUERY</con:style><con:default>20</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>id.greaterThan</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>id.greaterThanOrEqual </con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>id.lessThanOrEqual</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>id.equals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>id.notEquals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>id.specified</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>id.in</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>id.notIn</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>sirenNum.contains</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>sirenNum.doesNotContain</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>sirenNum.equals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>sirenNum.notEquals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>sirenNum.specified</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>sirenNum.in</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>sirenNum.notIn</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>companyName.contains</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>companyName.doesNotContain</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>companyName.equals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>companyName.notEquals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>companyName.specified</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>companyName.in</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>companyName.notIn</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>entityType.equals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>entityType.notEquals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>entityType.specified</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>entityType.in</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>entityType.notIn</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>modificationDate.greaterThan</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>modificationDate.lessThan</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>modificationDate.greaterThanOrEqual</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>modificationDate.lessThanOrEqual</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>modificationDate.equals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>modificationDate.notEquals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>modificationDate.specified</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>modificationDate.in</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>modificationDate.notIn</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkSiretId.greaterThan</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkSiretId.lessThan</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkSiretId.greaterThanOrEqual</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkSiretId.lessThanOrEqual</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkSiretId.equals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkSiretId.notEquals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkSiretId.specified</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkSiretId.in</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkSiretId.notIn</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkAddressLineId.greaterThan</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkAddressLineId.lessThan</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkAddressLineId.greaterThanOrEqual</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkAddressLineId.lessThanOrEqual</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkAddressLineId.equals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkAddressLineId.notEquals</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkAddressLineId.specified</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkAddressLineId.in</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>pkAddressLineId.notIn</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>distinct</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="Sirens 1" id="0bff12ce-f6e9-4f17-a784-e2f21b800afe" method="GET"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirens">sir:Fault</con:element></con:representation><con:request name="Request 1" id="9e49009b-d8d3-4506-b726-c218ab725647" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="size" value="20"/>
  <con:entry key="id.lessThan" value="1780"/>
  <con:entry key="page" value="0"/>
</con:parameters><con:parameterOrder><con:entry>id.lessThan</con:entry><con:entry>page</con:entry><con:entry>size</con:entry><con:entry>id.greaterThan</con:entry><con:entry>id.greaterThanOrEqual </con:entry><con:entry>id.lessThanOrEqual</con:entry><con:entry>id.equals</con:entry><con:entry>id.notEquals</con:entry><con:entry>id.specified</con:entry><con:entry>id.in</con:entry><con:entry>id.notIn</con:entry><con:entry>sirenNum.contains</con:entry><con:entry>sirenNum.doesNotContain</con:entry><con:entry>sirenNum.equals</con:entry><con:entry>sirenNum.notEquals</con:entry><con:entry>sirenNum.specified</con:entry><con:entry>sirenNum.in</con:entry><con:entry>sirenNum.notIn</con:entry><con:entry>companyName.contains</con:entry><con:entry>companyName.doesNotContain</con:entry><con:entry>companyName.equals</con:entry><con:entry>companyName.notEquals</con:entry><con:entry>companyName.specified</con:entry><con:entry>companyName.in</con:entry><con:entry>companyName.notIn</con:entry><con:entry>entityType.equals</con:entry><con:entry>entityType.notEquals</con:entry><con:entry>entityType.specified</con:entry><con:entry>entityType.in</con:entry><con:entry>entityType.notIn</con:entry><con:entry>modificationDate.greaterThan</con:entry><con:entry>modificationDate.lessThan</con:entry><con:entry>modificationDate.greaterThanOrEqual</con:entry><con:entry>modificationDate.lessThanOrEqual</con:entry><con:entry>modificationDate.equals</con:entry><con:entry>modificationDate.notEquals</con:entry><con:entry>modificationDate.specified</con:entry><con:entry>modificationDate.in</con:entry><con:entry>modificationDate.notIn</con:entry><con:entry>pkSiretId.greaterThan</con:entry><con:entry>pkSiretId.lessThan</con:entry><con:entry>pkSiretId.greaterThanOrEqual</con:entry><con:entry>pkSiretId.lessThanOrEqual</con:entry><con:entry>pkSiretId.equals</con:entry><con:entry>pkSiretId.notEquals</con:entry><con:entry>pkSiretId.specified</con:entry><con:entry>pkSiretId.in</con:entry><con:entry>pkSiretId.notIn</con:entry><con:entry>pkAddressLineId.greaterThan</con:entry><con:entry>pkAddressLineId.lessThan</con:entry><con:entry>pkAddressLineId.greaterThanOrEqual</con:entry><con:entry>pkAddressLineId.lessThanOrEqual</con:entry><con:entry>pkAddressLineId.equals</con:entry><con:entry>pkAddressLineId.notEquals</con:entry><con:entry>pkAddressLineId.specified</con:entry><con:entry>pkAddressLineId.in</con:entry><con:entry>pkAddressLineId.notIn</con:entry><con:entry>distinct</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="f4c58078-e1f9-4cf1-8ba1-fdc2e9a0569c" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Delete-AdressLine" path="/api/address-lines/{IDAdress}" id="fc4b8b72-6deb-4d3b-b8aa-a4599e947c82"><con:settings/><con:parameters><con:parameter><con:name>IDAdress</con:name><con:value/><con:style>TEMPLATE</con:style><con:default/></con:parameter></con:parameters><con:method name="Delete-AdressLine" id="c2f92d60-2ba7-4730-bc71-64e938acbd0a" method="DELETE"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 405 500</con:status><con:params/><con:element xmlns:ns="http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines/3423">ns:Fault</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="Delete-AdressLine" id="7341c87a-8e60-4969-93b4-f515b88baa65" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines/3423</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="IDAdress" value="" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>IDAdress</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="bb5a8f2b-463d-4032-81e9-137182e94354" wadlVersion="http://wadl.dev.java.net/2009/02" name="http://legal-referential.chassagne-qa.generixgroup.com" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint></con:endpoints><con:resource name="Delete Siret/AdressLine" path="/api/sirets/{idSiret}" id="89f60aa3-b794-476c-a368-8ddc29131146"><con:settings/><con:parameters><con:parameter><con:name>idSiret</con:name><con:value/><con:style>TEMPLATE</con:style><con:default/></con:parameter></con:parameters><con:method name="Delete Siret/AdressLine" id="517fcf4f-d271-4f7c-86a4-54a7f41308ed" method="DELETE"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 405 500</con:status><con:params/><con:element xmlns:sir="http://legal-referential.chassagne-qa.generixgroup.com/api/sirets/">sir:Fault</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>204</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="Delete Siret/AdressLine" id="e10c7cd7-a9b8-4c92-aa75-c9d1e46c7879" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="idSiret" value="" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>idSiret</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="00d257bb-c499-4ef5-a19f-f3de0dbeb99f" wadlVersion="http://wadl.dev.java.net/2009/02" name="" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>- no endpoint set -</con:endpoint></con:endpoints><con:resource name="Recherche des ligne d'adressage dans l'annuaire" path="/api/v1/ligne-annuaire/recherche" id="e44a1a1e-a1ea-417c-80bb-a4faf4ef1c76"><con:settings/><con:parameters><con:parameter><con:name>format</con:name><con:value>JSON</con:value><con:style>QUERY</con:style><con:default>JSON</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>siren-destinataire</con:name><con:value>123</con:value><con:style>QUERY</con:style><con:default>123</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>est-rech-code-routage-stricte</con:name><con:value>true</con:value><con:style>QUERY</con:style><con:default>true</con:default><con:path xsi:nil="true"/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>raison-sociale-destinataire </con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>type-entite-destinataire</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>siret-destinataire</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>code-routage-destinataire</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>libelle-code-routage-destinataire</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>libelle-adresse</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>code-postal</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>libelle-commune</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter><con:parameter><con:name>libelle-pays</con:name><con:value/><con:style>QUERY</con:style><con:default/><con:description xsi:nil="true"/></con:parameter></con:parameters><con:method name="Recherche des ligne d'adressage dans l'annuaire" id="9295bf41-576b-4603-b69b-3f3b8cbd8f02" method="POST"><con:settings/><con:parameters/><con:representation type="REQUEST"><con:mediaType>application/json</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>401 400</con:status><con:params/><con:element xmlns:rec="https://legalref.dev.apps.generix.biz/api/v1/ligne-annuaire/recherche">rec:Fault</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/><con:element>Response</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="REQUEST"><con:mediaType>multipart/form-data</con:mediaType><con:params/></con:representation><con:representation type="FAULT"><con:mediaType>application/json</con:mediaType><con:status>500</con:status><con:params/><con:element xmlns:rec="https://legalref.dev.apps.generix.biz/api/v1/ligne-annuaire/recherche">rec:Fault</con:element></con:representation><con:request name="Recherche des ligne d'adressage dans l'annuaire" id="023ba6a5-a00f-401a-a4c2-43c44e7e5577" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 0,
  "offset": 0
}</con:request><con:originalUri>https://legalref.dev.apps.generix.biz/api/v1/ligne-annuaire/recherche</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry><con:entry>raison-sociale-destinataire </con:entry><con:entry>type-entite-destinataire</con:entry><con:entry>siret-destinataire</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>libelle-code-routage-destinataire</con:entry><con:entry>libelle-adresse</con:entry><con:entry>code-postal</con:entry><con:entry>libelle-commune</con:entry><con:entry>libelle-pays</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="973d62f3-45dc-4f6a-a7d2-55a0830c3952" wadlVersion="http://wadl.dev.java.net/2009/02" name="https://legalref.dev.apps.generix.biz" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint></con:endpoints><con:resource name="Create Fake Data" path="/api/v1/fakedata/create" id="ccd5e65a-0072-4ea6-bf8b-53488660e46b"><con:settings/><con:parameters/><con:method name="Create Fake Data" id="46ad6caf-82eb-4686-be70-a357e00789fe" method="GET"><con:settings/><con:parameters/><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="Create Fake Data" id="e0c2b936-fb9a-4240-90b6-5b9e81943b79" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request/><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="f3a98527-ae1d-4def-ab7e-bf7c96c1cb37" wadlVersion="http://wadl.dev.java.net/2009/02" name="https://legalref.dev.apps.generix.biz" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint></con:endpoints><con:resource name="Delete Fake Data" path="/api/v1/fakedata/delete" id="17973a4d-0eec-4e2e-ac53-b1537a0ba6e2"><con:settings/><con:parameters/><con:method name="Delete Fake Data" id="958bec7d-5792-47ab-8e54-20052632dd34" method="GET"><con:settings/><con:parameters/><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType>application/json</con:mediaType><con:status>200</con:status><con:params/></con:representation><con:request name="Delete Fake Data" id="ed0c4d94-1a59-42d1-9483-93730cfda6b9" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request/><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:request></con:method></con:resource></con:interface><con:interface xsi:type="con:RestService" id="bca02aa7-1ba8-4716-b793-2d2f26bcd182" wadlVersion="http://wadl.dev.java.net/2009/02" name="https://legalref.dev.apps.generix.biz" type="rest" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart=""/><con:endpoints><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint></con:endpoints><con:resource name="Get Adress Line By ID" path="/api/v1/ligne-annuaire/{ID}" id="93527f74-0501-49bd-a545-e78265ba0963"><con:settings/><con:parameters><con:parameter><con:name>ID</con:name><con:style>TEMPLATE</con:style></con:parameter></con:parameters><con:method name="301003 1" id="b1b701ce-1edb-467c-9e9e-aeb947152a0e" method="GET"><con:settings/><con:parameters/><con:representation type="FAULT"><con:mediaType>application/problem+json</con:mediaType><con:status>404 500</con:status><con:params/><con:element xmlns:ns="https://legalref.dev.apps.generix.biz/api/v1/ligne-annuaire/301003">ns:Fault</con:element></con:representation><con:representation type="FAULT"><con:mediaType xsi:nil="true"/><con:status>401</con:status><con:params/><con:element>data</con:element></con:representation><con:representation type="RESPONSE"><con:mediaType xsi:nil="true"/><con:status>0</con:status><con:params/><con:element>data</con:element></con:representation><con:request name="Request 1" id="e4a069a6-7d7d-46db-a619-7e47424d9cf1" mediaType="application/json"><con:settings/><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request/><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="ID" value="301003" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>ID</con:entry></con:parameterOrder></con:request></con:method></con:resource></con:interface><con:testSuite id="da8419fd-c676-4220-b47a-062b464e04d1" name="SearchByFakeData"><con:settings/><con:runType>SEQUENTIAL</con:runType><con:testCase id="bbb0c013-796c-4bf1-80ea-ead881fbbe07" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="SC1:Siren-->Adress Line" searchProperties="true"><con:description>
</con:description><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="b745a079-1f06-4a1e-a7fb-6f5506948abd"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="b64698e3-7974-49b9-9108-1e1b9c172b8d"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="6378e619-eb15-46aa-bbe5-f78d055bf606"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="groovy" name="Loop TestCase" id="2ed391cd-cf44-44df-b6ef-885300814bcd"><con:settings/><con:config><script>import groovy.json.JsonSlurper
import groovy.util.logging.Log

// Initialize lists to store IDs
def idList = []
def idList1 = []

for (int i = 1; i &lt;= 3; i++) {
    def sirenNum = "SNum1${i}"
    def companyName = "CNum1${i}"
    def addressLineCode = "ADL1${i}"

    // Construct the JSON with the current loop values
    def requestJson = """
{
    "sirenNum": "${sirenNum}",
    "companyName": "${companyName}",
    "entityType": "PUBLIQUE",
    "modificationDate": "2023-11-10T09:38:34.652Z"
}
"""

    // Set the constructed JSON as the request body
    testRunner.testCase.testSteps["CreateSiren"].setPropertyValue("Request", requestJson)

    // Execute the test step
    testRunner.runTestStepByName("CreateSiren")

    // Extract the ID from the Siren response and store it in a variable
    def responseSiren = testRunner.testCase.testSteps["CreateSiren"].getPropertyValue("Response")
    def jsonResponseSiren = new groovy.json.JsonSlurper().parseText(responseSiren)
    def idSiren = jsonResponseSiren.id

    // Extract the sirenNum from the CreateSiren response
    def responseSiren3 = testRunner.testCase.testSteps["CreateSiren"].getPropertyValue("Response")
    def jsonResponseSiren3 = new groovy.json.JsonSlurper().parseText(responseSiren3)
    def sirenNumFromResponse3 = jsonResponseSiren3.sirenNum

    // Add the ID to the list
    idList.add(idSiren)

    // Log the ID of the response
    //log.info("ID de la réponse de CreateSiren ${i} : " + idSiren)

    // Extract the ID from the response and store it in a variable
    def response = testRunner.testCase.testSteps["CreateSiren"].getPropertyValue("Response")
    def requestJson2 = """
{
    "addressLineCode": "${addressLineCode}",
    "applicationDate": "2023-11-10T08:33:52.579Z",
    "codeTypeModification": "A",
    "modificationId": "string",
    "entityType": "PUBLIQUE",
    "invoicingLineStatus": "ACTIF",
    "platformType": "PPF",
    "platformReceptionRegNumber": 0,
    "pdpSocialRaison": "string",
    "pdpCommercialName": "string",
    "platformReceptionDataContact": "string",
    "platformPeriodStart": "2023-11-10T08:33:52.579Z",
    "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
    "platformReceptionState": "ACTIF",
    "managementLegalEngagement": true,
    "managementService": true,
    "managementServiceOrLegal": true,
    "moa": true,
    "moaOnly": true,
    "managementStatePayment": true,
    "modificationDate": "2023-11-10T08:33:52.579Z",
    "fkAddressLineSiren": ${response}
}
"""
    testRunner.testCase.testSteps["CreateAdressLINE"].setPropertyValue("Request", requestJson2)
    log.info("Avant création ${i}")
    testRunner.runTestStepByName("CreateAdressLINE")
    log.info("Après création ${i}")

    // Log the response of CreateAdressLINE
    def response1 = testRunner.testCase.testSteps["CreateAdressLINE"].getPropertyValue("Response")
    // log.info("Réponse de CreateAdressLINE ${i} : " + response1)

    // Extract the ID from the Create AdressLine response and store it in a variable
    def jsonResponseObject = new groovy.json.JsonSlurper().parseText(response1)
    def idAdress = ""
    if (response1 &amp;&amp; jsonResponseObject.id) {
        idAdress = jsonResponseObject.id
        // log.info("idAdress ${i} : " + idAdress)
    } else {
        log.warn("Property 'id' not found in the JSON response.")
    }

    log.info("SirenDestinaire value: ${sirenNumFromResponse3}")
    try {

        // Set the constructed JSON as the request body for SearchAdressLineBySirenNum
        testRunner.testCase.testSteps["SearchAdressLineBySirenNum"].setPropertyValue("siren-destinataire", sirenNumFromResponse3.toString())

        // Execute the SearchAdressLineBySirenNum test step
        testRunner.runTestStepByName("SearchAdressLineBySirenNum")
        def responseDeleteAdress = testRunner.testCase.testSteps["SearchAdressLineBySirenNum"].getPropertyValue("Response")
    } catch (Exception e) {
        log.error("An error occurred during SearchAdressLineBySirenNum: " + e.getMessage())
        e.printStackTrace()
    }
    // Use the ID to call the Delete Of AdressLine API
    try {
        testRunner.testCase.testSteps["Delete-AdressLine"].setPropertyValue("IDAdress", idAdress.toString())
        testRunner.runTestStepByName("Delete-AdressLine")
        def responseDeleteAdress = testRunner.testCase.testSteps["Delete-AdressLine"].getPropertyValue("Response")
        // log.info("Réponse de Delete-AdressLine ${i} : " + responseDeleteAdress)
    } catch (Exception e) {
        log.error("An error occurred during Delete-AdressLine: " + e.getMessage())
        e.printStackTrace()
    }

// Use the ID to call the Delete Siren API
try {
    // Set the ID as a property for the DeleteSiren test step
    testRunner.testCase.testSteps["DeleteSiren"].setPropertyValue("id", idSiren.toString())

    // Execute the DeleteSiren test step
    testRunner.runTestStepByName("DeleteSiren")

    // Log the response of DeleteSiren
    def responseDeleteSiren = testRunner.testCase.testSteps["DeleteSiren"].getPropertyValue("Response")
    // log.info("Réponse de DeleteSiren ${i} : " + responseDeleteSiren)
} catch (Exception e) {
    log.error("An error occurred during DeleteSiren: " + e.getMessage())
    e.printStackTrace()
}




}
</script></con:config></con:testStep><con:testStep type="restrequest" name="CreateSiren" id="b735c7a2-7a97-4bc0-8582-0ba70b245061"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens" methodName="Sirens 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateSiren" id="74e36145-da5c-437e-9b4f-264a72a5b692" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>
{
    "sirenNum": "SNum11",
    "companyName": "CNum11",
    "entityType": "PUBLIQUE",
    "modificationDate": "2023-11-10T09:38:34.652Z"
}
</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="22159bf5-dc21-4f3a-ac94-8e20f4ce81d8" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="CreateAdressLINE" id="ddc0b190-c230-4f28-8043-1e4b63208847"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/address-lines" methodName="Address-lines 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateAdressLINE" id="37f76b19-0068-4927-93cf-4136d3943ac6" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>
{
    "addressLineCode": "ADL13",
    "applicationDate": "2023-11-10T08:33:52.579Z",
    "codeTypeModification": "A",
    "modificationId": "string",
    "entityType": "PUBLIQUE",
    "invoicingLineStatus": "ACTIF",
    "platformType": "PPF",
    "platformReceptionRegNumber": 0,
    "pdpSocialRaison": "string",
    "pdpCommercialName": "string",
    "platformReceptionDataContact": "string",
    "platformPeriodStart": "2023-11-10T08:33:52.579Z",
    "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
    "platformReceptionState": "ACTIF",
    "managementLegalEngagement": true,
    "managementService": true,
    "managementServiceOrLegal": true,
    "moa": true,
    "moaOnly": true,
    "managementStatePayment": true,
    "modificationDate": "2023-11-10T08:33:52.579Z",
    "fkAddressLineSiren": {"id":24522594,"sirenNum":"SNum13","companyName":"CNum13","entityType":"PUBLIQUE"}
}
</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="8b27f5d4-8307-41b0-a1de-dcfe64f2ddd3" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="SearchAdressLineBySirenNum" id="eae61d65-5f5e-4f1b-a98a-de8523d7cbc5"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SearchAdressLineBySirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>
{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="SNum13"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="Delete-AdressLine" id="d7b1b95d-aa15-4566-9e1f-e1f6d6be5883"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/address-lines/{IDAdress}" methodName="Delete-AdressLine" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="Delete-AdressLine" id="7341c87a-8e60-4969-93b4-f515b88baa65" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines/3423</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="470b3cd1-26ac-4557-9bb8-6fb61922c369" name="Valid HTTP Status Codes"><con:configuration><codes>204
</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="IDAdress" value="24524593" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>IDAdress</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="DeleteSiren" id="6b0dbd05-cd45-43e1-abe8-27dda73ef213"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens/{id}" methodName="DeleteSiren" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="DeleteSiren" id="8fba604a-a061-4fea-95c7-af7cba3db176" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Tenant" value="${#Project#Tenant}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/1224</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="6c02fe4b-621c-49ad-99db-8e915776c7b2" name="Valid HTTP Status Codes"><con:configuration><codes>204</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="24522594" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry><con:entry/></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="2872d502-53e0-4f31-bca7-43efeb096380" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>100</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>5</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type><con:config><testDelay>5</testDelay><randomFactor>0.5</randomFactor></con:config></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZoHiF4exYM_o9lODWat7-6rQ75iDJ862szRoXkXTuzq0UBybYn8LRGFGR97PT1kKNqQKnoH7AxGddtOZmemZmvLkv4auOd-9MLC2YyMuPhV1T0zSiLcg7Jm_hej7k87kfdJC2sr1xhzIvxtIQx1pmXzdoyTEKqK3Yyn96FZl0MSbWCm9GEpH8WV855HnGAoq1R3mSA3Z0hB_scUtyKKOZGf2Ykxl61eJrUg0N1PigLSdZ3tFHE3eVLNi1QHIBOS_Hs9oMckIO5dl5H9u0m-L4tDkWBQ5GQGrCj09Xg6o0rXW11WIQbPIbKc4ZN0wdk95GnDINc5I18yV_kseGUnMIg</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZoHiF4exYM_o9lODWat7-6rQ75iDJ862szRoXkXTuzq0UBybYn8LRGFGR97PT1kKNqQKnoH7AxGddtOZmemZmvLkv4auOd-9MLC2YyMuPhV1T0zSiLcg7Jm_hej7k87kfdJC2sr1xhzIvxtIQx1pmXzdoyTEKqK3Yyn96FZl0MSbWCm9GEpH8WV855HnGAoq1R3mSA3Z0hB_scUtyKKOZGf2Ykxl61eJrUg0N1PigLSdZ3tFHE3eVLNi1QHIBOS_Hs9oMckIO5dl5H9u0m-L4tDkWBQ5GQGrCj09Xg6o0rXW11WIQbPIbKc4ZN0wdk95GnDINc5I18yV_kseGUnMIg</con:value></con:property><con:property><con:name>SirenResponse</con:name><con:value>{"id":2835,"sirenNum":"SN10","companyName":"CN10","entityType":"PUBLIQUE","modificationDate":"2023-11-10T09:38:34.652Z"}</con:value></con:property><con:property><con:name>SirenData</con:name><con:value>SN3</con:value></con:property><con:property><con:name>ID</con:name><con:value>3360</con:value></con:property><con:property><con:name>IDAdress</con:name><con:value/></con:property><con:property><con:name>siren-destinataire</con:name><con:value/></con:property><con:property><con:name>idSiren</con:name><con:value/></con:property></con:properties></con:testCase><con:testCase id="aad1a3af-7bd0-45da-9704-50d5c8431ede" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="SC2: 2 Siret-->2 Adress Lines" searchProperties="true"><con:description>
</con:description><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="9c801760-2c8e-4877-b520-8f5a470a8405"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="aa620e6f-2de9-48a5-b1dc-3913289d6fa4"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="1cefa09c-927e-40f2-bad9-6bc36c0aa874"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="groovy" name="Loop TestCase" id="877355f5-2cde-4988-b87d-54591f6b3e0e" disabled="true"><con:settings/><con:config><script>import groovy.json.JsonSlurper
import groovy.util.logging.Log

// Initialize lists to store IDs
def idList = []
def idList1 = []

for (int i = 1; i &lt;= 10; i++) {
    def sirenNum = "SNum1${i}"
    def companyName = "CNum1${i}"
    def addressLineCode = "ADL1${i}"

    // Construct the JSON with the current loop values
    def requestJson = """
{
    "sirenNum": "${sirenNum}",
    "companyName": "${companyName}",
    "entityType": "PUBLIQUE",
    "modificationDate": "2023-11-10T09:38:34.652Z"
}
"""

    // Set the constructed JSON as the request body
    testRunner.testCase.testSteps["CreateSiren"].setPropertyValue("Request", requestJson)

    // Execute the test step
    testRunner.runTestStepByName("CreateSiren")

    // Extract the ID from the Siren response and store it in a variable
    def responseSiren = testRunner.testCase.testSteps["CreateSiren"].getPropertyValue("Response")
    def jsonResponseSiren = new groovy.json.JsonSlurper().parseText(responseSiren)
    def idSiren = jsonResponseSiren.id

    // Extract the sirenNum from the CreateSiren response
    def responseSiren3 = testRunner.testCase.testSteps["CreateSiren"].getPropertyValue("Response")
    def jsonResponseSiren3 = new groovy.json.JsonSlurper().parseText(responseSiren3)
    def sirenNumFromResponse3 = jsonResponseSiren3.sirenNum

    // Add the ID to the list
    idList.add(idSiren)

    // Log the ID of the response
    //log.info("ID de la réponse de CreateSiren ${i} : " + idSiren)

    // Extract the ID from the response and store it in a variable
    def response = testRunner.testCase.testSteps["CreateSiren"].getPropertyValue("Response")
    def requestJson2 = """
{
    "addressLineCode": "${addressLineCode}",
    "applicationDate": "2023-11-10T08:33:52.579Z",
    "codeTypeModification": "A",
    "modificationId": "string",
    "entityType": "PUBLIQUE",
    "invoicingLineStatus": "ACTIF",
    "platformType": "PPF",
    "platformReceptionRegNumber": 0,
    "pdpSocialRaison": "string",
    "pdpCommercialName": "string",
    "platformReceptionDataContact": "string",
    "platformPeriodStart": "2023-11-10T08:33:52.579Z",
    "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
    "platformReceptionState": "ACTIF",
    "managementLegalEngagement": true,
    "managementService": true,
    "managementServiceOrLegal": true,
    "moa": true,
    "moaOnly": true,
    "managementStatePayment": true,
    "modificationDate": "2023-11-10T08:33:52.579Z",
    "fkAddressLineSiren": ${response}
}
"""
    testRunner.testCase.testSteps["CreateAdressLINE"].setPropertyValue("Request", requestJson2)
    log.info("Avant création ${i}")
    testRunner.runTestStepByName("CreateAdressLINE")
    log.info("Après création ${i}")

    // Log the response of CreateAdressLINE
    def response1 = testRunner.testCase.testSteps["CreateAdressLINE"].getPropertyValue("Response")
    // log.info("Réponse de CreateAdressLINE ${i} : " + response1)

    // Extract the ID from the Create AdressLine response and store it in a variable
    def jsonResponseObject = new groovy.json.JsonSlurper().parseText(response1)
    def idAdress = ""
    if (response1 &amp;&amp; jsonResponseObject.id) {
        idAdress = jsonResponseObject.id
        // log.info("idAdress ${i} : " + idAdress)
    } else {
        log.warn("Property 'id' not found in the JSON response.")
    }

    log.info("SirenDestinaire value: ${sirenNumFromResponse3}")
    try {

        // Set the constructed JSON as the request body for SearchAdressLineBySirenNum
        testRunner.testCase.testSteps["SearchAdressLineBySirenNum"].setPropertyValue("siren-destinataire", sirenNumFromResponse3.toString())

        // Execute the SearchAdressLineBySirenNum test step
        testRunner.runTestStepByName("SearchAdressLineBySirenNum")
        def responseDeleteAdress = testRunner.testCase.testSteps["SearchAdressLineBySirenNum"].getPropertyValue("Response")
    } catch (Exception e) {
        log.error("An error occurred during SearchAdressLineBySirenNum: " + e.getMessage())
        e.printStackTrace()
    }
    // Use the ID to call the Delete API
    try {
        testRunner.testCase.testSteps["Delete-AdressLine"].setPropertyValue("IDAdress", idAdress.toString())
        testRunner.runTestStepByName("Delete-AdressLine")
        def responseDeleteAdress = testRunner.testCase.testSteps["Delete-AdressLine"].getPropertyValue("Response")
        // log.info("Réponse de Delete-AdressLine ${i} : " + responseDeleteAdress)
    } catch (Exception e) {
        log.error("An error occurred during Delete-AdressLine: " + e.getMessage())
        e.printStackTrace()
    }

}
</script></con:config></con:testStep><con:testStep type="groovy" name="Groovy Script" id="b5b825ae-bd38-4fd5-8569-924225347288"><con:settings/><con:config><script>import groovy.json.JsonSlurper

// Initialize lists to store IDs
def idList = []

for (int i = 1; i &lt;= 3; i++) {
    def sirenNum = "SN${i}"
    def siretValue1 = "ST${i+1}"
    def siretValue2 = "ST${i+2}"
    def adressLineValue1 = "ADL${i+1}"
    def adressLineValue2 = "ADL${i+2}"

    // Create Siren
    def requestJsonSiren = """
{
    "sirenNum": "${sirenNum}",
    "companyName": "Company${i}",
    "entityType": "PUBLIQUE",
    "modificationDate": "2023-11-10T09:38:34.652Z"
}
"""
    testRunner.testCase.testSteps["CreateSiren"].setPropertyValue("Request", requestJsonSiren)
    testRunner.runTestStepByName("CreateSiren")

    // Extract the ID from the Siren response and store it in a variable
    def responseSiren = testRunner.testCase.testSteps["CreateSiren"].getPropertyValue("Response")
    def jsonResponseSiren = new groovy.json.JsonSlurper().parseText(responseSiren)
    def idSiren = jsonResponseSiren.id

    // Add the ID to the list
    idList.add(idSiren)

    // Create Siret 1
    def requestJsonSiret1 = """
{
    "siretNum": "${siretValue1}",
    "principalEstablishment": "P",
    "label": "string",
    "addressLigne1": "string",
    "addressLigne2": "string",
    "addressLigne3": "string",
    "town": "string",
    "postalCode": 0,
    "country": "string",
    "modificationDate": "2023-11-10T09:09:07.132Z",
    "fkSiretSiren": ${responseSiren}
}
"""
    testRunner.testCase.testSteps["CreateSiret"].setPropertyValue("Request", requestJsonSiret1)
    testRunner.runTestStepByName("CreateSiret")
    def responseSiret1 = testRunner.testCase.testSteps["CreateSiret"].getPropertyValue("Response")
    def jsonResponseSiret = new groovy.json.JsonSlurper().parseText(responseSiret1)
    def idSiret = jsonResponseSiret.id




    // Create Siret 2
    def requestJsonSiret2 = """
{
    "siretNum": "${siretValue2}",
    "principalEstablishment": "P",
    "label": "string",
    "addressLigne1": "string",
    "addressLigne2": "string",
    "addressLigne3": "string",
    "town": "string",
    "postalCode": 0,
    "country": "string",
    "modificationDate": "2023-11-10T09:09:07.132Z",
    "fkSiretSiren": ${responseSiren}
}
"""
    testRunner.testCase.testSteps["CreateSiret"].setPropertyValue("Request", requestJsonSiret2)
    testRunner.runTestStepByName("CreateSiret")
    def responseSiret2 = testRunner.testCase.testSteps["CreateSiret"].getPropertyValue("Response")

    // Create AddressLine 1
    def requestJsonAdressLine1 = """
{
    "addressLineCode": "${adressLineValue1}",
    "applicationDate": "2023-11-10T08:33:52.579Z",
    "codeTypeModification": "A",
    "modificationId": "string",
    "entityType": "PUBLIQUE",
    "invoicingLineStatus": "ACTIF",
    "platformType": "PPF",
    "platformReceptionRegNumber": 0,
    "pdpSocialRaison": "string",
    "pdpCommercialName": "string",
    "platformReceptionDataContact": "string",
    "platformPeriodStart": "2023-11-10T08:33:52.579Z",
    "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
    "platformReceptionState": "ACTIF",
    "managementLegalEngagement": true,
    "managementService": true,
    "managementServiceOrLegal": true,
    "moa": true,
    "moaOnly": true,
    "managementStatePayment": true,
    "modificationDate": "2023-11-10T08:33:52.579Z",
    "fkAddressLineSiret": ${responseSiret1}
}
"""
    testRunner.testCase.testSteps["CreateAdressLINE"].setPropertyValue("Request", requestJsonAdressLine1)
    testRunner.runTestStepByName("CreateAdressLINE")

    // Create AddressLine 2
    def requestJsonAdressLine2 = """
{
    "addressLineCode": "${adressLineValue2}",
    "applicationDate": "2023-11-10T08:33:52.579Z",
    "codeTypeModification": "A",
    "modificationId": "string",
    "entityType": "PUBLIQUE",
    "invoicingLineStatus": "ACTIF",
    "platformType": "PPF",
    "platformReceptionRegNumber": 0,
    "pdpSocialRaison": "string",
    "pdpCommercialName": "string",
    "platformReceptionDataContact": "string",
    "platformPeriodStart": "2023-11-10T08:33:52.579Z",
    "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
    "platformReceptionState": "ACTIF",
    "managementLegalEngagement": true,
    "managementService": true,
    "managementServiceOrLegal": true,
    "moa": true,
    "moaOnly": true,
    "managementStatePayment": true,
    "modificationDate": "2023-11-10T08:33:52.579Z",
    "fkAddressLineSiret": ${responseSiret2}
}
"""
    testRunner.testCase.testSteps["CreateAdressLINE"].setPropertyValue("Request", requestJsonAdressLine2)
    testRunner.runTestStepByName("CreateAdressLINE")

    
 try {
   // Set the constructed JSON as the request body for SearchAdressLineBySirenNum
        testRunner.testCase.testSteps["SearchAdressLineBySirenNum"].setPropertyValue("siren-destinataire", sirenNum.toString())

        // Execute the SearchAdressLineBySirenNum test step
        testRunner.runTestStepByName("SearchAdressLineBySirenNum")
        def responseDeleteAdress = testRunner.testCase.testSteps["SearchAdressLineBySirenNum"].getPropertyValue("Response")
    } catch (Exception e) {
        log.error("An error occurred during SearchAdressLineBySirenNum: " + e.getMessage())
        e.printStackTrace()
    }


   // Log the response of CreateAdressLINE
    def response1 = testRunner.testCase.testSteps["CreateAdressLINE"].getPropertyValue("Response")
    // log.info("Réponse de CreateAdressLINE ${i} : " + response1)

    // Extract the ID from the Create AdressLine response and store it in a variable
    def jsonResponseObject = new groovy.json.JsonSlurper().parseText(response1)
    def idAdress = ""
    if (response1 &amp;&amp; jsonResponseObject.id) {
        idAdress = jsonResponseObject.id
        // log.info("idAdress ${i} : " + idAdress)
    } else {
        log.warn("Property 'id' not found in the JSON response.")
    }



// Use the ID to call the Delete Of AdressLine API
    try {
        testRunner.testCase.testSteps["Delete-AdressLine"].setPropertyValue("IDAdress", idAdress.toString())
        testRunner.runTestStepByName("Delete-AdressLine")
        def responseDeleteAdress = testRunner.testCase.testSteps["Delete-AdressLine"].getPropertyValue("Response")
        // log.info("Réponse de Delete-AdressLine ${i} : " + responseDeleteAdress)
    } catch (Exception e) {
        log.error("An error occurred during Delete-AdressLine: " + e.getMessage())
        e.printStackTrace()
    }



// Utiliser l'ID du Siret pour appeler l'API Delete-Siret
try {
    testRunner.testCase.testSteps["Delete-Siret"].setPropertyValue("idSiret", idSiret.toString())
    testRunner.runTestStepByName("Delete-Siret")
    def responseDeleteSiret = testRunner.testCase.testSteps["Delete-Siret"].getPropertyValue("Response")
    // log.info("Réponse de Delete-Siret ${i} : " + responseDeleteSiret)
} catch (Exception e) {
    log.error("An error occurred during Delete-Siret: " + e.getMessage())
    e.printStackTrace()
}



// Use the ID to call the Delete Siren API
try {
    // Set the ID as a property for the DeleteSiren test step
    testRunner.testCase.testSteps["DeleteSiren"].setPropertyValue("id", idSiren.toString())

    // Execute the DeleteSiren test step
    testRunner.runTestStepByName("DeleteSiren")

    // Log the response of DeleteSiren
    def responseDeleteSiren = testRunner.testCase.testSteps["DeleteSiren"].getPropertyValue("Response")
    // log.info("Réponse de DeleteSiren ${i} : " + responseDeleteSiren)
} catch (Exception e) {
    log.error("An error occurred during DeleteSiren: " + e.getMessage())
    e.printStackTrace()
}



    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="CreateSiren" id="424155fe-2caf-495b-8e55-db0e156abd8e"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens" methodName="Sirens 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateSiren" id="74e36145-da5c-437e-9b4f-264a72a5b692" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>
{
    "sirenNum": "SN3",
    "companyName": "Company3",
    "entityType": "PUBLIQUE",
    "modificationDate": "2023-11-10T09:38:34.652Z"
}
</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="22159bf5-dc21-4f3a-ac94-8e20f4ce81d8" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="CreateSiret" id="f88b5d90-3b52-44ad-8c10-165d994ea064"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirets" methodName="Sirets 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateSiret" id="68b6b5d0-2534-43cd-9508-4614923504aa" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>
{
    "siretNum": "ST5",
    "principalEstablishment": "P",
    "label": "string",
    "addressLigne1": "string",
    "addressLigne2": "string",
    "addressLigne3": "string",
    "town": "string",
    "postalCode": 0,
    "country": "string",
    "modificationDate": "2023-11-10T09:09:07.132Z",
    "fkSiretSiren": {"id":6043,"sirenNum":"SN3","companyName":"Company3","entityType":"PUBLIQUE","modificationDate":"2023-11-10T09:38:34.652Z"}
}
</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirets</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="6c52b479-d33b-4039-a92b-e59b3e29936c" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="CreateAdressLINE" id="f44317a7-b3a0-4468-a325-a4cac4df10c9"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/address-lines" methodName="Address-lines 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateAdressLINE" id="37f76b19-0068-4927-93cf-4136d3943ac6" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>
{
    "addressLineCode": "ADL5",
    "applicationDate": "2023-11-10T08:33:52.579Z",
    "codeTypeModification": "A",
    "modificationId": "string",
    "entityType": "PUBLIQUE",
    "invoicingLineStatus": "ACTIF",
    "platformType": "PPF",
    "platformReceptionRegNumber": 0,
    "pdpSocialRaison": "string",
    "pdpCommercialName": "string",
    "platformReceptionDataContact": "string",
    "platformPeriodStart": "2023-11-10T08:33:52.579Z",
    "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
    "platformReceptionState": "ACTIF",
    "managementLegalEngagement": true,
    "managementService": true,
    "managementServiceOrLegal": true,
    "moa": true,
    "moaOnly": true,
    "managementStatePayment": true,
    "modificationDate": "2023-11-10T08:33:52.579Z",
    "fkAddressLineSiret": {"id":6161,"siretNum":"ST5","principalEstablishment":"P","label":"string","addressLigne1":"string","addressLigne2":"string","addressLigne3":"string","town":"string","postalCode":0,"country":"string","modificationDate":"2023-11-10T09:09:07.132Z","fkSiretSiren":{"id":6043,"sirenNum":null,"companyName":null,"entityType":null,"modificationDate":null}}
}
</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="8b27f5d4-8307-41b0-a1de-dcfe64f2ddd3" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="SearchAdressLineBySirenNum" id="c76b3d5c-70f9-4042-bff5-06aa63d6b12b"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SearchAdressLineBySirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>
{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="SN3"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="Delete-AdressLine" id="cba75ad5-487c-4bf6-b882-9aa99ea42ad3"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/address-lines/{IDAdress}" methodName="Delete-AdressLine" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="Delete-AdressLine" id="7341c87a-8e60-4969-93b4-f515b88baa65" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines/3423</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="470b3cd1-26ac-4557-9bb8-6fb61922c369" name="Valid HTTP Status Codes"><con:configuration><codes>204
</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="IDAdress" value="6205" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>IDAdress</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="Delete-Siret" id="56115e80-11d3-44a2-96b3-edbd93a7d72b"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirets/{idSiret}" methodName="Delete Siret/AdressLine" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="Delete-Siret" id="e10c7cd7-a9b8-4c92-aa75-c9d1e46c7879" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirets/</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="385975a6-fd6e-4478-9c74-e14f07c15c98" name="Valid HTTP Status Codes"><con:configuration><codes>204</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="idSiret" value="6160" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>idSiret</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="DeleteSiren" id="19bed724-21ef-4246-930c-729adf5181ec"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens/{id}" methodName="DeleteSiren" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="DeleteSiren" id="8fba604a-a061-4fea-95c7-af7cba3db176" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Tenant" value="${#Project#Tenant}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/1224</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="6c02fe4b-621c-49ad-99db-8e915776c7b2" name="Valid HTTP Status Codes"><con:configuration><codes>204</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="6043" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry><con:entry/></con:parameterOrder></con:restRequest></con:config></con:testStep><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OalZPv5zm2Ixmlv-tAz6NMJmojOZUCEAcD_QIVbUncNWaf7ZW1iiRwg_DPc1LNgj2UrltqhjVeNmqfZRqORPpxSTQUlUxUlUDID8xChn08oks5cM4FBuKhhOOtNREy2zCiFsi3cmmTZRU9yTwTFa8AjoXarBxd4MMppfYood9MthtkqyJGlDrjmNduPkJfsEPw0CFmL_VV3WTDfowz3sMcntzu3KHS6bBS5HqZEW3Tgw5Whpel9bcmOCHUh6BakkoKEmzBvWi8HQ4rUnUDOQJKlzJWYa-sfdHuuihnpzEc4xddt6t5rup9gQyIABbAon532dK6qmKczFLZE-cs6Svw</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OalZPv5zm2Ixmlv-tAz6NMJmojOZUCEAcD_QIVbUncNWaf7ZW1iiRwg_DPc1LNgj2UrltqhjVeNmqfZRqORPpxSTQUlUxUlUDID8xChn08oks5cM4FBuKhhOOtNREy2zCiFsi3cmmTZRU9yTwTFa8AjoXarBxd4MMppfYood9MthtkqyJGlDrjmNduPkJfsEPw0CFmL_VV3WTDfowz3sMcntzu3KHS6bBS5HqZEW3Tgw5Whpel9bcmOCHUh6BakkoKEmzBvWi8HQ4rUnUDOQJKlzJWYa-sfdHuuihnpzEc4xddt6t5rup9gQyIABbAon532dK6qmKczFLZE-cs6Svw</con:value></con:property><con:property><con:name>SirenResponse</con:name><con:value>{"id":2835,"sirenNum":"SN10","companyName":"CN10","entityType":"PUBLIQUE","modificationDate":"2023-11-10T09:38:34.652Z"}</con:value></con:property><con:property><con:name>SirenData</con:name><con:value>SN3</con:value></con:property><con:property><con:name>ID</con:name><con:value>3360</con:value></con:property><con:property><con:name>IDAdress</con:name><con:value/></con:property><con:property><con:name>siren-destinataire</con:name><con:value/></con:property><con:property><con:name>idSiret</con:name><con:value/></con:property></con:properties></con:testCase><con:testCase id="18266cc3-4fe0-4180-88c2-fad10c3f15a1" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="FakeData Siren-->Adress Line" searchProperties="true"><con:description>
</con:description><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="1a33d445-8ed0-480e-b896-b2987ac5d442"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="d29f6d1a-5e5f-42c4-9afd-317c9af5096a"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="1c162b93-3b76-444b-91c8-d6cac30998c8"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="Copy of Delete Fake Data" id="5126f00c-61bf-4dab-9dcf-f464e30b7261" disabled="true"><con:settings/><con:config service="https://legalref.dev.apps.generix.biz" resourcePath="/api/v1/fakedata/delete" methodName="Delete Fake Data" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="Copy of Delete Fake Data" id="ed0c4d94-1a59-42d1-9483-93730cfda6b9" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request/><con:originalUri>https://legalref.dev.apps.generix.biz/api/v1/fakedata/delete</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="0f08f6b4-c55d-4ae8-a173-01b4c51f235b" name="Valid HTTP Status Codes"><con:configuration><codes>200
</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="Create Fake Data" id="26163edc-531d-4b07-bd50-dd2126c2abc6"><con:settings/><con:config service="https://legalref.dev.apps.generix.biz" resourcePath="/api/v1/fakedata/create" methodName="Create Fake Data" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="Create Fake Data" id="e0c2b936-fb9a-4240-90b6-5b9e81943b79" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request/><con:originalUri>https://legalref.dev.apps.generix.biz/api/v1/fakedata/create</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="3e40778f-61dc-478f-89ab-4863b9f87360" name="Valid HTTP Status Codes"><con:configuration><codes>200
</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="Recherche des lignes d'adressage dans l'annuaire" id="0999cb40-025d-414e-ae0b-685d618d0480"><con:settings/><con:config service="" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="Recherche des ligne d'adressage dans l'annuaire" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="Recherche des lignes d'adressage dans l'annuaire" id="023ba6a5-a00f-401a-a4c2-43c44e7e5577" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit":20,
  "offset": 0
}</con:request><con:originalUri>https://legalref.dev.apps.generix.biz/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="d95899bd-4167-4ed5-a847-cc75357327f8" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="siren-destinataire" value="1" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry><con:entry>raison-sociale-destinataire </con:entry><con:entry>type-entite-destinataire</con:entry><con:entry>siret-destinataire</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>libelle-code-routage-destinataire</con:entry><con:entry>libelle-adresse</con:entry><con:entry>code-postal</con:entry><con:entry>libelle-commune</con:entry><con:entry>libelle-pays</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="Delete Fake Data" id="987ae256-48b8-4c76-ae6e-1f0faa9e7e9b" disabled="true"><con:settings/><con:config service="https://legalref.dev.apps.generix.biz" resourcePath="/api/v1/fakedata/delete" methodName="Delete Fake Data" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="Delete Fake Data" id="ed0c4d94-1a59-42d1-9483-93730cfda6b9" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request/><con:originalUri>https://legalref.dev.apps.generix.biz/api/v1/fakedata/delete</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="87d75654-052e-4466-bda7-78aad1c3aa43" name="Valid HTTP Status Codes"><con:configuration><codes>200

</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="Get Adress Line With ID" id="ec05a822-2098-4760-bfa3-77c29fdf34c7"><con:settings/><con:config service="https://legalref.dev.apps.generix.biz" resourcePath="/api/v1/ligne-annuaire/{ID}" methodName="301003 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="Get Adress Line With ID" id="e4a069a6-7d7d-46db-a619-7e47424d9cf1" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request/><con:originalUri>https://legalref.dev.apps.generix.biz/api/v1/ligne-annuaire/301003</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="60d72cc1-a52c-4594-a2a8-9ed08c1ade6f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="ID" value="AAAAAAAAAA1" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>ID</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="845ab9de-38a8-4022-b7be-e260b620b647" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>100</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>205</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type><con:config><testDelay>1</testDelay><randomFactor>0.5</randomFactor></con:config></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.N-WOTT0AHgdMFRhLI9WPeqdgltZxQD_5A3NtoVx2AAlzzS0fL-Dk1YvRhgoEeiaZIvYPDrCwRmWfVBsAuKagxrD4PQKfTSD9uJ040tIiMyLQJ7LUUJEWJUylR3kUy6m2dT84uoXSd2f0XonUJD_ZU7QC9jkq6vpnYVk5AJABV-7Fj71phMCh8QAxY87NYVFybRamMwcxtlRoPD5iwLNon5i5DAFlZXQsJxjnQmZZ-rErHuX02FP-zwoJ5ZObHFqiVVRRs6K36ebl_vbBvdjE54QbODXFZ7Iqm_CYyy7hdJxj1dXbgapm-m-I_ND3WrVWLXtYCvq6Bc9-AZtnFyufGw</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.N-WOTT0AHgdMFRhLI9WPeqdgltZxQD_5A3NtoVx2AAlzzS0fL-Dk1YvRhgoEeiaZIvYPDrCwRmWfVBsAuKagxrD4PQKfTSD9uJ040tIiMyLQJ7LUUJEWJUylR3kUy6m2dT84uoXSd2f0XonUJD_ZU7QC9jkq6vpnYVk5AJABV-7Fj71phMCh8QAxY87NYVFybRamMwcxtlRoPD5iwLNon5i5DAFlZXQsJxjnQmZZ-rErHuX02FP-zwoJ5ZObHFqiVVRRs6K36ebl_vbBvdjE54QbODXFZ7Iqm_CYyy7hdJxj1dXbgapm-m-I_ND3WrVWLXtYCvq6Bc9-AZtnFyufGw</con:value></con:property><con:property><con:name>SirenResponse</con:name><con:value>{"id":2835,"sirenNum":"SN10","companyName":"CN10","entityType":"PUBLIQUE","modificationDate":"2023-11-10T09:38:34.652Z"}</con:value></con:property><con:property><con:name>SirenData</con:name><con:value>SN3</con:value></con:property><con:property><con:name>ID</con:name><con:value>3360</con:value></con:property><con:property><con:name>IDAdress</con:name><con:value/></con:property><con:property><con:name>siren-destinataire</con:name><con:value/></con:property><con:property><con:name>idSiren</con:name><con:value/></con:property></con:properties></con:testCase><con:properties><con:property><con:name>api1Id</con:name><con:value>{"id":1538,"sirenNum":"2023333","companyName":"AAAA1","entityType":"PUBLIQUE","modificationDate":"2023-11-10T09:38:34.652Z"}</con:value></con:property></con:properties></con:testSuite><con:testSuite id="4017f487-d1b2-434c-9423-06e0363e6dfa" name="TestSuite"><con:settings/><con:runType>SEQUENTIAL</con:runType><con:testCase id="0b3e87fb-c908-4007-8856-67d999762836" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="SearchBySirenDestinataire" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="a2ab4cd1-1983-4b55-9434-fd76c0aab781"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="51e58001-5829-47e1-94a5-c92d763d94e3"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="e01b23ed-b463-47b1-8be3-be42a2027953"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="CreateSiren" id="251bb6ab-b4e8-4e9b-b9fb-d10cebcbef00"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens" methodName="Sirens 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateSiren" id="74e36145-da5c-437e-9b4f-264a72a5b692" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
 
  "sirenNum": "123456",
  "companyName": "AAAA1",
  "entityType": "PUBLIQUE",
  "modificationDate": "2023-11-10T09:38:34.652Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="22159bf5-dc21-4f3a-ac94-8e20f4ce81d8" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TransferSirenId" id="d6f41193-e2bc-4aa4-ac5e-454d8506fd66"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SirenID</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>CreateSiren</con:sourceStep><con:sourcePath/><con:targetType>SirenResponse</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SirenData</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>CreateSiren</con:sourceStep><con:sourcePath>$.sirenNum</con:sourcePath><con:targetType>SirenData</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="b413ad40-1e09-4ff0-8887-d6882bcbab48"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#SirenData}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="CreateAdressLINE" id="b1454361-e137-43d8-8ef5-5f190e03b298"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/address-lines" methodName="Address-lines 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateAdressLINE" id="37f76b19-0068-4927-93cf-4136d3943ac6" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "addressLineCode": "66666",
  "applicationDate": "2023-11-10T08:33:52.579Z",
  "codeTypeModification": "A",
  "modificationId": "string",
  "entityType": "PUBLIQUE",
  "invoicingLineStatus": "ACTIF",
  "platformType": "PPF",
  "platformReceptionRegNumber": 0,
  "pdpSocialRaison": "string",
  "pdpCommercialName": "string",
  "platformReceptionDataContact": "string",
  "platformPeriodStart": "2023-11-10T08:33:52.579Z",
  "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
  "platformReceptionState": "ACTIF",
  "managementLegalEngagement": true,
  "managementService": true,
  "managementServiceOrLegal": true,
  "moa": true,
  "moaOnly": true,
  "managementStatePayment": true,
  "modificationDate": "2023-11-10T08:33:52.579Z",
  "fkAddressLineSiren":${#TestCase#SirenResponse}
}
</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="8b27f5d4-8307-41b0-a1de-dcfe64f2ddd3" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="NullSiren" id="ad653edd-7d75-4d5a-9889-72635da93d7d"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="NullSiren" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>400</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="InvalidSirenNum" id="9a859b5b-008b-4bc1-8c55-c92ce42a899b" disabled="true"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="InvalidSirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
  &lt;con:entry key="accept" value="*/*"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Simple Contains" id="d12266c2-203d-4226-8325-b87778ed6aa1" name="Contains"><con:configuration><token>400</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="12346999999999995"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="REST Request" id="166d9593-b9f2-46da-a286-a1c80f10acc2"><con:settings/><con:config service="" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="Recherche des ligne d'adressage dans l'annuaire" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="REST Request" id="023ba6a5-a00f-401a-a4c2-43c44e7e5577" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>https://legalref.dev.apps.generix.biz</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit":10,
  "offset": 0
}</con:request><con:originalUri>https://legalref.dev.apps.generix.biz/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Simple Contains" id="835d5bf4-7eb4-4154-a2fb-c0a62f4e5d16" name="Contains"><con:configuration><token>200</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry><con:entry>raison-sociale-destinataire </con:entry><con:entry>type-entite-destinataire</con:entry><con:entry>siret-destinataire</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>libelle-code-routage-destinataire</con:entry><con:entry>libelle-adresse</con:entry><con:entry>code-postal</con:entry><con:entry>libelle-commune</con:entry><con:entry>libelle-pays</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iNoDqQOmufk-IzxzhLgSDLO-8K9skaY3t4OBMJRXTnDJ5GfxuBQwBOKjIdbKcuBfF8ipaPTIreoOPsXPXfuWSRrPU_mdqXOha8uSpvmgeijhJaKyd2r-szPaUhe_EDoEpUvYUwTfHY71vMMBNjNk_K1ATcxkHP-Po_d0ozqybb41HHQMJIFChbQMHKUNSjJdQpfsQvyxmoHsOSwmMMkw-jJein26-QmbY8AIpPScBOE1a8feuskPFzAbNpb5uvwj2VBjOm27BoBtwALjkvtaDT3kYAp2gMNjaOM9bp-WsYq6UeqX2ceo1tMeEiAKEdWI5JjOGazk_qXdiCPAOBGZ8g</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iNoDqQOmufk-IzxzhLgSDLO-8K9skaY3t4OBMJRXTnDJ5GfxuBQwBOKjIdbKcuBfF8ipaPTIreoOPsXPXfuWSRrPU_mdqXOha8uSpvmgeijhJaKyd2r-szPaUhe_EDoEpUvYUwTfHY71vMMBNjNk_K1ATcxkHP-Po_d0ozqybb41HHQMJIFChbQMHKUNSjJdQpfsQvyxmoHsOSwmMMkw-jJein26-QmbY8AIpPScBOE1a8feuskPFzAbNpb5uvwj2VBjOm27BoBtwALjkvtaDT3kYAp2gMNjaOM9bp-WsYq6UeqX2ceo1tMeEiAKEdWI5JjOGazk_qXdiCPAOBGZ8g</con:value></con:property><con:property><con:name>SirenResponse</con:name><con:value>{"id":24522639,"sirenNum":"123456","companyName":"AAAA1","entityType":"PUBLIQUE"}</con:value></con:property><con:property><con:name>SirenData</con:name><con:value>123456</con:value></con:property></con:properties></con:testCase><con:testCase id="5f2f8e2a-b620-4270-b6c4-4523b7eb73c8" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="SearchBySiretDestinataire" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="be48a873-6d06-41c4-943d-2135201a9500"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="64eaf698-34f4-4a56-ba6d-124170a02069"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="26456959-8f72-4636-bb72-44283a2276af"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="CreateSiren" id="4c983393-5cc8-423d-842b-6d2ca68ad46d"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens" methodName="Sirens 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateSiren" id="74e36145-da5c-437e-9b4f-264a72a5b692" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{

  "sirenNum": "2023",
  "companyName": "Company1",
  "entityType": "PUBLIQUE",
  "modificationDate": "2023-11-10T08:21:42.875Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="22159bf5-dc21-4f3a-ac94-8e20f4ce81d8" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TransferSirenId" id="38b5bffd-da60-4e6b-a8cb-36363d0a2fd6"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SirenID</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>CreateSiren</con:sourceStep><con:targetType>SirenResponse</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>XPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="CreateSiret" id="969e5325-aa70-4315-88d8-e3127453c5a1"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirets" methodName="Sirets 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateSiret" id="68b6b5d0-2534-43cd-9508-4614923504aa" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  
  "siretNum": "1236",
  "principalEstablishment": "P",
  "label": "string",
  "addressLigne1": "string",
  "addressLigne2": "string",
  "addressLigne3": "string",
  "town": "string",
  "postalCode": 0,
  "country": "string",
  "modificationDate": "2023-11-10T09:09:07.132Z",
  "fkSiretSiren": ${#TestCase#SirenResponse}
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirets</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="36e5c496-44de-4198-bf6c-dec0b6faa578" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TransferSiretId" id="3c5b11cf-32d7-41e0-ae51-35e55947d1b2"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SiretID</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>CreateSiret</con:sourceStep><con:targetType>SiretResponse</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SiretData</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>CreateSiret</con:sourceStep><con:sourcePath>$.siretNum</con:sourcePath><con:targetType>SiretData</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="CreateAdressLine" id="e7e31679-6082-4430-8dd8-cdaf0ab3f7fd"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/address-lines" methodName="Address-lines 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateAdressLine" id="37f76b19-0068-4927-93cf-4136d3943ac6" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "addressLineCode": "5556",
  "applicationDate": "2023-11-10T08:33:52.579Z",
  "codeTypeModification": "A",
  "modificationId": "string",
  "entityType": "PUBLIQUE",
  "invoicingLineStatus": "ACTIF",
  "platformType": "PPF",
  "platformReceptionRegNumber": 0,
  "pdpSocialRaison": "string",
  "pdpCommercialName": "string",
  "platformReceptionDataContact": "string",
  "platformPeriodStart": "2023-11-10T08:33:52.579Z",
  "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
  "platformReceptionState": "ACTIF",
  "managementLegalEngagement": true,
  "managementService": true,
  "managementServiceOrLegal": true,
  "moa": true,
  "moaOnly": true,
  "managementStatePayment": true,
  "modificationDate": "2023-11-10T08:33:52.579Z",
   "fkAddressLineSiret":${#TestCase#SiretResponse}
}
</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="adbc7b07-f2d2-463f-9277-0e9cb6633309" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="SiretNum" id="ddc9fe5f-2d2b-49db-99f9-7f1e0dcaf23f"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
 
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="27b1124f-b18d-4689-9049-7efcc275ced7" name="Contains"><con:configuration><token>codeLigneAdressage</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="${#TestCase#SiretData}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="InvalidSiretNum" id="fec4b6de-78b0-4ff4-8455-325cf527adff"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="InvalidSiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>400</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value=""/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QzIXP5wAQJXfOuLdt2BW0JH_gmtsEsSbUH3LySrkouX5jXGagGl7kxC6EDXVyaiQmXkk5e_nSbTkN782oBz34izi1W6i-xV-8L7SsA6QGJ7JIaudkdCWd1fDNV10vQxByES3uEHT1rpqujPSXakjao79Dt0lHgvOq-LQiqgh6NVDdhynixumHGX_ZPnbuLWuZ-nVkHwvpaxvyjcFlmRw92EWILiNjbGUjgHSi6JCyJbkznSviVPXAFp-PRGBiynbwLmExUwQHvnjAUDyIPjc7W6IZ7Nwggi1_m7G_btxAXb764C6Bi9bO5ERpg9aas1NPJ_9R5Z-ta8LdHHOchYRsg</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QzIXP5wAQJXfOuLdt2BW0JH_gmtsEsSbUH3LySrkouX5jXGagGl7kxC6EDXVyaiQmXkk5e_nSbTkN782oBz34izi1W6i-xV-8L7SsA6QGJ7JIaudkdCWd1fDNV10vQxByES3uEHT1rpqujPSXakjao79Dt0lHgvOq-LQiqgh6NVDdhynixumHGX_ZPnbuLWuZ-nVkHwvpaxvyjcFlmRw92EWILiNjbGUjgHSi6JCyJbkznSviVPXAFp-PRGBiynbwLmExUwQHvnjAUDyIPjc7W6IZ7Nwggi1_m7G_btxAXb764C6Bi9bO5ERpg9aas1NPJ_9R5Z-ta8LdHHOchYRsg</con:value></con:property><con:property><con:name>SirenResponse</con:name><con:value>{"id":********,"sirenNum":"2023","companyName":"Company1","entityType":"PUBLIQUE"}</con:value></con:property><con:property><con:name>SiretResponse</con:name><con:value>{"id":24524284,"siretNum":"1236","principalEstablishment":"P","label":"string","addressLigne1":"string","addressLigne2":"string","addressLigne3":"string","town":"string","postalCode":0,"country":"string","fkSiretSiren":{"id":********,"sirenNum":null,"companyName":null,"entityType":null}}</con:value></con:property><con:property><con:name>SiretData</con:name><con:value>1236</con:value></con:property><con:property><con:name>SirenData</con:name><con:value/></con:property></con:properties></con:testCase><con:testCase id="ddee60fd-41d7-437e-843e-70eaad56f4f5" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="SearchByRoutingCode" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="d6604ea0-0dc8-4a0d-8849-57e6aaf12628"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="266db8ec-09fd-4bae-8a99-604513bb21c6"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="7aee3f19-39fb-4041-8458-730c3cc19e7c"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="CreateSiren" id="89b01d0b-ab22-4e6a-be54-55a7489e9e3f"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens" methodName="Sirens 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateSiren" id="74e36145-da5c-437e-9b4f-264a72a5b692" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{

  "sirenNum": "111",
  "companyName": "Company1",
  "entityType": "PUBLIQUE",
  "modificationDate": "2023-11-10T08:21:42.875Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="22159bf5-dc21-4f3a-ac94-8e20f4ce81d8" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TransferSirenId" id="574d31fe-f173-4659-ae2d-ced7b679bc98"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SirenID</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>CreateSiren</con:sourceStep><con:targetType>SirenResponse</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>XPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="CreateSiret" id="9ec0cc49-81f2-467a-9c28-c13e36879bb2"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirets" methodName="Sirets 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateSiret" id="68b6b5d0-2534-43cd-9508-4614923504aa" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  
  "siretNum": "222",
  "principalEstablishment": "P",
  "label": "string",
  "addressLigne1": "string",
  "addressLigne2": "string",
  "addressLigne3": "string",
  "town": "string",
  "postalCode": 0,
  "country": "string",
  "modificationDate": "2023-11-10T09:09:07.132Z",
  "fkSiretSiren": ${#TestCase#SirenResponse}
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirets</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="36e5c496-44de-4198-bf6c-dec0b6faa578" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TransferSiretId" id="4761b422-c901-44c9-a5a7-3ad4c2ad2816"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SiretID</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>CreateSiret</con:sourceStep><con:targetType>SiretResponse</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="CreateRoutingCode" id="65406d4c-b3fb-48e6-b01b-52d4d1bfb009"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" methodName="Routing-codes 1" resourcePath="/api/routing-codes" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateRoutingCode" id="aa774db8-551c-4bab-95e0-4477ea9f9352" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
 
  "routingCode": "333",
  "label": "string",
  "type": "DUNS",
  "modificationDate": "2023-11-10T10:25:20.167Z",
  "fkRoutingCodeSiret":${#TestCase#SiretResponse}
  
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/routing-codes</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="abdffc30-f2a4-4b1e-8928-5a100acdd157" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TransferRoutingId" id="89a66296-e5e7-4b80-b793-9cb4c7df8dd3"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>RoutingCodeID</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>CreateRoutingCode</con:sourceStep><con:targetType>RoutingResponse</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>RoutingData</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>CreateRoutingCode</con:sourceStep><con:sourcePath>$.routingCode</con:sourcePath><con:targetType>RoutingData</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="CreateAdressLine" id="8420c58b-705d-4b48-aa57-179f9285cf74"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/address-lines" methodName="Address-lines 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateAdressLine" id="37f76b19-0068-4927-93cf-4136d3943ac6" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "addressLineCode": "string",
  "applicationDate": "2023-11-10T08:33:52.579Z",
  "codeTypeModification": "A",
  "modificationId": "string",
  "entityType": "PUBLIQUE",
  "invoicingLineStatus": "ACTIF",
  "platformType": "PPF",
  "platformReceptionRegNumber": 0,
  "pdpSocialRaison": "string",
  "pdpCommercialName": "string",
  "platformReceptionDataContact": "string",
  "platformPeriodStart": "2023-11-10T08:33:52.579Z",
  "platformPeriodEnd": "2023-11-10T08:33:52.579Z",
  "platformReceptionState": "ACTIF",
  "managementLegalEngagement": true,
  "managementService": true,
  "managementServiceOrLegal": true,
  "moa": true,
  "moaOnly": true,
  "managementStatePayment": true,
  "modificationDate": "2023-11-10T08:33:52.579Z",
   "fkRoutingCodeSiret":${#TestCase#RoutingResponse}
}
</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="adbc7b07-f2d2-463f-9277-0e9cb6633309" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="RoutingCode" id="b20565b9-a5e5-4e44-8cf3-908747babe3a"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="RoutingCode" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="RoutingCode" id="dea67ace-36be-4ae6-bcfd-7a12055f063b" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="3a93385f-8259-46e9-affc-e6b1e8a17133" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="code-routage-destinataire" value="${#TestCase#RoutingData}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="InvalidRoutingCode" id="520de4d3-a30d-4ca6-b12b-d55ac844f5df"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="RoutingCode" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="InvalidRoutingCode" id="dea67ace-36be-4ae6-bcfd-7a12055f063b" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="3a93385f-8259-46e9-affc-e6b1e8a17133" name="Valid HTTP Status Codes"><con:configuration><codes>400</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="code-routage-destinataire" value=""/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iVwhYMLbFNyi9yBCF57wVpX28I76lnLybu2mwkx49FeCt2WV8jPloIxa2dxE65Hn1kgsRhH8ljKe_2HqJwK5xMzIEtvM8yTGL3BBP2pNp3rkB7Ay6gBtx7Ja1r8UtoM8jGoyxfzm_290JjFWnYUhaJC8vGOcu-m2RV8wQGB5Ukm-T9NEJvznA1M2f0G_LYkb3Bd-pBMTaGKnOTe8ApZ81f2QeEcJQtDJYFul8ZEb3qVquv2OKrhbZ8pjN8kNNNZ73wf8ZfRCu7cNA4iAsItNhOPVNM9jMUCpkl4Wg4PUxfDJo5CYWhJwN0a6lvFhANWhOTXBOvYMheleXsEasThs8g</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iVwhYMLbFNyi9yBCF57wVpX28I76lnLybu2mwkx49FeCt2WV8jPloIxa2dxE65Hn1kgsRhH8ljKe_2HqJwK5xMzIEtvM8yTGL3BBP2pNp3rkB7Ay6gBtx7Ja1r8UtoM8jGoyxfzm_290JjFWnYUhaJC8vGOcu-m2RV8wQGB5Ukm-T9NEJvznA1M2f0G_LYkb3Bd-pBMTaGKnOTe8ApZ81f2QeEcJQtDJYFul8ZEb3qVquv2OKrhbZ8pjN8kNNNZ73wf8ZfRCu7cNA4iAsItNhOPVNM9jMUCpkl4Wg4PUxfDJo5CYWhJwN0a6lvFhANWhOTXBOvYMheleXsEasThs8g</con:value></con:property><con:property><con:name>SirenResponse</con:name><con:value>{"id":********,"sirenNum":"111","companyName":"Company1","entityType":"PUBLIQUE"}</con:value></con:property><con:property><con:name>SiretResponse</con:name><con:value>{"id":********,"siretNum":"222","principalEstablishment":"P","label":"string","addressLigne1":"string","addressLigne2":"string","addressLigne3":"string","town":"string","postalCode":0,"country":"string","fkSiretSiren":{"id":********,"sirenNum":null,"companyName":null,"entityType":null}}</con:value></con:property><con:property><con:name>RoutingResponse</con:name><con:value>{"id":********,"routingCode":"333","label":"string","type":"DUNS","fkRoutingCodeSiret":{"id":********,"siretNum":null,"principalEstablishment":null,"label":null,"addressLigne1":null,"addressLigne2":null,"addressLigne3":null,"town":null,"postalCode":null,"country":null,"fkSiretSiren":null}}</con:value></con:property><con:property><con:name>RoutingData</con:name><con:value>333</con:value></con:property></con:properties></con:testCase><con:testCase id="92206a40-6007-408a-b7da-7ef8e792b131" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="LoginWithInvalidCredentials" searchProperties="true"><con:settings/><con:testStep type="groovy" name="ReadFromExcel" id="b1a0f404-5528-4d05-a416-d7047605573e"><con:settings/><con:config><script><![CDATA[import org.apache.poi.ss.usermodel.*
import org.apache.poi.hssf.usermodel.*
import org.apache.poi.xssf.usermodel.*
import org.apache.poi.ss.util.*
import org.apache.poi.ss.usermodel.WorkbookFactory

// Ajoutez cette ligne pour importer WorkbookFactory

// Définir le chemin du fichier Excel correctement (en supposant qu'il est sur le bureau)
def desktopPath = System.getProperty("user.home") + File.separator
def excelFilePath = desktopPath+File.separator+"data_ref.xlsx"
log.info("Excel file path: " + excelFilePath)

// Créer un flux d'entrée pour le fichier Excel
def fileInputStream = new FileInputStream(excelFilePath)

// Ouvrir le classeur Excel en utilisant WorkbookFactory
def workbook = WorkbookFactory.create(fileInputStream)

// Accéder à la feuille de calcul (supposons que c'est la première feuille, index 0)
def sheet = workbook.getSheetAt(0)



// Obtenir le nombre de lignes et de colonnes de la feuille
def lastRowNum = sheet.getLastRowNum() + 1 // +1 car les indices de ligne commencent à partir de 0
def lastColNum = sheet.getRow(1).getLastCellNum() // Prenez la première ligne pour obtenir le nombre de colonnes
// Créez une liste pour stocker toutes les données extraites de manière successive
def Data = []

// Parcourir les lignes et les colonnes pour extraire les données
for (int i = 2; i < lastRowNum; i++) {
    def allData = []
    for (int j = 0; j < lastColNum; j++) {
        def cell = sheet.getRow(i) ? sheet.getRow(i).getCell(j) : null
        def cellValue = cell ? cell.toString() : ""
        allData.add(cellValue)
    }
    Data.add(allData)
}

def usernames = []
def passwords = []


    for (int j = 1; j < Data.get(0).size(); j++) {
        usernames.add(Data.get(0).get(j))
    }
    for (int j = 1; j < Data.get(1).size(); j++) {
        passwords.add(Data.get(1).get(j))
    }
log.info('List of usernames: '+usernames)
log.info('List of passwords: '+passwords)


// Définir les noms des scénarios
def scenarioStepNames = ["Scenario1", "Scenario2", "Scenario3"]

// Loop through the test steps and send the data to the corresponding scenario
for (int i = 0; i < scenarioStepNames.size(); i++) {
    def stepName = scenarioStepNames[i]
    def Username = usernames[i]
    def Password = passwords[i]
log.info('My Username: '+Username)
    // Transférez les données au test step correspondant
    testRunner.testCase.getTestStepByName("InvalidUserName&Password").setPropertyValue("username", Username)
    testRunner.testCase.getTestStepByName("InvalidPassword").setPropertyValue("password", Password)
   testRunner.testCase.getTestStepByName("InvalidUsername").setPropertyValue("password", Password)
    
    // Exécutez le test step
    testRunner.runTestStepByName("InvalidUserName&Password")
    testRunner.runTestStepByName("InvalidPassword")
     testRunner.runTestStepByName("InvalidUsername") 
}

// Fermez le fichier Excel
workbook.close()
fileInputStream.close()
return

]]></script></con:config></con:testStep><con:testStep type="restrequest" name="InvalidUserName&amp;Password" id="06c63179-e600-4de4-86ae-05d83a98d763"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="InvalidUserName&amp;Password" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="ee92ed21-e23e-44c7-99b1-e7fb7193b2a6" name="Valid HTTP Status Codes"><con:configuration><codes>400</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="4a1663e7-e271-4014-8a84-956e92999060" name="Contains"><con:configuration><token>invalid_client</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:username>Neyla</con:username><con:password>legalref_admin1</con:password><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@1234"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalRef2"/>
  <con:entry key="username" value="legalref_admin111"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="InvalidPassword" id="ba80458d-4237-4b63-b5fb-f476adf2f6b4"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="InvalidPassword" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="55c44c8b-d7cc-486c-ade7-270f0b3d2981" name="Valid HTTP Status Codes"><con:configuration><codes>400</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="29dc10e1-b39d-4243-bca2-89662e3b1b4f" name="Contains"><con:configuration><token>invalid_client</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalRef2"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="InvalidUsername" id="979323ad-c88c-4c97-b6f5-9a60207d9942"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="InvalidUsername" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="a167d5a3-c84b-46c8-b175-b14f89a7c6e8" name="Valid HTTP Status Codes"><con:configuration><codes>400</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="d70c4cb8-099f-46cf-a99a-facc29df3341" name="Contains"><con:configuration><token>invalid_client</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalRef2"/>
  <con:entry key="username" value="legalref_admin111"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:loadTest id="d2eb6011-8b98-4fed-81ef-f3f8fb8647ce" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>5</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties/><con:securityTest id="194c8f69-66d9-4c63-98fb-157cb9774416" name="SecurityTest 1" failOnError="true"><con:settings/><con:properties/></con:securityTest></con:testCase><con:testCase id="c2061701-7581-4023-927c-d08258c1d8db" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="SearchByComapanyName" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="30da7c23-1ed1-4e32-9a71-42f7fee51df0"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="67005b4a-caf2-4539-a00f-63277168ec9b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="2b9ef53e-5ec0-40df-809e-99ec4dc4a306" name="Contains"><con:configuration><token>access_token</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:assertion type="JsonPath Match" id="b5c2a223-e6ae-4f4d-ad23-119ff0602381"><con:configuration/></con:assertion><con:assertion type="JsonPath Match" id="93aba33a-1002-431a-810e-d8ab3808b0e0" name="JsonPath Match 1"><con:configuration/></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="8361f78c-26e4-4518-af1d-d31bd7e624b0"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="true" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>XPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="affb60eb-025a-4971-ae04-6d88c4b720dc"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="CompanyName" id="3e956d02-42b4-41fb-903b-44936533b456"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="CompanyName" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CompanyName" id="206b2ab6-887e-4890-9b73-54aea639f5f0" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit":10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Simple Contains" id="8f53b157-8e12-41cd-8c28-23ebec59bb19" name="Contains"><con:configuration><token/><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:assertion type="Valid HTTP Status Codes" id="eb2bc694-c0c9-4854-99e9-94f3420d4ca0" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>raison-sociale-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="InvalidCompanyName" id="8fe41794-02bc-4a2a-a114-e8665225ef51"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="CompanyName" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="InvalidCompanyName" id="206b2ab6-887e-4890-9b73-54aea639f5f0" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  
  "limit":10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="eb2bc694-c0c9-4854-99e9-94f3420d4ca0" name="Valid HTTP Status Codes"><con:configuration><codes>400</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="format" value="JSON"/>
  <con:entry key="raison-sociale-destinataire" value=""/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>raison-sociale-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="cf885ab1-5c19-4656-a5b9-573a350dad3c" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>50</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>30</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NZRiZfUXHfkyV6GlvrysW_udkdxPPo1nSHRQ4QsFt02arEXzT2C3NUIBEllAIr6eAgDnOmNvBlP5xQtzJLTBGGS06qUH80ZAzHvd5mk2jpex7BgibNmCuB2wngapcwSIqqlnE2i9GfgUg3nuYHk98XL-yqzFoKwSNgejBPv-i7wiFGrJ4qAnOcRn4uB-dAxoXTFC1E80uANQA1HipjNzYlhUao06a25idHU9E-Iv0Dr_iIpAw9bZR5lMk2dRjx-MBmZS2Vzcu0y1fNlcZToRrdVrGf9w5HDSBLcoz9AG5LM6iCPC7OfZJm25OPRiQhtgJgChNR4U9JvWYaFx6YNHOg</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NZRiZfUXHfkyV6GlvrysW_udkdxPPo1nSHRQ4QsFt02arEXzT2C3NUIBEllAIr6eAgDnOmNvBlP5xQtzJLTBGGS06qUH80ZAzHvd5mk2jpex7BgibNmCuB2wngapcwSIqqlnE2i9GfgUg3nuYHk98XL-yqzFoKwSNgejBPv-i7wiFGrJ4qAnOcRn4uB-dAxoXTFC1E80uANQA1HipjNzYlhUao06a25idHU9E-Iv0Dr_iIpAw9bZR5lMk2dRjx-MBmZS2Vzcu0y1fNlcZToRrdVrGf9w5HDSBLcoz9AG5LM6iCPC7OfZJm25OPRiQhtgJgChNR4U9JvWYaFx6YNHOg</con:value></con:property></con:properties></con:testCase><con:testCase id="92bb7add-f8e1-495f-a577-4fef1cc89cf3" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="IterativeScénarios" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="b6f84289-bbb3-4282-b0da-c1713403b679"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="67005b4a-caf2-4539-a00f-63277168ec9b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="2b9ef53e-5ec0-40df-809e-99ec4dc4a306" name="Contains"><con:configuration><token>access_token</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:assertion type="JsonPath Match" id="b5c2a223-e6ae-4f4d-ad23-119ff0602381"><con:configuration/></con:assertion><con:assertion type="JsonPath Match" id="93aba33a-1002-431a-810e-d8ab3808b0e0" name="JsonPath Match 1"><con:configuration/></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="Property Transfer" id="a2d692e7-80d0-4263-af9b-4ded6f57f48b"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="true" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>XPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="ede8b671-791b-43df-96c1-83abb2b4c133"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="3954f545-b9a2-47c6-b39f-98ab7f8e941f"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token>1234</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="1234"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="SiretNum" id="39272da7-7597-4b56-8ebb-f9fd6258c78c"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="caf1dc6b-d97d-4ca8-9d44-e674c529cd23" name="Contains"><con:configuration><token>siretDestinataire</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="222"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="RoutingCode" id="c22879c3-12a0-4cd4-ab3b-3ab794eee692"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="RoutingCode" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="RoutingCode" id="dea67ace-36be-4ae6-bcfd-7a12055f063b" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{

  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="3a93385f-8259-46e9-affc-e6b1e8a17133" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="b1df28e5-14e3-490e-8275-3dfc9e5b4e21" name="Contains"><con:configuration><token>333</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="code-routage-destinataire" value="333"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="CompanyName" id="4ac2250f-e011-49de-8f62-a80666a0a056"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="CompanyName" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CompanyName" id="206b2ab6-887e-4890-9b73-54aea639f5f0" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="d3b18c48-2b2b-445d-9758-50ea57f98d54" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="c1635d4f-9a94-42fe-80eb-38e41cf1862e" name="Contains"><con:configuration><token>raisonSocialeDestinataire</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="format" value="JSON"/>
  <con:entry key="raison-sociale-destinataire" value="Company XYZ"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>raison-sociale-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="8cf8fbcf-32bb-4b6f-9c9c-f9a978ede3fd" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>50</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>30</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Vz2n3iEk9E_B2AfVfkkscXod-nHLpHlfkVrO3IBmEr2arryFdM_Lr0YTxg585V9v8HILtefODmduifhGIGZicUOXFE9ojHxUfT_QiUQP2U4np9yAiihvIaJHwr2ERss2IH8-PZWOe62l3zA2zZvx80Rg0dGUy0-PkbmDznIY215Cs2CCR_h-H4Hd-JtfOX9k0YovGL2BWW0GxteKQGB0ODq19PQVtEUCAqdrLdirzJUo8_tuY6MPJnjJudbyQ2wkhIfQXsCsRUpku5KfPzFV-0WwsFL0I80xrXnJicwdKwBE--xUBWliw49mH6rQZoJhOTyqzLEroK8giNPbsq5-pg</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Vz2n3iEk9E_B2AfVfkkscXod-nHLpHlfkVrO3IBmEr2arryFdM_Lr0YTxg585V9v8HILtefODmduifhGIGZicUOXFE9ojHxUfT_QiUQP2U4np9yAiihvIaJHwr2ERss2IH8-PZWOe62l3zA2zZvx80Rg0dGUy0-PkbmDznIY215Cs2CCR_h-H4Hd-JtfOX9k0YovGL2BWW0GxteKQGB0ODq19PQVtEUCAqdrLdirzJUo8_tuY6MPJnjJudbyQ2wkhIfQXsCsRUpku5KfPzFV-0WwsFL0I80xrXnJicwdKwBE--xUBWliw49mH6rQZoJhOTyqzLEroK8giNPbsq5-pg</con:value></con:property></con:properties></con:testCase><con:testCase id="ea22d151-0794-4790-ac3e-30549de70221" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="Siret&amp;SirenSearch" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="4c022897-8abe-4ddb-bd07-08476b686209"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="86e12916-578b-4e13-8d60-a0baa6428b97" name="Valid HTTP Status Codes"><con:configuration><codes>200
</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="6e56db48-2fea-4c1b-9e6a-9640417171d3"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="7d3028b4-a4fd-4adf-afe9-ca0cfeb908d2"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="SiretNum" id="016323f9-ea43-420c-a2b0-e9441d96ff35"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="cb6e3ef0-f939-4fbc-9648-314e1ca6d1dc" name="Contains"><con:configuration><token>"siretDestinataire":"300001s2"</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="300001s2"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="SirenTransfert" id="9982e4b8-eee4-4a24-8a70-8972feb0d1b8"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>siren-destinataire</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SiretNum</con:sourceStep><con:sourcePath>$[0].sirenDestinataire</con:sourcePath><con:targetType>SirenDestinataire</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:targetPath/><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="0406d9ec-4618-4044-a12f-db3678ac3701"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#SirenDestinataire}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ih9L1a9YErMu_Qc8_7COGM3IBNnuqaqiDbsdeocV0md2wHphw8VJUhBQ1KD2qzEs6050rQQYNaq1hbo-cShgS50BmzQpIpuSfrdSWXSBU4_Zbf6MGm-0sa7l0LbWDgyrJ8MUZRmTWyrEDfcY7rYrzDVVQQNon6OVd4PjBDgb8gqq5ssWSOMYc8CR6jkesAd2qJwQ0wbkVZOMQv5pCrzSI-nZ2AKrF4x-F1gfbEgOT0AmNS6iZcvDECBPvLj8sh6ub2sZ-oZ5XKu9ebxTLxMQGz2LowViezdcdWiNNuEB73g_RX4SlWF04hhATZt3VH6fPH76mx2abGp4mz9LKXKOBg</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ih9L1a9YErMu_Qc8_7COGM3IBNnuqaqiDbsdeocV0md2wHphw8VJUhBQ1KD2qzEs6050rQQYNaq1hbo-cShgS50BmzQpIpuSfrdSWXSBU4_Zbf6MGm-0sa7l0LbWDgyrJ8MUZRmTWyrEDfcY7rYrzDVVQQNon6OVd4PjBDgb8gqq5ssWSOMYc8CR6jkesAd2qJwQ0wbkVZOMQv5pCrzSI-nZ2AKrF4x-F1gfbEgOT0AmNS6iZcvDECBPvLj8sh6ub2sZ-oZ5XKu9ebxTLxMQGz2LowViezdcdWiNNuEB73g_RX4SlWF04hhATZt3VH6fPH76mx2abGp4mz9LKXKOBg</con:value></con:property><con:property><con:name>SIREN</con:name><con:value>3</con:value></con:property><con:property><con:name>CompanyName</con:name><con:value>AA3</con:value></con:property><con:property><con:name>SirenDestinataire</con:name><con:value>300001</con:value></con:property></con:properties></con:testCase><con:testCase id="aea223fc-bc44-49ca-b804-42ff0b4740ba" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="Siret&amp;SirenSearch&amp;RoutingCodeSearch" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="0900a0d6-b3c6-4841-a487-090ed63dd0f7"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="86e12916-578b-4e13-8d60-a0baa6428b97" name="Valid HTTP Status Codes"><con:configuration><codes>200
</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="ef850238-4dd0-40be-9a5f-d35f819bbf9a"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="425eb94c-2d4e-4934-aaf1-84fa9df689de"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="RoutingCode" id="b52406b1-5a02-4d7e-a498-990df83c08e6"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="RoutingCode" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="RoutingCode" id="dea67ace-36be-4ae6-bcfd-7a12055f063b" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="3a93385f-8259-46e9-affc-e6b1e8a17133" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="b1df28e5-14e3-490e-8275-3dfc9e5b4e21" name="Contains"><con:configuration><token>*********</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="code-routage-destinataire" value="*********"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="RoutingCodeTransfert" id="15998f2c-d06a-4750-bd9e-1dcee810f95a"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>code-routage-destinataie</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>RoutingCode</con:sourceStep><con:sourcePath>$[0].siretDestinataire</con:sourcePath><con:targetType>SIRET</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="SiretNum" id="eb56f2b4-ca8c-47e8-8c43-6c82291bec86"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="cb6e3ef0-f939-4fbc-9648-314e1ca6d1dc" name="Contains"><con:configuration><token>siretDestinataire</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="${#TestCase#SIRET}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="SirenNumTransfert" id="31237bed-b7ae-4fb7-a3ad-7fc82de8a8ac"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>siren-destinataire</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SiretNum</con:sourceStep><con:sourcePath>$[0].sirenDestinataire</con:sourcePath><con:targetType>sirenDestinataire</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:targetPath/><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="8da1a678-d9ac-4b88-853e-0c606c5bfc51"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token>codeLigneAdressage</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#sirenDestinataire}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ENtAdSxZbaY-H-_WUWhaIpBR1Vez57KdTjwpdCo5YHFkoLyQi9l57S8JSv-8dj6Hfq24UcBT3QBK9sIkUKavhoijskcziE88KOo1wJeSYsWHmkwKmFWA7r8Yu6d2NmoDhgx5uPoNZMcMNCUennTRVvIssglbzKKh7_x_8OyP-UuG2wUiJUPyMIDt2qIDKtKNaoxAppgwJDbWD7_ZOs7kwjA4N2iGK46h6k1wy_X6-m0udH0V6R6p9shDOkQ77rXNRO08AHiMdnFhLPnqsYmwqVSfuURYOZ4d-BeX1k3lIkK9Dtqzyak5ME4qbvr_WgihK4A0DBwdJUnKwyXLLJW_9A</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ENtAdSxZbaY-H-_WUWhaIpBR1Vez57KdTjwpdCo5YHFkoLyQi9l57S8JSv-8dj6Hfq24UcBT3QBK9sIkUKavhoijskcziE88KOo1wJeSYsWHmkwKmFWA7r8Yu6d2NmoDhgx5uPoNZMcMNCUennTRVvIssglbzKKh7_x_8OyP-UuG2wUiJUPyMIDt2qIDKtKNaoxAppgwJDbWD7_ZOs7kwjA4N2iGK46h6k1wy_X6-m0udH0V6R6p9shDOkQ77rXNRO08AHiMdnFhLPnqsYmwqVSfuURYOZ4d-BeX1k3lIkK9Dtqzyak5ME4qbvr_WgihK4A0DBwdJUnKwyXLLJW_9A</con:value></con:property><con:property><con:name>SIREN</con:name><con:value>3</con:value></con:property><con:property><con:name>CompanyName</con:name><con:value>AA3</con:value></con:property><con:property><con:name>sirenDestinataire</con:name><con:value>500000</con:value></con:property><con:property><con:name>SIRET</con:name><con:value>500000ss1</con:value></con:property></con:properties></con:testCase><con:testCase id="20322ed1-8347-4d6f-a656-c096094c088f" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="CreationOfAdressLine" searchProperties="true" disabled="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="b133b100-f307-438f-8064-a244c77215be"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="1dd5b0e1-e70e-4b83-a2d5-16bd2d88d0d9"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="86545db0-4580-41fc-961c-c3e51c7cf300"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="6fb0f82e-1f2c-4344-b48e-417ba1cd76c4"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="500000"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="Property Transfer" id="81514026-8b6d-4d1c-8a51-bbdefbce8757"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>TransferAdressLine</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SirenNum</con:sourceStep><con:sourcePath>[0].codeLigneAdressage</con:sourcePath><con:targetType>AdressLine</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="CreateAdressLine" id="86f270ef-9d51-4fe6-83b8-c8c0be4572ed"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/address-lines" methodName="Address-lines 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="CreateAdressLine" id="26e3c2dc-be7f-4bc6-8444-ffb8db7c49f6" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "id": 125,
  "addressLineCode": "neila01",
  "applicationDate": "2023-11-07T07:50:42.566Z",
  "codeTypeModification": "A",
  "modificationId": "string",
  "entityType": "PUBLIQUE",
  "invoicingLineStatus": "ACTIF",
  "platformType": "PPF",
  "platformReceptionRegNumber": 0,
  "pdpSocialRaison": "string",
  "pdpCommercialName": "string",
  "platformReceptionDataContact": "string",
  "platformPeriodStart": "2023-11-07T07:50:42.566Z",
  "platformPeriodEnd": "2023-11-07T07:50:42.566Z",
  "platformReceptionState": "ACTIF",
  "managementLegalEngagement": true,
  "managementService": true,
  "managementServiceOrLegal": true,
  "moa": true,
  "moaOnly": true,
  "managementStatePayment": true,
  "modificationDate": "2023-11-07T07:50:42.566Z",
  "pkRoutingCode": {
 
    "routingCode": "string",
    "label": "string",
    "type": "DUNS",
    "modificationDate": "2023-11-07T07:50:42.566Z",
    "fkRoutingCodeSiret": {
    
      "siretNum": "neila02",
      "principalEstablishment": "P",
      "label": "string",
      "addressLigne1": "string",
      "addressLigne2": "string",
      "addressLigne3": "string",
      "town": "string",
      "postalCode": 0,
      "country": "string",
      "modificationDate": "2023-11-07T07:50:42.566Z",
      "fkSiretSiren": {
    
        "sirenNum": "string",
        "companyName": "string",
        "entityType": "PUBLIQUE",
        "modificationDate": "2023-11-07T07:50:42.566Z"
      }
    }
  },
  "fkAddressLineSiren": {
 
    "sirenNum": "string",
    "companyName": "string",
    "entityType": "PUBLIQUE",
    "modificationDate": "2023-11-07T07:50:42.566Z"
  },
  "fkAddressLineSiret": {

    "siretNum": "string",
    "principalEstablishment": "P",
    "label": "string",
    "addressLigne1": "string",
    "addressLigne2": "string",
    "addressLigne3": "string",
    "town": "string",
    "postalCode": 0,
    "country": "string",
    "modificationDate": "2023-11-07T07:50:42.566Z",
    "fkSiretSiren": {
 
      "sirenNum": "string",
      "companyName": "string",
      "entityType": "PUBLIQUE",
      "modificationDate": "2023-11-07T07:50:42.566Z"
    }
  }
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/address-lines</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="0e9f0b28-6451-44b1-8325-e78224c881cc" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LOB7ERch3-lSBXWFjfKTkWKJqEGID58wWbSLag2OA63TCJmK_3EnugoBIq5C0yYm3O1cJEAcMQaQsIZagY45JWr3XlmRjPz16gI1WgoOAQhX-WAudUNCEZ1QMyZAdR4zVhSiIwW-tTueG9frFqlfvRFP3PpgHI0yspXcSX_yAlqkPJ3QImZlCrSKrexehVfk0j_KIsww7L7fpcsNWSoWgRZ0i75xRVbIe9NoQcnTgXgBUxnVbcw65ZBuVvpXBwsggLibAMKQtByXlCjeZs2vV2Ecq5NcY6ASlXl6TaxHaVbFIM0UaFsxHZeoh4HlKvFnHIXw5A2SK5TIjNBuUo-Sdg</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LOB7ERch3-lSBXWFjfKTkWKJqEGID58wWbSLag2OA63TCJmK_3EnugoBIq5C0yYm3O1cJEAcMQaQsIZagY45JWr3XlmRjPz16gI1WgoOAQhX-WAudUNCEZ1QMyZAdR4zVhSiIwW-tTueG9frFqlfvRFP3PpgHI0yspXcSX_yAlqkPJ3QImZlCrSKrexehVfk0j_KIsww7L7fpcsNWSoWgRZ0i75xRVbIe9NoQcnTgXgBUxnVbcw65ZBuVvpXBwsggLibAMKQtByXlCjeZs2vV2Ecq5NcY6ASlXl6TaxHaVbFIM0UaFsxHZeoh4HlKvFnHIXw5A2SK5TIjNBuUo-Sdg</con:value></con:property></con:properties></con:testCase><con:properties><con:property><con:name>sirenNum.equals</con:name><con:value>1</con:value></con:property></con:properties><con:tearDownScript/></con:testSuite><con:testSuite id="d7b94de0-898b-47ac-83bc-c5e72a9ab814" name="Copy of Library"><con:settings/><con:runType>SEQUENTIAL</con:runType><con:testCase id="91286a37-da3a-45e5-98c5-a20fbb7d6315" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="Reporting_Utility" searchProperties="true"><con:settings/><con:testStep type="groovy" name="GenerateCSVReport" id="f1f184f7-7b95-4a5a-a438-c62601f90a05"><con:settings/><con:config><script>// Try-catch block to handle exceptions
try {

// 1. Create a "SoapUIResults" folder in the project path 
      // Retrieve the project root folder
      def projectPath = new com.eviware.soapui.support.GroovyUtils(context).projectPath

      // Specify a folder inside project root to store the results
      String folderPath = projectPath + "/SoapUIResults";

      // Create a File object for the specified path
      def resultFolder = new File(folderPath);

      // Check for existence of folder and create a folder
      if(!resultFolder.exists())
      {
        resultFolder.mkdirs();
      }

/* ------------------------------------------------------------------------------- */

// 2. Create a subfolder (with timestamp) to store the request-response local copy 
      // Retrieve the latest execution date-time
      Date d = new Date();
      def executionDate = d.format("dd-MMM-yyyy HH_mm");

      // Specify the subfolder path with name Request-Response_CurrentTimeStamp
      String subfolderPath1 = folderPath+ "/Request-Response_"+executionDate;

      // Create this sub-folder
      new File(subfolderPath1).mkdirs();

/* ------------------------------------------------------------------------------- */

// 3. Create another subfolder "CSV Reports" to store the reports file 
      // Specify the subfolder path with name CSV Reports
      String subfolderPath2 = folderPath+ "/CSV Reports";

      // Create this sub-folder
      new File(subfolderPath2).mkdirs();

/* ------------------------------------------------------------------------------- */

// 4. Create a Report.csv file inside the CSV Reports folder 
      // Create a File object for Report csv file (with timestamp)
      def reportFile = new File(subfolderPath2, "Report_"+executionDate+".csv");

      // Check for existence of report file and create a file
      if(!reportFile.exists())
      {
        reportFile.createNewFile();
        // Create required column names in the report file
        reportFile.write('"Test Suite","Test Case","Test Step","Step Type","Step Status",'
                        +'"Result message","Execution Date"');
      }
/* ------------------------------------------------------------------------------- */
// 5. Inserting data in the file
      // Iterate over all the test steps results
  for(stepResult in testRunner.getResults())
  {
    // Retrieve Test Suite name
   def testSuite = testRunner.testCase.testSuite.name;
   // Retrieve Test Case name
   def testCase = testRunner.testCase.name;
   // Retrieve Test Step
   def testStep = stepResult.getTestStep();
   // Retrieve Test Step name
   def testStepName = testStep.name
   // Retrieve Test Step type
   def type = testStep.config.type
   // Retrieve Test Step status
   def status = stepResult.getStatus()

   // Creating new line in report file
   reportFile.append('\n');

   // Write all the necessary information in the file
   reportFile.append('"' + testSuite + '",');
   reportFile.append('"' + testCase + '",');
   reportFile.append('"' + testStepName + '",');
   reportFile.append('"' + type + '",');
   reportFile.append('"' + status + '",');

   // Retrieve the test result messages
   reportFile.append('"');
   for(resMessage in stepResult.getMessages())
   {
     // Write messages and separate multiple messages by new line
     reportFile.append('Message:' + resMessage + '\n');
   }
   reportFile.append('",');

   //Write executionDate in the file
   reportFile.append('"' + executionDate + '",');
/* ------------------------------------------------------------------------------- */
// 6. Extract the request and response and save it to external file
      // Verify if the test step type is request: SOAP project or restrequest: REST project
        if((type=="request").or(type=="restrequest"))
        {
          // Extract the request from the test step
          def extRequest = testStep.properties["Request"].value;    

      // Create a file in the reports folder and write the request
      // Naming convention: request_TestSuiteName_TestCaseName_TestStepName.txt
      def requestFile=subfolderPath1+"/request_"+testSuite+"_"+testCase+"_"+testStepName+".txt";
      def rqfile = new File(requestFile);
      rqfile.write(extRequest, "UTF-8");

      // Extract the response from the test step
      def extResponse = stepResult.getResponseContent();    

      // Create a file in the reports folder and write the response
      // Naming convention: response_TestSuiteName_TestCaseName_TestStepName.txt
      def responseFile=subfolderPath1+"/response_"+testSuite+"_"+testCase+"_"+testStepName+".txt";
      def rsfile = new File(responseFile);
      rsfile.write(extResponse, "UTF-8");
     }
   }
 }
catch(exc)
{
   log.error("Exception happened: " + exc.toString());
}</script></con:config></con:testStep><con:properties/></con:testCase><con:properties/></con:testSuite><con:testSuite id="3371b640-4aeb-4972-b27b-92b459a98096" name="BT Covering Test" disabled="true"><con:settings/><con:runType>SEQUENTIAL</con:runType><con:testCase id="6a4a2c73-f7ce-4fcf-b443-c1b3f3aa67cb" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="+Response:AdressLineIDResponse" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="89e20f2a-bb79-4827-9eaa-fb467f994f39"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="7aad676a-7189-4e73-b2c7-5b9cadc2f92a"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="76160fb3-397b-4c84-a701-713aaa69fcae"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="AdressLINE" id="2ca859b0-0517-4732-9da8-3f02a40c67f0"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1" methodName="AdressLine" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="AdressLINE" id="af8548a4-e2c5-49c8-ae0c-c16c53a929cc" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/12345-6789</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="9e2df7f0-d6df-4af6-ad54-27fa95a32382" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="7db2355a-7b79-4d00-9597-84cd55aa2f0a" name="Contains"><con:configuration><token>12345-6789</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="ligne-annuaire" value="12345-6789" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>ligne-annuaire</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="672e3c66-8786-488a-8e14-d74ad388eef4" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>5</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Q7-mizg-M3kJ2jbIf_ok-EoiFiJDoSZ7PvE1yuBaF5_LWapdj_Lc7ZugcoC7K9xs1C1AiHtBzGJAhypvHFsaWzDQfYiDRScdfRndJZ4S0dkN6GIt3Dso1RpZEK1WL6S-DYPI62UDIOYFq2vkC1jnHPJVaaHQS2xUQB7jKa8eNsOqN0705QUGShvQF-dS48Zmczv0VDt2GBbs6w0b4tOkJXLmmIEmEd4-8Uuv11KPUjCnxOn864cI6lr9LaPoVPTsygLoNFJyGr7VnEuS3-3mWy7aB5bRUPzgmWRK6iv2wWmxeYlC-a4GX8f-RU-KEjW1zwmaUbFw2FOfnUFJWIv9og</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Q7-mizg-M3kJ2jbIf_ok-EoiFiJDoSZ7PvE1yuBaF5_LWapdj_Lc7ZugcoC7K9xs1C1AiHtBzGJAhypvHFsaWzDQfYiDRScdfRndJZ4S0dkN6GIt3Dso1RpZEK1WL6S-DYPI62UDIOYFq2vkC1jnHPJVaaHQS2xUQB7jKa8eNsOqN0705QUGShvQF-dS48Zmczv0VDt2GBbs6w0b4tOkJXLmmIEmEd4-8Uuv11KPUjCnxOn864cI6lr9LaPoVPTsygLoNFJyGr7VnEuS3-3mWy7aB5bRUPzgmWRK6iv2wWmxeYlC-a4GX8f-RU-KEjW1zwmaUbFw2FOfnUFJWIv9og</con:value></con:property></con:properties><con:securityTest id="e02f5c5a-2b66-4d1e-b51f-14e263b9c1c3" name="SecurityTest 1" failOnError="true"><con:settings/><con:properties/></con:securityTest></con:testCase><con:testCase id="0a32356a-0aa4-422d-b974-99f8f7a53514" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="+Respons:SirenNum&amp;Siret&amp;RoutingCode" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="9eebf9cf-8375-4c87-b241-a501c00c76c6"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="3158a70c-c895-48ed-91e5-3694a69ea228"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="380c8a62-1025-45e1-bf23-a15883f088b5"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="SiretNum" id="d53dd978-729a-412f-a4b7-29bf1d0cf11b"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="cb6e3ef0-f939-4fbc-9648-314e1ca6d1dc" name="Contains"><con:configuration><token>"siretDestinataire":"*********00012"</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="*********00012"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="SirenTransfert" id="1fe6f1e7-32a8-4dbf-9e8e-dfc24e78246d"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>siren-destinataire</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SiretNum</con:sourceStep><con:sourcePath>$[0].sirenDestinataire</con:sourcePath><con:targetType>SirenDestinataire</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:targetPath/><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="1ad9cedc-a0f2-4e12-a20c-39810c324c30"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token>codeLigneAdressage</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#SirenDestinataire}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TransferRoutingCode" id="1ee56d75-4a4f-4d34-ad12-c9cc1141f2bb"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>code-routage-destinataie</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SirenNum</con:sourceStep><con:sourcePath>$[0].codeRoutageDestinataire</con:sourcePath><con:targetType>RoutingCode</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="RoutingCode" id="579eef13-ed22-4002-ab71-c9fed9153a44"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="RoutingCode" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="RoutingCode" id="dea67ace-36be-4ae6-bcfd-7a12055f063b" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="3a93385f-8259-46e9-affc-e6b1e8a17133" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="b1df28e5-14e3-490e-8275-3dfc9e5b4e21" name="Contains"><con:configuration><token>RoutCode01</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="code-routage-destinataire" value="${#TestCase#RoutingCode}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="8b1e9b3b-25d6-4402-a994-458485f9e24a" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>5</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HNQh0VyRwdTSJj9Qu3idtEpGQy-bf8k1oWe3kSwuuKPpvYrXGDE4ftbMs2AZ-GDH1cmMM-NamAfPl6iqNNLbjpbFnSaMcqSSIOKohO8zyK7OxfuBT3kLKKcw7og5ex6K0irHj-CAGV6YWCOwHKtSf0x4wjhh_Wbl_YiNxRXb8DGRMIvsrEZTzybG4qt9HenSZDKa32lOXA28msbVpnyRsTJiOhoJwb-fdCfKJtz1LbUJ5qzP1XAaL2gcm11SezL-fXmnIsx8tmqtnYHAShjCE9SaQ8qRfVceJCwjBEg378wbWuJwtXgbGXvHzeW2a8JbH7Zjzo99P6IB6F8ACCPbWw</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HNQh0VyRwdTSJj9Qu3idtEpGQy-bf8k1oWe3kSwuuKPpvYrXGDE4ftbMs2AZ-GDH1cmMM-NamAfPl6iqNNLbjpbFnSaMcqSSIOKohO8zyK7OxfuBT3kLKKcw7og5ex6K0irHj-CAGV6YWCOwHKtSf0x4wjhh_Wbl_YiNxRXb8DGRMIvsrEZTzybG4qt9HenSZDKa32lOXA28msbVpnyRsTJiOhoJwb-fdCfKJtz1LbUJ5qzP1XAaL2gcm11SezL-fXmnIsx8tmqtnYHAShjCE9SaQ8qRfVceJCwjBEg378wbWuJwtXgbGXvHzeW2a8JbH7Zjzo99P6IB6F8ACCPbWw</con:value></con:property><con:property><con:name>SirenDestinataire</con:name><con:value>*********</con:value></con:property><con:property><con:name>RoutingCode</con:name><con:value>RoutCode01</con:value></con:property></con:properties><con:securityTest id="d5412564-582f-4226-8824-ff617600441c" name="SecurityTest 1" failOnError="true"><con:settings/><con:properties/></con:securityTest></con:testCase><con:testCase id="9237ab50-c747-4f61-b93f-1e579f9cc5e1" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="-Response:AdressLine>+Response:Siret&amp;Siren" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="1dd39977-65dc-4a49-9aca-616405a5d5af"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="f2b63691-a103-4e8c-94ad-47ce77903f0c"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="03d91179-325e-48f3-aec9-06bdb8b481e1"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="AdressLINE" id="2e68212e-4472-40ca-ad87-cb0518f3c03d"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1" methodName="AdressLine" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="AdressLINE" id="af8548a4-e2c5-49c8-ae0c-c16c53a929cc" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/12345-6789</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="ligne-annuaire" value="12345-6789" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>ligne-annuaire</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="groovy" name="Groovy Script" id="d3dfed3e-1896-4960-829c-a917b0976ad6"><con:settings/><con:config><script>def currentTestCase = testRunner.testCase
def addressLineStep = currentTestCase.getTestStepByName("AdressLINE")
def siretNumStep = currentTestCase.getTestStepByName("SiretNum")

// Exécuter l'étape AdressLINE
addressLineStep.run(testRunner, context)

// Vérifier le code de statut HTTP de la réponse
def response = addressLineStep.testRequest.response
def statusCode = response.getStatusCode()

// Si le code de statut est différent de 200, exécuter l'étape SiretNum
if (statusCode != 200) {
    siretNumStep.run(testRunner, context)
}

// Sinon, l'étape SiretNum ne sera pas exécutée
</script></con:config></con:testStep><con:testStep type="restrequest" name="SiretNum" id="6364a81b-2b2c-458c-a1ec-f3719e35cacb"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="cb6e3ef0-f939-4fbc-9648-314e1ca6d1dc" name="Contains"><con:configuration><token>"siretDestinataire":"*********00012"</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="*********00012"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="Property Transfer1" id="0e77c64f-edca-48a4-8844-00d674fc4389"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>siren-destinataire</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SiretNum</con:sourceStep><con:sourcePath>$[0].sirenDestinataire</con:sourcePath><con:targetType>SirenDestinataire</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:targetPath/><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="7eddae5b-5eca-42e1-ad39-68ba40d5f429"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token>codeLigneAdressage</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#SirenDestinataire}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="241072a2-8b6a-42ad-bcbf-5fac859ba62d" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>5</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZWSqHrAjbtvRkuJAI3dgXNLdo5JILwXlbU9sa4f8BV_9FdA4R10x70WozDNh_SdSBozRKg5m0qYQN8bn6Hf5Nux_R8Rd9HgjALKc97vrr4bHYjNdjpsUIBJyLGrlanplZxUJ1DztJCQzVKodtfIWiemhAxnshQhybNy_b9I3zlKNPhD94RFdxliLRij01WXWBaeexQtXcOVRm4c9YiFS-L18RVVKypF2d8uA_yItpCY2YI6g_ov-i8mtdiDl6LfqoJmkg_5pA18BJkCVjjoJ3VHKDo-GNBu4TlTjMNXplUgkm3FQUJC4tnxWXKM8YCHShVA0HKgs562QM2MPPNvK1Q</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZWSqHrAjbtvRkuJAI3dgXNLdo5JILwXlbU9sa4f8BV_9FdA4R10x70WozDNh_SdSBozRKg5m0qYQN8bn6Hf5Nux_R8Rd9HgjALKc97vrr4bHYjNdjpsUIBJyLGrlanplZxUJ1DztJCQzVKodtfIWiemhAxnshQhybNy_b9I3zlKNPhD94RFdxliLRij01WXWBaeexQtXcOVRm4c9YiFS-L18RVVKypF2d8uA_yItpCY2YI6g_ov-i8mtdiDl6LfqoJmkg_5pA18BJkCVjjoJ3VHKDo-GNBu4TlTjMNXplUgkm3FQUJC4tnxWXKM8YCHShVA0HKgs562QM2MPPNvK1Q</con:value></con:property><con:property><con:name>SirenDestinataire</con:name><con:value>*********</con:value></con:property></con:properties><con:securityTest id="37e0af77-1271-4358-96b0-b5bc2e101415" name="SecurityTest 1" failOnError="true"><con:settings/><con:properties/></con:securityTest></con:testCase><con:testCase id="7f291382-0063-4c47-ab7e-49e28688a370" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="-Response:AdressLine>-Response:Siret&amp;Siren" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="25c16ee0-c4e7-43b6-8938-ac544723437f"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="e6e4ecba-5270-428d-b399-f58b9a4f070d"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="0f6e26d3-d686-44cb-bc04-c56eccd4faf6"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="AdressLINE" id="645d9985-40b4-43f1-b50f-097616595466"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1" methodName="AdressLine" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="AdressLINE" id="af8548a4-e2c5-49c8-ae0c-c16c53a929cc" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/12345-6789</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="9e2df7f0-d6df-4af6-ad54-27fa95a32382" name="Valid HTTP Status Codes"><con:configuration><codes>400</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="ligne-annuaire" value="12345-6789" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>ligne-annuaire</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="SiretNum" id="95bc3560-80c4-4ccc-a7c0-8a698188dabb"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="cb6e3ef0-f939-4fbc-9648-314e1ca6d1dc" name="Contains"><con:configuration><token>"siretDestinataire":"*********00012"</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="*********00012"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="SirenTransfer" id="cf25a09a-3129-4778-825a-1f9c15c51f7c"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>siren-destinataire</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SiretNum</con:sourceStep><con:sourcePath>$[0].sirenDestinataire</con:sourcePath><con:targetType>SirenDestinataire</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:targetPath/><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="da802ee4-e9d1-4c7b-949b-235e3f017a32"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>400</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#SirenDestinataire}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="SearchSirenNum" id="283e4c21-2ecf-45c2-bdf5-04de94e7db5d"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SearchSirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token>codeLigneAdressage</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="1234"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="0cf98615-9b45-41b2-b791-ecffc7057624" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>5</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dARPSN2oOqSKQmwVdWRPL3NjCpkcQyrm96o00no9yURvxpJE64Yj6scjgU2rN7tbQn_8FiDc9eW5FkOS6VI7yXcxgakscwdu8nwjdhOd9bNVoSzwS9JvDOFqiiTnV9CaZZjaDmVl9C-C7uTh4y9srRKG2xKHVwrFEU3IZ4ns_qS_7QeuBc2u_JyU3rRVSeBCRlSqebYy6y08cXaBOvhyritGQc7dmwgEZoVGH8-ROr4ZQ2BiKe4K8r_tWj-BcvfdmX0AB4HPbctW_3-VQV1gouZRX7za8jkCFAmBfJFWr92qb3ra5dpu93rn5nZ4fr2RnZL3eLySTP20_S37-tBweA</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dARPSN2oOqSKQmwVdWRPL3NjCpkcQyrm96o00no9yURvxpJE64Yj6scjgU2rN7tbQn_8FiDc9eW5FkOS6VI7yXcxgakscwdu8nwjdhOd9bNVoSzwS9JvDOFqiiTnV9CaZZjaDmVl9C-C7uTh4y9srRKG2xKHVwrFEU3IZ4ns_qS_7QeuBc2u_JyU3rRVSeBCRlSqebYy6y08cXaBOvhyritGQc7dmwgEZoVGH8-ROr4ZQ2BiKe4K8r_tWj-BcvfdmX0AB4HPbctW_3-VQV1gouZRX7za8jkCFAmBfJFWr92qb3ra5dpu93rn5nZ4fr2RnZL3eLySTP20_S37-tBweA</con:value></con:property><con:property><con:name>SirenDestinataire</con:name><con:value/></con:property></con:properties><con:securityTest id="64413d0a-179d-4551-ac52-bda0dd1d84b9" name="SecurityTest 1" failOnError="true"><con:settings/><con:properties/></con:securityTest></con:testCase><con:testCase id="c48f330c-83eb-4be4-8a74-6a2be24ff99e" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="-Respons:SirenNum&amp;Siret&amp;RoutingCode>+Response:Siren&amp;Siret" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="ff406ca0-f7ad-4b6e-81be-5a6a0719934a"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="1fdf5d75-3a78-46e8-b38f-690e3bbe47df"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="d7118e00-9197-41cd-9f2d-9f261d44f18c"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="SiretNum" id="94526479-7fdb-4692-9af2-aed75adb04e0"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="cb6e3ef0-f939-4fbc-9648-314e1ca6d1dc" name="Contains"><con:configuration><token>"siretDestinataire":"*********00012"</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="*********00012"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="SirenTransfert" id="430053a3-2143-4ec2-a6ad-ebb82a70613e"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>siren-destinataire</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SiretNum</con:sourceStep><con:sourcePath>$[0].sirenDestinataire</con:sourcePath><con:targetType>SirenDestinataire</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:targetPath/><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="3a53df44-3cb9-4f54-8e56-bd68dd45fae5"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token/><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#SirenDestinataire}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TransferRoutingCode" id="e9b7fd44-6984-480a-8f21-eafb8faf7355"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>code-routage-destinataie</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SirenNum</con:sourceStep><con:sourcePath>$[0].codeRoutageDestinataire</con:sourcePath><con:targetType>RoutingCode</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="RoutingCode" id="e85a3964-9be2-4e65-82fd-46dda92b7614"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="RoutingCode" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="RoutingCode" id="dea67ace-36be-4ae6-bcfd-7a12055f063b" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="code-routage-destinataire" value="${#TestCase#RoutingCode}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="groovy" name="Groovy Script" id="19988aca-b2f2-4cdb-8c77-3a0f6090fa7c"><con:settings/><con:config><script>def currentTestCase = testRunner.testCase
def RoutingCodeStep = currentTestCase.getTestStepByName("RoutingCode")
def SiretNumStep = currentTestCase.getTestStepByName("ValidSiretNum")

// Exécuter l'étape AdressLINE
RoutingCodeStep.run(testRunner, context)

// Vérifier le code de statut HTTP de la réponse
def response = RoutingCodeStep.testRequest.response
def statusCode = response.getStatusCode()

// Si le code de statut est différent de 200, exécuter l'étape SiretNum
if (statusCode != 200) {
    SiretNumStep.run(testRunner, context)
}

// Sinon, l'étape SiretNum ne sera pas exécutée
</script></con:config></con:testStep><con:testStep type="restrequest" name="ValidSiretNum" id="48c3602d-6b31-4a88-8286-b2f12ae2df42"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidSiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>http://legal-referential.chassagne-qa.generixgroup.com</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="cb6e3ef0-f939-4fbc-9648-314e1ca6d1dc" name="Contains"><con:configuration><token>"siretDestinataire":"*********00012"</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="*********00012"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="ValidSirenTransfert" id="824d0595-a87a-415a-b6ee-92d7c3355319"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>siren-destinataire</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SiretNum</con:sourceStep><con:sourcePath>$[0].sirenDestinataire</con:sourcePath><con:targetType>SirenDestinataire</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:targetPath/><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="ValidSirenNum" id="1aded3bd-129a-4a2b-94a2-34d539b89615"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidSirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token>codeLigneAdressage</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#SirenDestinataire}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="c9fa4784-f94b-4b83-b219-6e148dbf26b4" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>5</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.b78NTV1o3A3r2v51tJjABd5QX-YKED2RVNBGN1MrKo8TkBDJ0GnxapgenwO5dHPggfEIdrig9BQheuDPiGG85FOqZvNnNe-KBT1pkj_82UMT53UGyFKgfvjgSry1TI0UOD-z_OgTkHLCLmToiGKJZ0jI62TkbWvdJ_C8PISjUhM_4MMt_Ulj1W3oxJc5lukbx6dVj0xqKUGM5ELgSrOwydbBRPZd82GcwJ217vNrHqW5QxnQi7iBu7iRDGT8FQYKismmtsdO2deGDwd9Dbp1r0BHe_Um_Gctf88vF5GDdL3FZb5MurHKyTT6giltgfU5CF0KNwwRriz4RKHLSyWttw</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.b78NTV1o3A3r2v51tJjABd5QX-YKED2RVNBGN1MrKo8TkBDJ0GnxapgenwO5dHPggfEIdrig9BQheuDPiGG85FOqZvNnNe-KBT1pkj_82UMT53UGyFKgfvjgSry1TI0UOD-z_OgTkHLCLmToiGKJZ0jI62TkbWvdJ_C8PISjUhM_4MMt_Ulj1W3oxJc5lukbx6dVj0xqKUGM5ELgSrOwydbBRPZd82GcwJ217vNrHqW5QxnQi7iBu7iRDGT8FQYKismmtsdO2deGDwd9Dbp1r0BHe_Um_Gctf88vF5GDdL3FZb5MurHKyTT6giltgfU5CF0KNwwRriz4RKHLSyWttw</con:value></con:property><con:property><con:name>SirenDestinataire</con:name><con:value>*********</con:value></con:property><con:property><con:name>RoutingCode</con:name><con:value>RoutCode01</con:value></con:property></con:properties><con:securityTest id="8a32572d-1cf6-42f0-b398-acefe3e8cee5" name="SecurityTest 1" failOnError="true"><con:settings/><con:properties/></con:securityTest></con:testCase><con:testCase id="0fcd8734-15cf-41f5-9eaa-4180a63d2ac3" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="Co-Respons:SirenNum&amp;Siret&amp;RoutingCode>-Response:Siren&amp;Siret" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="ee114123-1d33-4514-8dbf-de336ae3e25a"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="1ccf8040-216f-454c-8a25-ab092fbd965e"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="1fe22a79-10ad-4146-946c-8052b720a9cc"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="SiretNum" id="cd2b3350-d197-4f63-9ba1-e702d3812a55"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="cb6e3ef0-f939-4fbc-9648-314e1ca6d1dc" name="Contains"><con:configuration><token>"siretDestinataire":"*********00012"</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="*********00012"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="SirenTransfert" id="e1557855-3c8f-473f-bb0f-e562b500979e"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>siren-destinataire</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SiretNum</con:sourceStep><con:sourcePath>$[0].sirenDestinataire</con:sourcePath><con:targetType>SirenDestinataire</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:targetPath/><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="SirenNum" id="79cd0dc1-942b-4ba5-b483-90ad0236cfc7"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token>codeLigneAdressage</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#SirenDestinataire}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TransferRoutingCode" id="ceff897e-6aa5-4f41-84af-f93222c37c6f"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>code-routage-destinataie</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SirenNum</con:sourceStep><con:sourcePath>$[0].codeRoutageDestinataire</con:sourcePath><con:targetType>RoutingCode</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="RoutingCode" id="02f16fad-9cb5-42ac-8798-0c0d4599ef5e"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="RoutingCode" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="RoutingCode" id="dea67ace-36be-4ae6-bcfd-7a12055f063b" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="code-routage-destinataire" value="${#TestCase#RoutingCode}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>code-routage-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="ValidSiretNum" id="1e9c1653-2228-4757-8e89-47be91f08473"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SiretNum" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidSiretNum" id="9f3653a8-3d2a-416d-9c05-dfac3c606e7a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 10,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="c35a153d-b4dc-434b-885b-9e57f92a621b" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="cb6e3ef0-f939-4fbc-9648-314e1ca6d1dc" name="Contains"><con:configuration><token>"siretDestinataire":"*********00012"</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siret-destinataire" value="*********00012"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siret-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="ValidSirenTransfert" id="8227b36a-a8da-4bd6-9328-47f1133f86d0"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>siren-destinataire</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>SiretNum</con:sourceStep><con:sourcePath>$[0].sirenDestinataire</con:sourcePath><con:targetType>SirenDestinataire</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:targetPath/><con:type>JSONPATH</con:type><con:targetTransferType>XQUERY</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="ValidSirenNum" id="05297b86-5b96-434f-9db5-b776d1312b73"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidSirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token>codeLigneAdressage</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="${#TestCase#SirenDestinataire}"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="SearchSirenNum" id="0e93f859-7dd6-41fd-8bda-a344e251f3b0"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/v1/ligne-annuaire/recherche" methodName="SirenNumSearch" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="SearchSirenNum" id="b39fd29e-2736-422f-b042-4889c3f2af3a" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL}</con:endpoint><con:request>{
  "where": "string",
  "sort": "string",
  "fields": "string",
  "limit": 20,
  "offset": 0
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/v1/ligne-annuaire/recherche</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="cafecf3e-1748-443a-90f6-a7953824a479" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:assertion type="Simple Contains" id="018983e3-73f3-43ae-aedb-4951908c11d0" name="Contains"><con:configuration><token>codeLigneAdressage</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="siren-destinataire" value="1234"/>
  <con:entry key="format" value="JSON"/>
  <con:entry key="est-rech-code-routage-stricte" value="true"/>
</con:parameters><con:parameterOrder><con:entry>format</con:entry><con:entry>siren-destinataire</con:entry><con:entry>est-rech-code-routage-stricte</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="45194a87-1abb-4f50-9452-f8d0a2bac292" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>5</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:tearDownScript>// Code to execute the GenerateCSVReport test step
testRunner.testCase.testSuite.project.testSuites["Library"].testCases["Reporting_Utility"].
testSteps["GenerateCSVReport"].run(testRunner, context);</con:tearDownScript><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ioMoaVtlFLzkKAl9KzhV-BqC-iqoo5lv3hvcD5GIICAochRE0Hn8YCvvhqlnexwhQ_mhq5fEHn1FJxYqP6s5uVwaXJptZUfYCjHN46grb5BG0hH5l2ovVbvNEL9SarLyrzJRHvM4bjPxP4vjphkq1p8bq47HSSMOZ9ZOy11QZWdlZvPFx1FrH-H35zqAj2siW40rQdpLha9NELqeKgqHWVSwMrHjvS6YpgC4ezJj-sgDZwb7DMVxwhcMe6vbs_-eJbC1rcbMymf_O0AfzGiQFhJo759wtfS3ntd5hRsXxiWJUK_JDmz3qRlz2N2n4LJTAJWNG5IP9oXsyLPpUaOmZg</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ioMoaVtlFLzkKAl9KzhV-BqC-iqoo5lv3hvcD5GIICAochRE0Hn8YCvvhqlnexwhQ_mhq5fEHn1FJxYqP6s5uVwaXJptZUfYCjHN46grb5BG0hH5l2ovVbvNEL9SarLyrzJRHvM4bjPxP4vjphkq1p8bq47HSSMOZ9ZOy11QZWdlZvPFx1FrH-H35zqAj2siW40rQdpLha9NELqeKgqHWVSwMrHjvS6YpgC4ezJj-sgDZwb7DMVxwhcMe6vbs_-eJbC1rcbMymf_O0AfzGiQFhJo759wtfS3ntd5hRsXxiWJUK_JDmz3qRlz2N2n4LJTAJWNG5IP9oXsyLPpUaOmZg</con:value></con:property><con:property><con:name>SirenDestinataire</con:name><con:value>*********</con:value></con:property><con:property><con:name>RoutingCode</con:name><con:value>RoutCode01</con:value></con:property></con:properties><con:securityTest id="e48f350c-d58f-4635-8650-20a8d64e19b9" name="SecurityTest 1" failOnError="true"><con:settings/><con:properties/></con:securityTest></con:testCase><con:properties/></con:testSuite><con:testSuite id="c7338eeb-2fe2-41b6-99f4-284ce4878900" name="SIREN resource"><con:settings/><con:runType>SEQUENTIAL</con:runType><con:testCase id="b1bf3953-7bf1-4430-9198-4f41f2df47db" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="POST-GETSirenID-PUT-DELETE" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="b6aa275c-3317-4d90-8a4c-95965b145b84"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="12acca55-08f2-473f-869c-6a45204b5daa"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="bcebc108-e1c3-4959-8616-ffdc3beda8d8"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="[POST]-Siren" id="ae7a436c-285e-4566-9b71-e5009caf0cff"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens" methodName="Sirens 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="[POST]-Siren" id="74e36145-da5c-437e-9b4f-264a72a5b692" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
 
  "sirenNum": "SN12",
  "companyName": "SIREN12",
  "entityType": "PUBLIQUE",
  "modificationDate": "2023-11-10T09:38:34.652Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="22159bf5-dc21-4f3a-ac94-8e20f4ce81d8" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="SirenID" id="f9c36af7-ca0e-4da3-83a1-680833c7437c"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SirenID</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>[POST]-Siren</con:sourceStep><con:sourcePath>$.id</con:sourcePath><con:targetType>SirenID</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>sirendata</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>[POST]-Siren</con:sourceStep><con:sourcePath>$.sirenNum</con:sourcePath><con:targetType>SirenData</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="[Get]-SirenByID" id="69f6af3f-2384-406d-a2b9-d9f5822e9c86"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens/{id}" methodName="GetSirenByID" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="[Get]-SirenByID" id="5becf1d0-e532-4404-84d0-c022ac986bad" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Tenant" value="${#Project#Tenant}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="8456c535-45eb-4be2-b38e-4d5f4bf14af7" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="${#TestCase#SirenID}" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry><con:entry/></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="[PUT]-Siren" id="0696ce75-0793-49dd-bef7-49f57a53906e"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens/{id}" methodName="PUT Siren" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="[PUT]-Siren" id="22dd9eca-9132-4547-a2cd-818373c3d1ad" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Tenant" value="${#Project#Tenant}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
   "id":${#TestCase#SirenID},
   "sirenNum": "SNmod",
   "companyName": "SIREN7",
   "entityType": "PUBLIQUE",
   "modificationDate": "2023-11-10T09:38:34.652Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/%7B%23Testcase%23SirenID%7D</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="d7cd07ef-3661-4669-829e-e25c001781da" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="${#TestCase#SirenID}" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="[Delete]-siren" id="71fc6b92-da13-4827-b784-28aeb0593c26"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens/{id}" methodName="DeleteSiren" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="[Delete]-siren" id="8fba604a-a061-4fea-95c7-af7cba3db176" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Tenant" value="${#Project#Tenant}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/1224</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="6c02fe4b-621c-49ad-99db-8e915776c7b2" name="Valid HTTP Status Codes"><con:configuration><codes>204</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="${#TestCase#SirenID}" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry><con:entry/></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="35ccb0b7-19ef-4ead-8fa0-4e18963180c9" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>50</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FgbYznD_OHSXt60aIJvjT44Hv5niPzB2Ln_p_QHTqfeXCC70x9YZAX3z_OnOcZDuiAaoYSX_5Ss8xryJsiroWEKTzpAbIFJ6vfQwYc7ml9-LdWE9lbss0XzC5ypn4ec6WcXGzX1ZCP4i4TtfXBVPVVHr5BQ86m2kb59jYJsMlBysVYf9RE5ZFaLPdFddrOcCWqZFdB-2CVKnNaLiz9QRKKhWptUbZ_epN84G2wR35Fb5TWoZhSPnwnZHVCtZQGL8pwJ-z2aKRZs5rj7ZTQEVpt5W-UYdLKigAIaNEB_uruk7EBJrhm__7EQ6nG4tZC2pq0lJLzUHGWGNKmOj1WO4_w</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FgbYznD_OHSXt60aIJvjT44Hv5niPzB2Ln_p_QHTqfeXCC70x9YZAX3z_OnOcZDuiAaoYSX_5Ss8xryJsiroWEKTzpAbIFJ6vfQwYc7ml9-LdWE9lbss0XzC5ypn4ec6WcXGzX1ZCP4i4TtfXBVPVVHr5BQ86m2kb59jYJsMlBysVYf9RE5ZFaLPdFddrOcCWqZFdB-2CVKnNaLiz9QRKKhWptUbZ_epN84G2wR35Fb5TWoZhSPnwnZHVCtZQGL8pwJ-z2aKRZs5rj7ZTQEVpt5W-UYdLKigAIaNEB_uruk7EBJrhm__7EQ6nG4tZC2pq0lJLzUHGWGNKmOj1WO4_w</con:value></con:property><con:property><con:name>SirenResponse</con:name><con:value>{"id":1752,"sirenNum":"SN3","companyName":"SIREN3","entityType":"PUBLIQUE","modificationDate":"2023-11-10T09:38:34.652Z"}</con:value></con:property><con:property><con:name>SirenData</con:name><con:value>SN12</con:value></con:property><con:property><con:name>AdressLineID</con:name><con:value>1727</con:value></con:property><con:property><con:name>SirenID</con:name><con:value>24522600</con:value></con:property></con:properties></con:testCase><con:testCase id="6dbfa10b-8e7c-469b-942a-43e21b1400ad" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="POST-PATCH-GETsirenID" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="7507669d-5e27-4e67-a9a0-945864f4d325"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="fa636049-28d8-401c-980e-be4fd77d37a8"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="c9e65bfb-5e73-4924-aeca-ed76ba173861"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="[POST]-Siren" id="61c4d557-73ca-40b7-a921-6ac114c9e7a0"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens" methodName="Sirens 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="[POST]-Siren" id="74e36145-da5c-437e-9b4f-264a72a5b692" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
 
  "sirenNum": "SN20",
  "companyName": "SIREN20",
  "entityType": "PUBLIQUE",
  "modificationDate": "2023-11-10T09:38:34.652Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="22159bf5-dc21-4f3a-ac94-8e20f4ce81d8" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="SirenID" id="b1c2f1c8-cd17-48d2-9e0e-73b247c8b09f"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SirenID</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>[POST]-Siren</con:sourceStep><con:sourcePath>$.id</con:sourcePath><con:targetType>SirenID</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>sirendata</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>[POST]-Siren</con:sourceStep><con:sourcePath>$.sirenNum</con:sourcePath><con:targetType>SirenData</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="restrequest" name="[Get]-SirenByID" id="35ebd6a8-6daa-498a-b9ff-f91b42636ded"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens/{id}" methodName="GetSirenByID" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="[Get]-SirenByID" id="5becf1d0-e532-4404-84d0-c022ac986bad" mediaType="application/json"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Tenant" value="${#Project#Tenant}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request/><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="8456c535-45eb-4be2-b38e-4d5f4bf14af7" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="${#TestCase#SirenID}" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry><con:entry/></con:parameterOrder></con:restRequest></con:config></con:testStep><con:testStep type="restrequest" name="[PATCH]-siren" id="ee2ac5b8-de13-4135-a538-844d160b91f1"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens/{id}" methodName="Patch-siren" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="[PATCH]-siren" id="4c50d386-e89b-4154-8b26-481d7a1125bd" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
  "id":${#TestCase#SirenID},
  "sirenNum": "sirenPATCHED",
   "companyName": "SIREN14",
   "entityType": "PUBLIQUE",
   "modificationDate": "2023-11-10T09:38:34.652Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens/</con:originalUri><con:assertion type="Simple Contains" id="34fcd0c7-6d4f-4d84-b1b6-4979a1cdc323" name="Contains"><con:configuration><token>204</token><ignoreCase>false</ignoreCase><useRegEx>false</useRegEx></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters><entry key="id" value="${#TestCase#SirenID}" xmlns="http://eviware.com/soapui/config"/></con:parameters><con:parameterOrder><con:entry>id</con:entry></con:parameterOrder></con:restRequest></con:config></con:testStep><con:loadTest id="5ac8bb59-77b3-4716-9b77-6d2f41e912d6" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>50</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SIXKZFzp_P9x9uwwJ9176hsBUmp6u-11xFUcsm9tmFnZFi0PvBt2Wx_kiL1s0VHJPxu_7fekMoKsOrvW_H4lEBnU3Bft6ghl3yMmgii9nmqUCvXicR_UhaEnMSZEgjVXXA9BIrrH7JavlUymTj49AUGvjGe3Zxp6tWfDxCCtKwPSv5pOlEbh-eM9icQTTJHXGae0CaOU9U5FdW5EkDVBju8uHyhN5su-vOxQkG0skQS-k-Lhqjf1cJJUPNn24r8wNOKWZS3_yhlav4e30DxOI3FZBk-wzHpvd0LaABODCGx1xLkDT9jlJVhBj5Hg1ZTl3lW_3_Oen5HTuy-iY-3EMw</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SIXKZFzp_P9x9uwwJ9176hsBUmp6u-11xFUcsm9tmFnZFi0PvBt2Wx_kiL1s0VHJPxu_7fekMoKsOrvW_H4lEBnU3Bft6ghl3yMmgii9nmqUCvXicR_UhaEnMSZEgjVXXA9BIrrH7JavlUymTj49AUGvjGe3Zxp6tWfDxCCtKwPSv5pOlEbh-eM9icQTTJHXGae0CaOU9U5FdW5EkDVBju8uHyhN5su-vOxQkG0skQS-k-Lhqjf1cJJUPNn24r8wNOKWZS3_yhlav4e30DxOI3FZBk-wzHpvd0LaABODCGx1xLkDT9jlJVhBj5Hg1ZTl3lW_3_Oen5HTuy-iY-3EMw</con:value></con:property><con:property><con:name>SirenResponse</con:name><con:value>{"id":1752,"sirenNum":"SN3","companyName":"SIREN3","entityType":"PUBLIQUE","modificationDate":"2023-11-10T09:38:34.652Z"}</con:value></con:property><con:property><con:name>SirenData</con:name><con:value>SN20</con:value></con:property><con:property><con:name>AdressLineID</con:name><con:value>1727</con:value></con:property><con:property><con:name>SirenID</con:name><con:value>24522601</con:value></con:property></con:properties></con:testCase><con:testCase id="32b792d2-5b7c-4d81-b8c9-807124366ab1" failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="POST-LOOP-GETsiren-GETsiren/Count" searchProperties="true"><con:settings/><con:testStep type="restrequest" name="ValidCredentials" id="c41055c7-d7f3-4f82-9c51-f6fac4815250"><con:settings/><con:config service="https://auth.staging.apps.generix.biz" resourcePath="/realms/${#Project#Tenant}/protocol/openid-connect/token" methodName="Token 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="ValidCredentials" id="d385e7eb-e9a7-4649-ab19-93648d9afa5b" mediaType="application/x-www-form-urlencoded" postQueryString="true"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:endpoint>${#Project#TokenURL}</con:endpoint><con:request/><con:originalUri>https://auth.staging.apps.generix.biz/auth/realms/legalreferential/protocol/openid-connect/token</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="7bf5b912-10f5-4fac-9aba-f997d858c25f" name="Valid HTTP Status Codes"><con:configuration><codes>200</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters>
  <con:entry key="password" value="Generix@123"/>
  <con:entry key="grant_type" value="password"/>
  <con:entry key="client_secret" value="NavQ0cN2h7PtTCyKR8kcgUNav0bmiNen"/>
  <con:entry key="client_id" value="legalref_app"/>
  <con:entry key="username" value="legalref_admin"/>
</con:parameters></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="TokenGenerating" id="89ea551c-c468-4613-9991-87ec5f5484dc"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>Authorization</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>ValidCredentials</con:sourceStep><con:sourcePath>$.access_token</con:sourcePath><con:targetType>Token</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:testStep type="groovy" name="Bearer+TokenGenerating" id="e42e02ae-b813-4c31-a866-e0b5870eb521"><con:settings/><con:config><script>def token = testRunner.testCase.getPropertyValue("Token")
log.info("Token extrait: ${token}")

if (token) {
    def bearerToken = "Bearer " + token
    log.info("BearerToken généré: ${bearerToken}")
    testRunner.testCase.setPropertyValue("BearerToken", bearerToken)
    
}
</script></con:config></con:testStep><con:testStep type="restrequest" name="[POST]-Siren" id="09cfdc35-747c-4296-83ae-1f674374c13c"><con:settings/><con:config service="http://legal-referential.chassagne-qa.generixgroup.com" resourcePath="/api/sirens" methodName="Sirens 1" xsi:type="con:RestRequestStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:restRequest name="[POST]-Siren" id="74e36145-da5c-437e-9b4f-264a72a5b692" mediaType="application/json" postQueryString="false"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment xmlns:con="http://eviware.com/soapui/config">&#13;
  &lt;con:entry key="Authorization" value="${#TestCase#BearerToken}"/>&#13;
  &lt;con:entry key="Accept" value="*/*"/>&#13;
  &lt;con:entry key="tenant" value="${#Project#Tenant}"/>&#13;
&lt;/xml-fragment></con:setting></con:settings><con:endpoint>${#Project#baseURL2}</con:endpoint><con:request>{
 
  "sirenNum": "SN12",
  "companyName": "SIREN12",
  "entityType": "PUBLIQUE",
  "modificationDate": "2023-11-10T09:38:34.652Z"
}</con:request><con:originalUri>http://legal-referential.chassagne-qa.generixgroup.com/api/sirens</con:originalUri><con:assertion type="Valid HTTP Status Codes" id="22159bf5-dc21-4f3a-ac94-8e20f4ce81d8" name="Valid HTTP Status Codes"><con:configuration><codes>201</codes></con:configuration></con:assertion><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:parameters/></con:restRequest></con:config></con:testStep><con:testStep type="transfer" name="SirenID" id="a15535b3-4d72-4439-9f7c-5e12a863c2aa"><con:settings/><con:config xsi:type="con:PropertyTransfersStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>SirenID</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>[POST]-Siren</con:sourceStep><con:sourcePath>$.id</con:sourcePath><con:targetType>SirenID</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers><con:transfers setNullOnMissingSource="true" transferTextContent="true" failOnError="true" ignoreEmpty="false" transferToAll="false" entitize="false" transferChildNodes="false"><con:name>sirendata</con:name><con:sourceType>Response</con:sourceType><con:sourceStep>[POST]-Siren</con:sourceStep><con:sourcePath>$.sirenNum</con:sourcePath><con:targetType>SirenData</con:targetType><con:targetStep>#TestCase#</con:targetStep><con:type>JSONPATH</con:type><con:targetTransferType>JSONPATH</con:targetTransferType><con:upgraded>true</con:upgraded></con:transfers></con:config></con:testStep><con:loadTest id="415e82c8-3081-4f7b-8ee7-386eb4eb8a38" name="LoadTest 1"><con:settings><con:setting id="HttpSettings@close-connections">false</con:setting></con:settings><con:threadCount>50</con:threadCount><con:startDelay>0</con:startDelay><con:sampleInterval>250</con:sampleInterval><con:calculateTPSOnTimePassed>true</con:calculateTPSOnTimePassed><con:resetStatisticsOnThreadCountChange>true</con:resetStatisticsOnThreadCountChange><con:historyLimit>-1</con:historyLimit><con:testLimit>60</con:testLimit><con:limitType>TIME</con:limitType><con:loadStrategy><con:type>Simple</con:type></con:loadStrategy><con:assertion type="Step Status" name="Step Status"/><con:maxAssertionErrors>100</con:maxAssertionErrors><con:cancelExcessiveThreads>true</con:cancelExcessiveThreads><con:strategyInterval>500</con:strategyInterval></con:loadTest><con:properties><con:property><con:name>Token</con:name><con:value>eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NpWHNpbiEk4pCsEw7XCh0iLDqm6Jfg_gO1Zk6mws0yTwqaOTjgby9zuRI2gFWw68SqNfOegHcmxMc45lpXElic6FgkKFxxkziYkCUPF29pag8feX7XCz0Spus3kZHVA-NqEYw-fuc5fV3mTq00N7cBDYTeRazy0VoFhZ7zOAYn38_EbaZ1Fl5vPryRPiskpABTBcMdeQNf-4qfCc-eftQbZ1Me6LqeQkGEcaZsHW4jZPmpmA0DRJc9Gdw7aKtjrSUuvt8UrQH6XW9WJboqD7JDUS17nAezQFqSb4iUuxsBmguqlmaT0VNCfl6lO4i3OsSNExtFkNvqp-W9bRFyr7FQ</con:value></con:property><con:property><con:name>BearerToken</con:name><con:value>Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsRHNuOWxReGQ5cjdYQm1ZS0JJUG9aMGJaQ3lHR1poc0VWeTd4WmhlVmVnIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NpWHNpbiEk4pCsEw7XCh0iLDqm6Jfg_gO1Zk6mws0yTwqaOTjgby9zuRI2gFWw68SqNfOegHcmxMc45lpXElic6FgkKFxxkziYkCUPF29pag8feX7XCz0Spus3kZHVA-NqEYw-fuc5fV3mTq00N7cBDYTeRazy0VoFhZ7zOAYn38_EbaZ1Fl5vPryRPiskpABTBcMdeQNf-4qfCc-eftQbZ1Me6LqeQkGEcaZsHW4jZPmpmA0DRJc9Gdw7aKtjrSUuvt8UrQH6XW9WJboqD7JDUS17nAezQFqSb4iUuxsBmguqlmaT0VNCfl6lO4i3OsSNExtFkNvqp-W9bRFyr7FQ</con:value></con:property><con:property><con:name>SirenResponse</con:name><con:value>{"id":1752,"sirenNum":"SN3","companyName":"SIREN3","entityType":"PUBLIQUE","modificationDate":"2023-11-10T09:38:34.652Z"}</con:value></con:property><con:property><con:name>SirenData</con:name><con:value>SN12</con:value></con:property><con:property><con:name>AdressLineID</con:name><con:value>1727</con:value></con:property><con:property><con:name>SirenID</con:name><con:value>10688</con:value></con:property></con:properties></con:testCase><con:properties/></con:testSuite><con:properties><con:property><con:name>baseURL</con:name><con:value>http://legal-referential.chassagne-qa.generixgroup.com</con:value></con:property><con:property><con:name>TokenURL</con:name><con:value>https://auth.staging.apps.generix.biz/auth</con:value></con:property><con:property><con:name>Tenant</con:name><con:value>legalreferential1</con:value></con:property><con:property><con:name>baseURL2</con:name><con:value>https://legalref.dev.apps.generix.biz</con:value></con:property></con:properties><con:wssContainer/><con:oAuth2ProfileContainer/><con:oAuth1ProfileContainer/><con:sensitiveInformation/></con:soapui-project>