pipeline {
    agent any

    parameters {
        choice(name: 'DEPLOY_TYPE', choices: ['snapshot', 'qualif', 'release'], description: 'Deployment Type')
        string(name: 'VERSION', defaultValue: '', description: 'Version to release (required for qualif and release)')
    }

    stages {
        stage('Initialize') {
            steps {
                script {
                    echo "Starting pipeline with DEPLOY_TYPE: ${DEPLOY_TYPE}"
                    if ((DEPLOY_TYPE == 'qualif' || DEPLOY_TYPE == 'release') && !params.VERSION) {
                        error "VERSION parameter is required for ${DEPLOY_TYPE} deployment"
                    }
                }
            }
        }

        stage('Checkout') {
            steps {
                git credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a',
                    url: 'http://git.chassagne-scm.generixgroup.com/generix/legal-referential'
                sh 'git checkout ${BRANCH_NAME}'
            }
        }

        stage('Configure Git') {
            steps {
                script {
                    sh """
                        git config user.email 'jen<PERSON>@generixgroup.com'
                        git config user.name 'Jenkins'
                    """
                }
            }
        }

        stage('Build') {
            when {
                expression { DEPLOY_TYPE != 'release' }
            }
            tools {
                maven 'Maven3.6.3'
            }
            steps {
                sh 'mvn clean package -DskipTests=true'
            }
        }

        stage('Run Tests') {
            when {
                expression { DEPLOY_TYPE != 'release' }
            }
            tools {
                maven 'Maven3.6.3'
            }
            steps {
                script {
                    try {
                        sh 'mvn test'
                    } catch (err) {
                        echo "Tests failed, but continuing with pipeline. Error: ${err}"
                    }
                }
            }
        }

        stage('Prepare Version') {
            when {
                expression { DEPLOY_TYPE == 'qualif' }
            }
            tools {
                maven 'Maven3.6.3'  // Add Maven tool configuration
            }
            steps {
                script {
                    // Extract major.minor from version (e.g., 2.2 from 2.2.1)
                    def versionParts = params.VERSION.split('\\.')
                    def branchVersion = "${versionParts[0]}.${versionParts[1]}"

                    echo "Preparing version ${params.VERSION} on branch qualif/${branchVersion}"

                    withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a',
                        usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
                        // Use single quotes to prevent Groovy string interpolation
                        sh '''
                    git remote set-url origin "http://${GIT_USERNAME}:${GIT_PASSWORD}@git.chassagne-scm.generixgroup.com/generix/legal-referential"

                    if git show-ref --verify --quiet "refs/heads/qualif/''' + branchVersion + '''"; then
                        echo "Branch qualif/''' + branchVersion + ''' exists. Checking out..."
                        git checkout "qualif/''' + branchVersion + '''"
                    else
                        echo "Creating new branch qualif/''' + branchVersion + '''"
                        git checkout -b "qualif/''' + branchVersion + '''"
                    fi

                    # Update version in pom.xml
                    mvn versions:set -DnewVersion=''' + params.VERSION + ''' -DgenerateBackupFiles=false

                    # Check if there are changes to commit
                    if git diff --quiet; then
                        echo "No changes to pom.xml"
                    else
                        git add pom.xml
                        git commit -m "Update version to ''' + params.VERSION + '''"
                        git push origin "qualif/''' + branchVersion + '''"
                    fi
                '''
                    }
                }
            }
        }

        stage('Deploy to Nexus') {
            tools {
                maven 'Maven3.6.3'
            }
            steps {
                script {
                    def nexusBase = 'https://nexus.chassagne-repo.generixgroup.com/content/repositories'
                    def artifactId = 'legal-referential'
                    def groupId = 'com.generix'
                    def packaging = 'jar'

                    def sourceRepo = "legalrefToQualif"
                    def targetRepo = "releasesLegal-Ref"
                    def artifactPath = "${groupId.replace('.', '/')}/${artifactId}/${params.VERSION}/${artifactId}-${params.VERSION}.${packaging}"
                    def sourceUrl = "${nexusBase}/${sourceRepo}/${artifactPath}"
                    def targetUrl = "${nexusBase}/${targetRepo}/${artifactPath}"

                    withCredentials([usernamePassword(credentialsId: 'nexus-password',
                        usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {

                        // Step 1: Download artifact from the source repository
                        echo "Downloading artifact from: ${sourceUrl}"
                        sh """
                            curl -k -u \$USERNAME:\$PASSWORD \
                                -O ${sourceUrl}
                        """

                        // Step 2: Verify the download
                        def artifactFilename = "${artifactId}-${params.VERSION}.${packaging}"
                        def fileExists = sh(script: "test -f ${artifactFilename} && echo 'true' || echo 'false'", returnStdout: true).trim()
                        if (fileExists == 'false') {
                            error "Failed to download artifact: ${artifactFilename}"
                        }
                        echo "Artifact downloaded successfully: ${artifactFilename}"

                        // Step 3: Upload artifact to the target repository
                        echo "Uploading artifact to: ${targetUrl}"
                        sh """
                            curl -k -u \$USERNAME:\$PASSWORD \
                                --upload-file ${artifactFilename} \
                                ${targetUrl}
                        """

                        // Step 4: Verify the upload
                        echo "Verifying upload to: ${targetUrl}"
                        sh """
                            curl -k -I -u \$USERNAME:\$PASSWORD \
                                ${targetUrl}
                        """
                    }
                }
            }
        }

       stage('Deploy to Nexus - Move Artifact') {
           steps {
               script {
                   def nexusApiBase = 'https://nexus.chassagne-repo.generixgroup.com/service/rest/v1'
                   def sourceRepo = 'legalrefToQualif'
                   def targetRepo = 'releasesLegal-Ref'
                   def artifactPath = 'com/generix/legal-referential/2.2.1/legal-referential-2.2.1.jar'

                   echo "Attempting to move artifact from ${sourceRepo} to ${targetRepo}"

                   // Request for moving the artifact
                   withCredentials([usernamePassword(credentialsId: 'nexus-password',
                       usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {

                       sh """
                           curl -k -X POST \
                               --user "\$USERNAME:\$PASSWORD" \
                               --header "Content-Type: application/json" \
                               --data '{
                                   "source": "${sourceRepo}",
                                   "destination": "${targetRepo}",
                                   "path": "${artifactPath}"
                               }' \
                               "${nexusApiBase}/staging/move"
                       """
                   }

                   echo "Move request completed for artifact: ${artifactPath}"
               }
           }
       }

        stage('Create Tag') {
            when {
                expression { DEPLOY_TYPE == 'qualif' }
            }
            steps {
                withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a',
                    usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
                    sh """
                        git remote set-url origin 'http://${GIT_USERNAME}:${GIT_PASSWORD}@git.chassagne-scm.generixgroup.com/generix/legal-referential'
                        git tag -a legel-ref-${params.VERSION} -m 'Version ${params.VERSION}'
                        git push origin legel-ref-${params.VERSION}
                    """
                }
            }
        }
    }

stage('Deploy to Nexus - Move Folder') {
    steps {
        script {
            def nexusBase = 'https://nexus.chassagne-repo.generixgroup.com/content/repositories'
            def sourceRepo = 'legalrefToQualif'
            def targetRepo = 'releasesLegal-Ref'
            def folderPath = "com/generix/legal-referential/${params.VERSION}" // Path to the version folder
            def sourceURL = "${nexusBase}/${sourceRepo}/${folderPath}"
            def targetURL = "${nexusBase}/${targetRepo}/${folderPath}"

            echo "Moving folder: ${folderPath} from ${sourceRepo} to ${targetRepo}"

            withCredentials([usernamePassword(credentialsId: 'nexus-password',
                usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {

                // Step 1: Download the entire folder from the source repository
                echo "Downloading folder from: ${sourceURL}"
                sh """
                    mkdir -p ${params.VERSION}
                    curl -k -u "\$USERNAME:\$PASSWORD" --remote-name-all \
                        ${sourceURL}/*
                """

                // Step 2: Verify that files are downloaded
                def folderExists = sh(script: "ls -1 ${params.VERSION} | wc -l", returnStdout: true).trim()
                if (folderExists == "0") {
                    error "Failed to download files from folder: ${sourceURL}"
                }
                echo "Downloaded folder successfully: ${sourceURL}"

                // Step 3: Upload the entire folder to the target repository
                echo "Uploading folder to: ${targetURL}"
                sh """
                    curl -k -u "\$USERNAME:\$PASSWORD" --upload-file ${params.VERSION}/* \
                        ${targetURL}/
                """

                // Step 4: Clean up the source folder to simulate a move
                echo "Deleting folder in source repository: ${sourceURL}"
                sh """
                    curl -k -X DELETE \
                        -u "\$USERNAME:\$PASSWORD" \
                        ${sourceURL}
                """
            }
        }
    }
}

    post {
        always {
            cleanWs()
        }
        success {
            echo "Pipeline completed successfully!"
        }
        failure {
            echo "Pipeline failed! Please check the logs for details."
        }
    }
}
