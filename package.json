{"name": "legal-referential", "version": "2.2.0-SNAPSHOT", "private": true, "description": "Description for legalReferential", "license": "UNLICENSED", "scripts": {"app:start": "./mvnw", "backend:build-cache": "./mvnw dependency:go-offline", "backend:debug": "./mvnw -Dspring-boot.run.jvmArguments=\"-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000\"", "backend:doc:test": "./mvnw -ntp javadoc:javadoc --batch-mode", "backend:info": "./mvnw -ntp enforcer:display-info --batch-mode", "backend:nohttp:test": "./mvnw -ntp checkstyle:check --batch-mode", "backend:start": "./mvnw", "backend:unit:test": "./mvnw -ntp verify --batch-mode -Dlogging.level.ROOT=OFF -Dlogging.level.org.zalando=OFF -Dlogging.level.tech.jhipster=OFF -Dlogging.level.com.generix.legalreferential=OFF -Dlogging.level.org.springframework=OFF -Dlogging.level.org.springframework.web=OFF -Dlogging.level.org.springframework.security=OFF", "ci:backend:test": "npm run backend:info && npm run backend:doc:test && npm run backend:nohttp:test && npm run backend:unit:test -- -P$npm_package_config_default_environment", "ci:e2e:package": "npm run java:$npm_package_config_packaging:$npm_package_config_default_environment -- -Pe2e -Denforcer.skip=true", "ci:e2e:prepare": "npm run ci:e2e:prepare:docker", "ci:e2e:prepare:docker": "npm run docker:db:up && npm run docker:others:up && docker ps -a", "preci:e2e:server:start": "npm run docker:db:await --if-present && npm run docker:others:await --if-present", "ci:e2e:server:start": "java -jar target/e2e.$npm_package_config_packaging --spring.profiles.active=e2e,$npm_package_config_default_environment -Dlogging.level.ROOT=OFF -Dlogging.level.org.zalando=OFF -Dlogging.level.tech.jhipster=OFF -Dlogging.level.com.generix.legalreferential=OFF -Dlogging.level.org.springframework=OFF -Dlogging.level.org.springframework.web=OFF -Dlogging.level.org.springframework.security=OFF --logging.level.org.springframework.web=ERROR", "ci:e2e:teardown": "npm run ci:e2e:teardown:docker", "ci:e2e:teardown:docker": "npm run docker:db:down --if-present && npm run docker:others:down && docker ps -a", "docker:app:up": "docker-compose -f src/main/docker/app.yml up -d", "docker:db:down": "docker-compose -f src/main/docker/postgresql.yml down -v", "docker:db:up": "docker-compose -f src/main/docker/postgresql.yml up -d", "docker:others:await": "", "docker:others:down": "", "predocker:others:up": "", "docker:others:up": "", "java:docker": "./mvnw -ntp verify -DskipTests -Pprod jib:dockerBuild", "java:docker:arm64": "npm run java:docker -- -Djib-maven-plugin.architecture=arm64", "java:docker:dev": "npm run java:docker -- -Pdev,webapp", "java:docker:prod": "npm run java:docker -- -P<PERSON>rod", "java:jar": "./mvnw -ntp verify -DskipTests --batch-mode", "java:jar:dev": "npm run java:jar -- -Pdev,webapp", "java:jar:prod": "npm run java:jar -- -P<PERSON>rod", "java:war": "./mvnw -ntp verify -DskipTests --batch-mode -Pwar", "java:war:dev": "npm run java:war -- -Pdev,webapp", "java:war:prod": "npm run java:war -- -P<PERSON>rod", "prepare": "husky install", "prettier:check": "prettier --check \"{,src/**/,.blueprint/**/}*.{md,json,yml,html,java}\"", "prettier:format": "prettier --write \"{,src/**/,.blueprint/**/}*.{md,json,yml,html,java}\""}, "config": {"backend_port": "8080", "default_environment": "prod", "packaging": "jar"}, "devDependencies": {"generator-jhipster": "7.9.3", "husky": "7.0.4", "lint-staged": "13.0.3", "prettier": "2.7.1", "prettier-plugin-java": "1.6.2", "prettier-plugin-packagejson": "2.2.18"}, "engines": {"node": ">=16.17.0"}, "cacheDirectories": ["node_modules"]}