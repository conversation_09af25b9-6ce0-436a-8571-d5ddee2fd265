<?xml version="1.0" encoding="UTF-8"?>
<settings>
  <activeProfiles>
    <activeProfile>release</activeProfile>
  </activeProfiles>
  <profiles>
    <profile>
      <id>release</id>
      <repositories>
        <repository>
          <id>snapshotsForLegalref</id>
          <name>Generix Snapshots for Legal Ref</name>
          <url>
            https://nexus.chassagne-repo.generixgroup.com/content/repositories/Legal-ref-Snapshots
          </url>
          <snapshots>
            <enabled>true</enabled>
            <updatePolicy>daily</updatePolicy>
          </snapshots>
          <releases>
            <enabled>false</enabled>
          </releases>
        </repository>
        <repository>
          <id>releasesLegalRef</id>
          <name>Generix Nexus Repository for legal Ref</name>
          <url>
            https://nexus.chassagne-repo.generixgroup.com/content/repositories/releasesLegal-Ref
          </url>
          <snapshots>
            <enabled>false</enabled>
            <updatePolicy>daily</updatePolicy>
          </snapshots>
          <releases>
            <enabled>true</enabled>
          </releases>
        </repository>
        <repository>
          <id>tokenValidator-nexus-snapshot-repository</id>
          <name>tokenValidator Nexus snapshot Repository</name>
          <url>
            https://nexus.chassagne-repo.generixgroup.com/content/repositories/tokenValidatorSnapshotRepository
          </url>
          <snapshots>
            <enabled>true</enabled>
            <updatePolicy>daily</updatePolicy>
          </snapshots>
          <releases>
            <enabled>false</enabled>
          </releases>
        </repository>
        <repository>
          <id>tokenValidator-nexus-release-repository</id>
          <name>tokenValidator Nexus release Repository</name>
          <url>
            https://nexus.chassagne-repo.generixgroup.com/content/repositories/tokenValidatorRelease
          </url>
          <snapshots>
            <enabled>false</enabled>
            <updatePolicy>daily</updatePolicy>
          </snapshots>
          <releases>
            <enabled>true</enabled>
          </releases>
        </repository>
      </repositories>
    </profile>
    <profile>
      <id>qualif</id>
      <repositories>
        <repository>
          <id>snapshotsForLegalref</id>
          <name>Generix Snapshots for Legal Ref</name>
          <url>
            https://nexus.chassagne-repo.generixgroup.com/content/repositories/Legal-ref-Snapshots
          </url>
          <snapshots>
            <enabled>true</enabled>
            <updatePolicy>daily</updatePolicy>
          </snapshots>
          <releases>
            <enabled>false</enabled>
          </releases>
        </repository>
        <repository>
          <id>legalRefToQualify</id>
          <name>Generix Angular Not Qualified Versions</name>
          <url>https://nexus.chassagne-repo.generixgroup.com/content/repositories/legalrefToQualif
          </url>
          <snapshots>
            <enabled>false</enabled>
            <updatePolicy>daily</updatePolicy>
          </snapshots>
          <releases>
            <enabled>true</enabled>
          </releases>
        </repository>
      </repositories>
    </profile>
  </profiles>
  <servers>
    <server>
      <id>snapshotsForLegalref</id>
      <username>%USER%</username>
      <password>%PASSWORD%</password>
    </server>
    <server>
      <id>releasesLegalRef</id>
      <username>%USER%</username>
      <password>%PASSWORD%</password>
    </server>
    <server>
      <id>legalRefToQualify</id>
      <username>%USER%</username>
      <password>%PASSWORD%</password>
    </server>
    <server>
      <id>tokenValidator-nexus-snapshot-repository</id>
      <username>%USER%</username>
      <password>%PASSWORD%</password>
    </server>
    <server>
      <id>tokenValidator-nexus-release-repository</id>
      <username>%USER%</username>
      <password>%PASSWORD%</password>
    </server>
    <server>
      <id>nexus</id>
      <username>%USER%</username>
      <password>%PASSWORD%</password>
    </server>
  </servers>
</settings>
