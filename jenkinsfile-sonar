pipeline {
    agent any
    environment{
        SONARQUBE_TOKEN = credentials('SonarToken') // <PERSON> credentials ID for SonarQube token
        SONARQUBE_URL = 'http://************:9003'
        PROJECT_KEY = 'generix_legal-referential_AY-6y0Pj_UiVIgZPRs24'
    }

    tools {
        maven 'Maven3.6.3'
    }
    stages {
        stage('Git Checkout') {
            steps {
                checkout scm       
            }
        }
        stage("SonarQube Analysis") {
            steps {
                withSonarQubeEnv('Sonarqube') {
                    sh 'mvn clean package sonar:sonar -Dsonar.projectKey=generix_legal-referential_AY-6y0Pj_UiVIgZPRs24'
                }
            }
        }
        //stage('Fetch SonarQube Report') {
            //steps {
                //script {
                    // Extract the branch name by removing the 'origin/' prefix
                    //def fullBranchName = env.GIT_BRANCH
                    // Extract the branch name by removing the 'origin/' prefix
                    //def branchName = fullBranchName.replace('origin/', '')
                    // Print the branch name
                    //echo "Branch name: ${branchName}"

                    //def apiUrl = "${env.SONARQUBE_URL}/api/issues/search?componentKeys=${env.PROJECT_KEY}&types=CODE_SMELL,BUG,VULNERABILITY&statuses=OPEN&ps=500"

                    // Capture the curl command output into a variable
                    //def jsonOutput = sh(script: "curl -u ${env.SONARQUBE_TOKEN}: ${apiUrl}", returnStdout: true).trim()

                    // Save the JSON output to a file
                    //writeFile file: 'sonar-report.json', text: jsonOutput

                    // Configure Git
                    //sh """
                        //git config user.name "jenkins"
                        //git config user.email "<EMAIL>"

                        //git checkout -b ${branchName}

                        //git add sonar-report.json
                        //git commit -m "Add SonarQube report"

                        //git push origin ${branchName}
                        //"""
                //}
            //}
        //}

        stage("Quality gate") {
            steps {
                timeout(time: 2, unit: 'MINUTES') {
                    script {
                        def qg = waitForQualityGate()
                        if (qg == null) {
                            error "Pipeline aborted due to timeout waiting for quality gate"
                        }
                        if (qg.status != 'OK') {
                            error "Pipeline aborted due to quality gate failure: ${qg.status}"
                        }
                    }
                }
            }
        }

    }
    post {
        always {
            cleanWs()
        }
    }

}

