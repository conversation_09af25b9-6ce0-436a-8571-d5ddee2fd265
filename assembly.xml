<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.2.0 http://maven.apache.org/xsd/assembly-2.2.0.xsd">
    <id>distribution</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <!-- Include the JAR file -->
        <fileSet>
            <directory>target</directory>
            <outputDirectory>/app</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>

        <!-- Include only YAML files starting with "application" from src/main/resources/config -->
        <fileSet>
            <directory>src/main/resources/config</directory>
            <outputDirectory>/setup/config</outputDirectory>
            <includes>
                <include>application-*.yml</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>
