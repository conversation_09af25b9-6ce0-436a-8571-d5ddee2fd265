{"total": 170, "p": 1, "ps": 100, "paging": {"pageIndex": 1, "pageSize": 100, "total": 170}, "effortTotal": 1307, "issues": [{"key": "AZGZMyG74raP-Qiy0k7n", "rule": "java:S3776", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 154, "hash": "23b2ff57684e8c4a841930388c9bba06", "textRange": {"startLine": 154, "endLine": 154, "startOffset": 17, "endOffset": 27}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 159, "endLine": 159, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 159, "endLine": 159, "startOffset": 29, "endOffset": 31}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 165, "endLine": 165, "startOffset": 9, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 169, "endLine": 169, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 175, "endLine": 175, "startOffset": 62, "endOffset": 63}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 179, "endLine": 179, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 181, "endLine": 181, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 183, "endLine": 183, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 185, "endLine": 185, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 187, "endLine": 187, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 189, "endLine": 189, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 191, "endLine": 191, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 193, "endLine": 193, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 198, "endLine": 198, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 200, "endLine": 200, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 204, "endLine": 204, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 206, "endLine": 206, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 17 to the 15 allowed.", "effort": "7min", "debt": "7min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2024-08-28T13:35:22+0000", "updateDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZGZMyG74raP-Qiy0k7o", "rule": "java:S3923", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 204, "hash": "465b2d2932ae4dbbd84030979c4db91f", "textRange": {"startLine": 204, "endLine": 204, "startOffset": 8, "endOffset": 10}, "flows": [], "status": "OPEN", "message": "Remove this conditional structure or edit its code blocks so that they're not all the same.", "effort": "15min", "debt": "15min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": [], "creationDate": "2024-08-28T13:35:22+0000", "updateDate": "2024-08-28T13:35:22+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZGZMyG74raP-Qiy0k7m", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 207, "hash": "1e85c0b852a8f8933a66ab6a1b7ccedb", "textRange": {"startLine": 207, "endLine": 207, "startOffset": 39, "endOffset": 56}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 207, "endLine": 207, "startOffset": 39, "endOffset": 56}, "msg": "'request' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 169, "endLine": 169, "startOffset": 11, "endOffset": 26}, "msg": "Implies 'request' is null.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"request\" is nullable here.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-08-28T13:35:22+0000", "updateDate": "2024-08-28T13:35:22+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZB85sWR4raP-QiyaNLq", "rule": "java:S1128", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 5, "hash": "703d398b64e3b0722541bb5bd3688d32", "textRange": {"startLine": 5, "endLine": 5, "startOffset": 7, "endOffset": 21}, "flows": [], "status": "OPEN", "message": "Remove this unused import 'java.util.Date'.", "effort": "2min", "debt": "2min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2024-07-04T08:39:55+0000", "updateDate": "2024-07-04T08:39:55+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUO", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "5a63c1a547db4f6f66386230da41de29", "textRange": {"startLine": 174, "endLine": 174, "startOffset": 17, "endOffset": 100}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Invoke method(s) only conditionally. Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUP", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "e216b52c09eb544f650177d9cc1b5c29", "textRange": {"startLine": 175, "endLine": 175, "startOffset": 17, "endOffset": 71}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Invoke method(s) only conditionally. Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUQ", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "f39d7b5610d37dd72d81504d8edb35ab", "textRange": {"startLine": 179, "endLine": 179, "startOffset": 21, "endOffset": 104}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Invoke method(s) only conditionally. Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUR", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "710c2c693c12ce6f45bafc48d128f1ae", "textRange": {"startLine": 183, "endLine": 183, "startOffset": 21, "endOffset": 57}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUS", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "428997573d04082129387ede14dfcf49", "textRange": {"startLine": 186, "endLine": 186, "startOffset": 17, "endOffset": 56}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUT", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "912887060b0493d90dc3d8d203337a4d", "textRange": {"startLine": 187, "endLine": 187, "startOffset": 17, "endOffset": 69}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUU", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "2b27b90e97db10b8dadf0e21e5979afe", "textRange": {"startLine": 188, "endLine": 188, "startOffset": 17, "endOffset": 55}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUV", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "7ccaec709b649e364e07490307151f35", "textRange": {"startLine": 190, "endLine": 190, "startOffset": 21, "endOffset": 56}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUW", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "41d355e452ff129317b26c1552c120f5", "textRange": {"startLine": 191, "endLine": 191, "startOffset": 17, "endOffset": 47}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUX", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "7abfd1c6b92f6e379ffc7ffe0d797689", "textRange": {"startLine": 196, "endLine": 196, "startOffset": 17, "endOffset": 57}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Use the built-in formatting to construct this argument.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUe", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "5a63c1a547db4f6f66386230da41de29", "textRange": {"startLine": 174, "endLine": 174, "startOffset": 8, "endOffset": 101}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUf", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "e216b52c09eb544f650177d9cc1b5c29", "textRange": {"startLine": 175, "endLine": 175, "startOffset": 8, "endOffset": 72}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUg", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "a73ddbe0c146d553f34056b41ba86793", "textRange": {"startLine": 176, "endLine": 176, "startOffset": 8, "endOffset": 78}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUh", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "f39d7b5610d37dd72d81504d8edb35ab", "textRange": {"startLine": 179, "endLine": 179, "startOffset": 12, "endOffset": 105}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUi", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "b37fc87a8e6039680674c42b7a881cdf", "textRange": {"startLine": 181, "endLine": 181, "startOffset": 12, "endOffset": 86}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUj", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "710c2c693c12ce6f45bafc48d128f1ae", "textRange": {"startLine": 183, "endLine": 183, "startOffset": 12, "endOffset": 58}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUk", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "593b1757f90caa5f0bd519577089b78c", "textRange": {"startLine": 185, "endLine": 185, "startOffset": 12, "endOffset": 78}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUl", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "428997573d04082129387ede14dfcf49", "textRange": {"startLine": 186, "endLine": 186, "startOffset": 8, "endOffset": 57}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUm", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "912887060b0493d90dc3d8d203337a4d", "textRange": {"startLine": 187, "endLine": 187, "startOffset": 8, "endOffset": 70}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUn", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "2b27b90e97db10b8dadf0e21e5979afe", "textRange": {"startLine": 188, "endLine": 188, "startOffset": 8, "endOffset": 56}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUo", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "7ccaec709b649e364e07490307151f35", "textRange": {"startLine": 190, "endLine": 190, "startOffset": 12, "endOffset": 57}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUp", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "41d355e452ff129317b26c1552c120f5", "textRange": {"startLine": 191, "endLine": 191, "startOffset": 8, "endOffset": 48}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUq", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "da634df81b5d6997f6884516eb97572e", "textRange": {"startLine": 193, "endLine": 193, "startOffset": 8, "endOffset": 93}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUr", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "2c0a31d1af0736775318c0da104bf7a6", "textRange": {"startLine": 195, "endLine": 195, "startOffset": 12, "endOffset": 84}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUs", "rule": "java:S3457", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "hash": "7abfd1c6b92f6e379ffc7ffe0d797689", "textRange": {"startLine": 196, "endLine": 196, "startOffset": 8, "endOffset": 58}, "flows": [], "resolution": "FIXED", "status": "CLOSED", "message": "Format specifiers should be used instead of string concatenation.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "confusing"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-08-28T13:35:22+0000", "closeDate": "2024-08-28T13:35:22+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUt", "rule": "java:S1128", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 23, "hash": "4e0a8d7d671653ed8c15faf7a59cf54d", "textRange": {"startLine": 23, "endLine": 23, "startOffset": 7, "endOffset": 57}, "flows": [], "status": "OPEN", "message": "Remove this unused import 'org.springframework.security.core.userdetails.User'.", "effort": "2min", "debt": "2min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-07-02T15:00:04+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUu", "rule": "java:S1128", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 27, "hash": "ae00a843b4f1ebb6cad79c763c0d65ca", "textRange": {"startLine": 27, "endLine": 27, "startOffset": 7, "endOffset": 73}, "flows": [], "status": "OPEN", "message": "Remove this unused import 'org.springframework.web.context.support.StandardServletEnvironment'.", "effort": "2min", "debt": "2min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-07-02T15:00:04+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUZ", "rule": "java:S1117", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 129, "hash": "0d198fbf2be88e474883d3b06202fe3e", "textRange": {"startLine": 129, "endLine": 129, "startOffset": 15, "endOffset": 18}, "flows": [], "status": "OPEN", "message": "Rename \"log\" which hides the field declared at line 51.", "effort": "5min", "debt": "5min", "assignee": "sridi-mansour31699", "author": "<EMAIL>", "tags": ["cert", "pitfall", "suspicious"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-07-04T08:39:55+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUa", "rule": "java:S1481", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 131, "hash": "8e63012b458a9cf39c0644a7415c30b7", "textRange": {"startLine": 131, "endLine": 131, "startOffset": 15, "endOffset": 22}, "flows": [], "status": "OPEN", "message": "Remove this unused \"proceed\" local variable.", "effort": "5min", "debt": "5min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-07-02T15:00:04+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUN", "rule": "java:S2629", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 133, "hash": "5d024954599ebba21eef03e6d9189ec3", "textRange": {"startLine": 133, "endLine": 133, "startOffset": 91, "endOffset": 127}, "flows": [], "status": "OPEN", "message": "Invoke method(s) only conditionally.", "effort": "5min", "debt": "5min", "assignee": "sridi-mansour31699", "author": "<EMAIL>", "tags": ["performance"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-07-02T15:00:04+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUc", "rule": "java:S1116", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 164, "hash": "2b8946e60d2d95f0bab471f600ae1de1", "textRange": {"startLine": 164, "endLine": 164, "startOffset": 71, "endOffset": 72}, "flows": [], "status": "OPEN", "message": "Remove this empty statement.", "effort": "2min", "debt": "2min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "unused"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-07-02T15:00:04+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AZBz9h9L4raP-QiyZcUY", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 223, "hash": "4d9c439ae04dd02318f5494dfb318dbf", "textRange": {"startLine": 223, "endLine": 223, "startOffset": 12, "endOffset": 32}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 223, "endLine": 223, "startOffset": 12, "endOffset": 32}, "msg": "'session' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 222, "endLine": 222, "startOffset": 12, "endOffset": 57}, "msg": "Implies 'session' can be null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "textRange": {"startLine": 222, "endLine": 222, "startOffset": 34, "endOffset": 56}, "msg": "'getSession()' can return null.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"session\" is nullable here.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-07-02T15:00:04+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZBz9h744raP-QiyZcUM", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/LoggingAspectConfiguration.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 17, "hash": "fa9b5af1f6b47d13a4a7a4db44280f84", "textRange": {"startLine": 17, "endLine": 17, "startOffset": 37, "endOffset": 120}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/LoggingAspectConfiguration.java", "textRange": {"startLine": 17, "endLine": 17, "startOffset": 37, "endOffset": 120}, "msg": "'getRequestAttributes' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/LoggingAspectConfiguration.java", "textRange": {"startLine": 17, "endLine": 17, "startOffset": 65, "endOffset": 108}, "msg": "'getRequestAttributes()' can return null.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRequestAttributes\" is nullable here.", "effort": "10min", "debt": "10min", "assignee": "ghozzi-mokhtar-ext85023", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-07-02T15:00:04+0000", "updateDate": "2024-07-02T15:00:04+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZw-4raP-QiyIUAx", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/AddressLineQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 91, "hash": "cdf974c635b61114faa7334277fc1cd9", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 8, "endOffset": 12}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZw-4raP-QiyIUAz", "rule": "java:S2589", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/AddressLineQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 103, "hash": "4c0cb0de85b587ae77a62c08643a9927", "textRange": {"startLine": 103, "endLine": 103, "startOffset": 29, "endOffset": 56}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/AddressLineQueryService.java", "textRange": {"startLine": 103, "endLine": 103, "startOffset": 29, "endOffset": 56}, "msg": "Expression is always true.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/AddressLineQueryService.java", "textRange": {"startLine": 103, "endLine": 103, "startOffset": 37, "endOffset": 56}, "msg": "'getProperty()' can return not null.", "msgFormattings": []}]}], "status": "OPEN", "message": "Remove this expression which always evaluates to \"true\"", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe", "redundant", "suspicious"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZw-4raP-QiyIUAy", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/AddressLineQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 106, "hash": "29c1d72412a8014c5541613a5987f4c4", "textRange": {"startLine": 106, "endLine": 106, "startOffset": 12, "endOffset": 16}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxM4raP-QiyIUA8", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/RoutingCodeQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 89, "hash": "cdf974c635b61114faa7334277fc1cd9", "textRange": {"startLine": 89, "endLine": 89, "startOffset": 8, "endOffset": 12}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxM4raP-QiyIUA-", "rule": "java:S2589", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/RoutingCodeQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 101, "hash": "4c0cb0de85b587ae77a62c08643a9927", "textRange": {"startLine": 101, "endLine": 101, "startOffset": 29, "endOffset": 56}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/RoutingCodeQueryService.java", "textRange": {"startLine": 101, "endLine": 101, "startOffset": 29, "endOffset": 56}, "msg": "Expression is always true.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/RoutingCodeQueryService.java", "textRange": {"startLine": 101, "endLine": 101, "startOffset": 37, "endOffset": 56}, "msg": "'getProperty()' can return not null.", "msgFormattings": []}]}], "status": "OPEN", "message": "Remove this expression which always evaluates to \"true\"", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe", "redundant", "suspicious"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxM4raP-QiyIUA9", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/RoutingCodeQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 104, "hash": "29c1d72412a8014c5541613a5987f4c4", "textRange": {"startLine": 104, "endLine": 104, "startOffset": 12, "endOffset": 16}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZwS4raP-QiyIUAN", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SirenQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 89, "hash": "ef5d11610bf448dc657e7789104dcfa3", "textRange": {"startLine": 89, "endLine": 89, "startOffset": 8, "endOffset": 12}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZwS4raP-QiyIUAP", "rule": "java:S2589", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SirenQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 100, "hash": "4c0cb0de85b587ae77a62c08643a9927", "textRange": {"startLine": 100, "endLine": 100, "startOffset": 29, "endOffset": 56}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SirenQueryService.java", "textRange": {"startLine": 100, "endLine": 100, "startOffset": 29, "endOffset": 56}, "msg": "Expression is always true.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SirenQueryService.java", "textRange": {"startLine": 100, "endLine": 100, "startOffset": 37, "endOffset": 56}, "msg": "'getProperty()' can return not null.", "msgFormattings": []}]}], "status": "OPEN", "message": "Remove this expression which always evaluates to \"true\"", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe", "redundant", "suspicious"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZwS4raP-QiyIUAO", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SirenQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 103, "hash": "29c1d72412a8014c5541613a5987f4c4", "textRange": {"startLine": 103, "endLine": 103, "startOffset": 12, "endOffset": 16}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxI4raP-QiyIUA4", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SiretQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 90, "hash": "d686859c7f2cc293a1392b3028237559", "textRange": {"startLine": 90, "endLine": 90, "startOffset": 8, "endOffset": 12}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxI4raP-QiyIUA6", "rule": "java:S2589", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SiretQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 101, "hash": "4c0cb0de85b587ae77a62c08643a9927", "textRange": {"startLine": 101, "endLine": 101, "startOffset": 29, "endOffset": 56}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SiretQueryService.java", "textRange": {"startLine": 101, "endLine": 101, "startOffset": 29, "endOffset": 56}, "msg": "Expression is always true.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SiretQueryService.java", "textRange": {"startLine": 101, "endLine": 101, "startOffset": 37, "endOffset": 56}, "msg": "'getProperty()' can return not null.", "msgFormattings": []}]}], "status": "OPEN", "message": "Remove this expression which always evaluates to \"true\"", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe", "redundant", "suspicious"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxI4raP-QiyIUA5", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SiretQueryService.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 104, "hash": "29c1d72412a8014c5541613a5987f4c4", "textRange": {"startLine": 104, "endLine": 104, "startOffset": 12, "endOffset": 16}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2024-05-06T08:23:29+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZyR4raP-QiyIUBo", "rule": "java:S116", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/ApplicationProperties.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 22, "hash": "1ecfac2b0ed1c6ab9efeca887558f1ea", "textRange": {"startLine": 22, "endLine": 22, "startOffset": 23, "endOffset": 38}, "flows": [], "status": "OPEN", "message": "Rename this field \"url_auth_server\" to match the regular expression '^[a-z][a-zA-Z0-9]*$'.", "effort": "2min", "debt": "2min", "author": "<EMAIL>", "tags": ["convention"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZyR4raP-QiyIUBp", "rule": "java:S100", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/ApplicationProperties.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 24, "hash": "0b1f783887ade58157a8359ea0884eb4", "textRange": {"startLine": 24, "endLine": 24, "startOffset": 22, "endOffset": 40}, "flows": [], "status": "OPEN", "message": "Rename this method name to match the regular expression '^[a-z][a-zA-Z0-9]*$'.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["convention"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZyR4raP-QiyIUBn", "rule": "java:S117", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/ApplicationProperties.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 28, "hash": "bfff0cc65be057445151b449e4e11f84", "textRange": {"startLine": 28, "endLine": 28, "startOffset": 46, "endOffset": 61}, "flows": [], "status": "OPEN", "message": "Rename this local variable to match the regular expression '^[a-z][a-zA-Z0-9]*$'.", "effort": "2min", "debt": "2min", "author": "<EMAIL>", "tags": ["convention"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZyR4raP-QiyIUBq", "rule": "java:S100", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/ApplicationProperties.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 28, "hash": "bfff0cc65be057445151b449e4e11f84", "textRange": {"startLine": 28, "endLine": 28, "startOffset": 20, "endOffset": 38}, "flows": [], "status": "OPEN", "message": "Rename this method name to match the regular expression '^[a-z][a-zA-Z0-9]*$'.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["convention"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZye4raP-QiyIUBw", "rule": "java:S1854", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/CSPFilter.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 48, "hash": "6016fa3814b9565e8600c0f1ef955c06", "textRange": {"startLine": 48, "endLine": 50, "startOffset": 35, "endOffset": 21}, "flows": [], "status": "OPEN", "message": "Remove this useless assignment to local variable \"urlStagingValue\".", "effort": "15min", "debt": "15min", "author": "<EMAIL>", "tags": ["cert", "cwe", "unused"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZye4raP-QiyIUBz", "rule": "java:S1481", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/CSPFilter.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 48, "hash": "6016fa3814b9565e8600c0f1ef955c06", "textRange": {"startLine": 48, "endLine": 48, "startOffset": 19, "endOffset": 34}, "flows": [], "status": "OPEN", "message": "Remove this unused \"urlStagingValue\" local variable.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AY-6zZyq4raP-QiyIUB0", "rule": "java:S125", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/WebConfigurer.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 78, "hash": "b2141cf0c59551ef6157e055bc78e528", "textRange": {"startLine": 78, "endLine": 78, "startOffset": 14, "endOffset": 80}, "flows": [], "status": "OPEN", "message": "This block of commented-out lines of code should be removed.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxS4raP-QiyIUBB", "rule": "java:S3358", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/security/SecurityUtils.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 150, "hash": "09be23aa17f9587fda8dffd03796bb15", "textRange": {"startLine": 150, "endLine": 152, "startOffset": 18, "endOffset": 37}, "flows": [], "status": "OPEN", "message": "Extract this nested ternary operation into an independent statement.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["confusing"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxS4raP-QiyIUBC", "rule": "java:S3358", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/security/SecurityUtils.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 165, "hash": "09be23aa17f9587fda8dffd03796bb15", "textRange": {"startLine": 165, "endLine": 167, "startOffset": 18, "endOffset": 37}, "flows": [], "status": "OPEN", "message": "Extract this nested ternary operation into an independent statement.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["confusing"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxS4raP-QiyIUA_", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/security/SecurityUtils.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 168, "hash": "40eff3033bd5e5e3f421abd025f92e29", "textRange": {"startLine": 168, "endLine": 168, "startOffset": 48, "endOffset": 68}, "flows": [], "status": "OPEN", "message": "Use already-defined constant 'PREFERRED_USERNAME' instead of duplicating its value here.", "effort": "4min", "debt": "4min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZwb4raP-QiyIUAT", "rule": "java:S4165", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/mapper/CustomAddressLineMapper.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 35, "hash": "984fa749d196059720452876b1b7f4d1", "textRange": {"startLine": 35, "endLine": 35, "startOffset": 12, "endOffset": 88}, "flows": [], "status": "OPEN", "message": "Remove this useless assignment; \"customAddressLineDTO\" already holds the assigned value along all execution paths.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["redundant"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx94raP-QiyIUBe", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 91, "hash": "e323bd9741cd97b5829c5f39e7143e7e", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 55, "endOffset": 76}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 55, "endOffset": 76}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 134, "endLine": 134, "startOffset": 55, "endOffset": 76}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 180, "endLine": 180, "startOffset": 55, "endOffset": 76}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"unique_routing_code\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx94raP-QiyIUBj", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 91, "hash": "e323bd9741cd97b5829c5f39e7143e7e", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 90, "endLine": 90, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 89, "endLine": 89, "startOffset": 21, "endOffset": 60}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx94raP-QiyIUBf", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 92, "hash": "9ad115672d56b1e43c9b16dd4bf871cf", "textRange": {"startLine": 92, "endLine": 92, "startOffset": 51, "endOffset": 80}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 92, "endLine": 92, "startOffset": 51, "endOffset": 80}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 135, "endLine": 135, "startOffset": 51, "endOffset": 80}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 181, "endLine": 181, "startOffset": 51, "endOffset": 80}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"Routing Code already in use\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx94raP-QiyIUBg", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 92, "hash": "9ad115672d56b1e43c9b16dd4bf871cf", "textRange": {"startLine": 92, "endLine": 92, "startOffset": 95, "endOffset": 107}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 92, "endLine": 92, "startOffset": 95, "endOffset": 107}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 135, "endLine": 135, "startOffset": 95, "endOffset": 107}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 181, "endLine": 181, "startOffset": 95, "endOffset": 107}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"codeExists\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx94raP-QiyIUBi", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 134, "hash": "e323bd9741cd97b5829c5f39e7143e7e", "textRange": {"startLine": 134, "endLine": 134, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 134, "endLine": 134, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 134, "endLine": 134, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 133, "endLine": 133, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 132, "endLine": 132, "startOffset": 21, "endOffset": 62}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx94raP-QiyIUBh", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 180, "hash": "e323bd9741cd97b5829c5f39e7143e7e", "textRange": {"startLine": 180, "endLine": 180, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 180, "endLine": 180, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 180, "endLine": 180, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 179, "endLine": 179, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "textRange": {"startLine": 178, "endLine": 178, "startOffset": 21, "endOffset": 69}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx64raP-QiyIUBZ", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 87, "hash": "78e249012e067bf9feb553ec5d5660e2", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 55, "endOffset": 73}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 55, "endOffset": 73}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 131, "endLine": 131, "startOffset": 55, "endOffset": 73}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 55, "endOffset": 73}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"unique_siren_num\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx64raP-QiyIUBc", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 87, "hash": "78e249012e067bf9feb553ec5d5660e2", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 86, "endLine": 86, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 85, "endLine": 85, "startOffset": 21, "endOffset": 48}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx64raP-QiyIUBY", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 88, "hash": "a240ab0b13563ebccc50b685022d322a", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 51, "endOffset": 77}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 51, "endOffset": 77}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 132, "endLine": 132, "startOffset": 51, "endOffset": 77}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 178, "endLine": 178, "startOffset": 51, "endOffset": 77}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"Siren Num already in use\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx64raP-QiyIUBa", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 88, "hash": "a240ab0b13563ebccc50b685022d322a", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 92, "endOffset": 103}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 92, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 132, "endLine": 132, "startOffset": 92, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 178, "endLine": 178, "startOffset": 92, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"numExists\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx64raP-QiyIUBb", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 131, "hash": "78e249012e067bf9feb553ec5d5660e2", "textRange": {"startLine": 131, "endLine": 131, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 131, "endLine": 131, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 131, "endLine": 131, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 130, "endLine": 130, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 129, "endLine": 129, "startOffset": 21, "endOffset": 50}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZx64raP-QiyIUBd", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 177, "hash": "78e249012e067bf9feb553ec5d5660e2", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 176, "endLine": 176, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "textRange": {"startLine": 175, "endLine": 175, "startOffset": 21, "endOffset": 57}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxm4raP-QiyIUBH", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 87, "hash": "a7270070c8b6fa0193c0690f23b70c9c", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 55, "endOffset": 73}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 55, "endOffset": 73}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 131, "endLine": 131, "startOffset": 55, "endOffset": 73}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 55, "endOffset": 73}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"unique_siret_num\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxm4raP-QiyIUBK", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 87, "hash": "a7270070c8b6fa0193c0690f23b70c9c", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 87, "endLine": 87, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 86, "endLine": 86, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 85, "endLine": 85, "startOffset": 21, "endOffset": 48}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxm4raP-QiyIUBG", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 88, "hash": "f84d0d82cf6ea4ad2c50020dc748a772", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 92, "endOffset": 103}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 92, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 132, "endLine": 132, "startOffset": 92, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 178, "endLine": 178, "startOffset": 92, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"numExists\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxm4raP-QiyIUBI", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 88, "hash": "f84d0d82cf6ea4ad2c50020dc748a772", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 51, "endOffset": 77}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 51, "endOffset": 77}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 132, "endLine": 132, "startOffset": 51, "endOffset": 77}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 178, "endLine": 178, "startOffset": 51, "endOffset": 77}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"Siret Num already in use\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxm4raP-QiyIUBL", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 131, "hash": "a7270070c8b6fa0193c0690f23b70c9c", "textRange": {"startLine": 131, "endLine": 131, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 131, "endLine": 131, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 131, "endLine": 131, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 130, "endLine": 130, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 129, "endLine": 129, "startOffset": 21, "endOffset": 50}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxm4raP-QiyIUBJ", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 177, "hash": "a7270070c8b6fa0193c0690f23b70c9c", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 176, "endLine": 176, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "textRange": {"startLine": 175, "endLine": 175, "startOffset": 21, "endOffset": 57}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxr4raP-QiyIUBQ", "rule": "java:S1858", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/UserKeycloakResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 95, "hash": "a125d6fc46334294e30da05a4bd942d2", "textRange": {"startLine": 95, "endLine": 95, "startOffset": 113, "endOffset": 118}, "flows": [], "status": "OPEN", "message": "\"getId\" returns a string, there's no need to call \"toString()\".", "effort": "2min", "debt": "2min", "author": "<EMAIL>", "tags": ["clumsy", "finding"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AY-6zZxr4raP-QiyIUBR", "rule": "java:S1858", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/UserKeycloakResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 108, "hash": "afe2d07c7630de873df71dfdbff5d5a0", "textRange": {"startLine": 108, "endLine": 108, "startOffset": 99, "endOffset": 101}, "flows": [], "status": "OPEN", "message": "\"id\" is already a string, there's no need to call \"toString()\" on it.", "effort": "2min", "debt": "2min", "author": "<EMAIL>", "tags": ["clumsy", "finding"], "creationDate": "2024-04-18T10:46:22+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AY-6zZxv4raP-QiyIUBT", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 91, "hash": "115c730c7feb9c59d9a0792fad680dfb", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 55, "endOffset": 81}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 55, "endOffset": 81}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 135, "endLine": 135, "startOffset": 55, "endOffset": 81}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 181, "endLine": 181, "startOffset": 55, "endOffset": 81}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"unique_address_line_code\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-03-11T10:21:36+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxv4raP-QiyIUBV", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 91, "hash": "115c730c7feb9c59d9a0792fad680dfb", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 90, "endLine": 90, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 89, "endLine": 89, "startOffset": 21, "endOffset": 60}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-03-11T10:21:36+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxv4raP-QiyIUBS", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 92, "hash": "a43d49ed8a80ac5836199be306b73197", "textRange": {"startLine": 92, "endLine": 92, "startOffset": 100, "endOffset": 112}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 92, "endLine": 92, "startOffset": 100, "endOffset": 112}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 136, "endLine": 136, "startOffset": 100, "endOffset": 112}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 182, "endLine": 182, "startOffset": 100, "endOffset": 112}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"codeExists\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-03-11T10:21:36+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxv4raP-QiyIUBU", "rule": "java:S1192", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 92, "hash": "a43d49ed8a80ac5836199be306b73197", "textRange": {"startLine": 92, "endLine": 92, "startOffset": 51, "endOffset": 85}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 92, "endLine": 92, "startOffset": 51, "endOffset": 85}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 136, "endLine": 136, "startOffset": 51, "endOffset": 85}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 182, "endLine": 182, "startOffset": 51, "endOffset": 85}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"Address Line Code already in use\" 3 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2024-03-11T10:21:36+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxv4raP-QiyIUBX", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 135, "hash": "115c730c7feb9c59d9a0792fad680dfb", "textRange": {"startLine": 135, "endLine": 135, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 135, "endLine": 135, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 135, "endLine": 135, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 134, "endLine": 134, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 133, "endLine": 133, "startOffset": 21, "endOffset": 62}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-03-11T10:21:36+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZxv4raP-QiyIUBW", "rule": "java:S2259", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 181, "hash": "115c730c7feb9c59d9a0792fad680dfb", "textRange": {"startLine": 181, "endLine": 181, "startOffset": 16, "endOffset": 43}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 181, "endLine": 181, "startOffset": 16, "endOffset": 43}, "msg": "Result of 'getRootCause()' is dereferenced.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 181, "endLine": 181, "startOffset": 16, "endOffset": 32}, "msg": "'getRootCause()' can return null.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 180, "endLine": 180, "startOffset": 17, "endOffset": 50}, "msg": "'DataIntegrityViolationException' is caught.", "msgFormattings": []}, {"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "textRange": {"startLine": 179, "endLine": 179, "startOffset": 21, "endOffset": 69}, "msg": "Exception is thrown.", "msgFormattings": []}]}], "status": "OPEN", "message": "A \"NullPointerException\" could be thrown; \"getRootCause()\" can return null.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cert", "cwe"], "creationDate": "2024-03-11T10:21:36+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZzt4raP-QiyIUB-", "rule": "java:S107", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/repository/AddressLineRepository.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 120, "hash": "48ca2f1d30b6b79815009ab2080f1ed0", "textRange": {"startLine": 120, "endLine": 120, "startOffset": 9, "endOffset": 50}, "flows": [], "status": "OPEN", "message": "Method has 21 parameters, which is greater than 7 authorized.", "effort": "20min", "debt": "20min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2024-01-22T09:08:24+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZzt4raP-QiyIUCA", "rule": "java:S107", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/repository/AddressLineRepository.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 219, "hash": "518e450deb7968fb4dc273e4792e1eac", "textRange": {"startLine": 219, "endLine": 219, "startOffset": 9, "endOffset": 36}, "flows": [], "status": "OPEN", "message": "Method has 10 parameters, which is greater than 7 authorized.", "effort": "20min", "debt": "20min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2024-01-22T09:08:24+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZzt4raP-QiyIUCC", "rule": "java:S107", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/repository/AddressLineRepository.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 329, "hash": "4d5bce25d8de9ba87724ff8178f60021", "textRange": {"startLine": 329, "endLine": 329, "startOffset": 9, "endOffset": 44}, "flows": [], "status": "OPEN", "message": "Method has 16 parameters, which is greater than 7 authorized.", "effort": "20min", "debt": "20min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2024-01-22T09:08:24+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZy24raP-QiyIUB2", "rule": "java:S1125", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/domain/validation/AddressLineValidator.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 22, "hash": "d43abee8924690b52326dba4d7ae3322", "textRange": {"startLine": 22, "endLine": 22, "startOffset": 18, "endOffset": 22}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/domain/validation/AddressLineValidator.java", "textRange": {"startLine": 23, "endLine": 23, "startOffset": 18, "endOffset": 23}, "msgFormattings": []}]}], "status": "OPEN", "message": "Remove the unnecessary boolean literals.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["clumsy"], "creationDate": "2024-01-17T09:15:24+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AY-6zZwJ4raP-QiyIUAM", "rule": "java:S1125", "severity": "MINOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/dto/validation/AddressLineDTOValidator.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 22, "hash": "d43abee8924690b52326dba4d7ae3322", "textRange": {"startLine": 22, "endLine": 22, "startOffset": 18, "endOffset": 22}, "flows": [{"locations": [{"component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/dto/validation/AddressLineDTOValidator.java", "textRange": {"startLine": 23, "endLine": 23, "startOffset": 18, "endOffset": 23}, "msgFormattings": []}]}], "status": "OPEN", "message": "Remove the unnecessary boolean literals.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["clumsy"], "creationDate": "2024-01-17T09:15:24+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AY-6zZxX4raP-QiyIUBD", "rule": "java:S1172", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/security/jwt/TokenProvider.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 154, "hash": "6087b2de8029a910af1273b91d360b13", "textRange": {"startLine": 154, "endLine": 154, "startOffset": 40, "endOffset": 49}, "flows": [], "status": "OPEN", "message": "Remove this unused method parameter \"authToken\".", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["cert", "unused"], "creationDate": "2024-01-17T09:03:45+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AY-6zZv84raP-QiyIUAK", "rule": "java:S5361", "severity": "CRITICAL", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/dto/custom/CustomAddressLineDTO.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 365, "hash": "973d9e4dd9cbcbf1340697c3c8a3637a", "textRange": {"startLine": 365, "endLine": 365, "startOffset": 48, "endOffset": 58}, "flows": [], "status": "OPEN", "message": "Replace this call to \"replaceAll()\" by a call to the \"replace()\" method.", "effort": "2min", "debt": "2min", "author": "<EMAIL>", "tags": ["performance", "regex"], "creationDate": "2023-12-14T09:18:52+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZv84raP-QiyIUAL", "rule": "java:S3358", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/dto/custom/CustomAddressLineDTO.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 366, "hash": "36f089a939920a2fa8e71e260fdfa035", "textRange": {"startLine": 366, "endLine": 366, "startOffset": 42, "endOffset": 118}, "flows": [], "status": "OPEN", "message": "Extract this nested ternary operation into an independent statement.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["confusing"], "creationDate": "2023-12-14T09:18:52+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZ1B4raP-QiyIUCI", "rule": "java:S5786", "severity": "INFO", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/test/java/com/generix/legalreferential/service/dto/CustomAddressLineDTOTest.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 11, "hash": "39a1b04fbc45becb103972e14f4e2ea9", "textRange": {"startLine": 11, "endLine": 11, "startOffset": 4, "endOffset": 10}, "flows": [], "status": "OPEN", "message": "Remove this 'public' modifier.", "effort": "2min", "debt": "2min", "author": "<EMAIL>", "tags": ["junit", "tests"], "creationDate": "2023-12-14T09:18:52+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "TEST", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AY-6zZye4raP-QiyIUBx", "rule": "java:S6397", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/CSPFilter.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 47, "hash": "4175274bf34b8ab2a695fa72e9af7994", "textRange": {"startLine": 47, "endLine": 47, "startOffset": 72, "endOffset": 73}, "flows": [], "status": "OPEN", "message": "Replace this character class by the character itself.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["regex"], "creationDate": "2023-12-07T15:47:34+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZye4raP-QiyIUBy", "rule": "java:S6397", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/CSPFilter.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 47, "hash": "4175274bf34b8ab2a695fa72e9af7994", "textRange": {"startLine": 47, "endLine": 47, "startOffset": 90, "endOffset": 91}, "flows": [], "status": "OPEN", "message": "Replace this character class by the character itself.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["regex"], "creationDate": "2023-12-07T15:47:34+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZye4raP-QiyIUBv", "rule": "java:S112", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/CSPFilter.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 62, "hash": "fdda924e16c6c8cb6afc035461946766", "textRange": {"startLine": 62, "endLine": 62, "startOffset": 22, "endOffset": 38}, "flows": [], "status": "OPEN", "message": "Define and throw a dedicated exception instead of using a generic one.", "effort": "20min", "debt": "20min", "author": "<EMAIL>", "tags": ["cert", "cwe", "error-handling"], "creationDate": "2023-12-07T15:47:34+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZzT4raP-QiyIUB6", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/domain/AddressLine.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 36, "hash": "7b7ac7916d9bfc39f6e1d9e6034c3296", "textRange": {"startLine": 36, "endLine": 36, "startOffset": 33, "endOffset": 55}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2023-11-23T14:56:32+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AY-6zZzP4raP-QiyIUB4", "rule": "java:S3740", "severity": "MAJOR", "component": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/domain/RoutingCode.java", "project": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "line": 29, "hash": "c1f80ebf3ec7ecd5b63e45e687f439f2", "textRange": {"startLine": 29, "endLine": 29, "startOffset": 33, "endOffset": 55}, "flows": [], "status": "OPEN", "message": "Provide the parametrized type for this generic.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["pitfall"], "creationDate": "2023-11-23T14:56:32+0000", "updateDate": "2024-05-27T16:05:56+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}], "components": [{"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/ApplicationProperties.java", "enabled": true, "qualifier": "FIL", "name": "ApplicationProperties.java", "longName": "src/main/java/com/generix/legalreferential/config/ApplicationProperties.java", "path": "src/main/java/com/generix/legalreferential/config/ApplicationProperties.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SirenQueryService.java", "enabled": true, "qualifier": "FIL", "name": "SirenQueryService.java", "longName": "src/main/java/com/generix/legalreferential/service/SirenQueryService.java", "path": "src/main/java/com/generix/legalreferential/service/SirenQueryService.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/CSPFilter.java", "enabled": true, "qualifier": "FIL", "name": "CSPFilter.java", "longName": "src/main/java/com/generix/legalreferential/config/CSPFilter.java", "path": "src/main/java/com/generix/legalreferential/config/CSPFilter.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/SiretQueryService.java", "enabled": true, "qualifier": "FIL", "name": "SiretQueryService.java", "longName": "src/main/java/com/generix/legalreferential/service/SiretQueryService.java", "path": "src/main/java/com/generix/legalreferential/service/SiretQueryService.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/AddressLineQueryService.java", "enabled": true, "qualifier": "FIL", "name": "AddressLineQueryService.java", "longName": "src/main/java/com/generix/legalreferential/service/AddressLineQueryService.java", "path": "src/main/java/com/generix/legalreferential/service/AddressLineQueryService.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/WebConfigurer.java", "enabled": true, "qualifier": "FIL", "name": "WebConfigurer.java", "longName": "src/main/java/com/generix/legalreferential/config/WebConfigurer.java", "path": "src/main/java/com/generix/legalreferential/config/WebConfigurer.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/RoutingCodeQueryService.java", "enabled": true, "qualifier": "FIL", "name": "RoutingCodeQueryService.java", "longName": "src/main/java/com/generix/legalreferential/service/RoutingCodeQueryService.java", "path": "src/main/java/com/generix/legalreferential/service/RoutingCodeQueryService.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24", "enabled": true, "qualifier": "TRK", "name": "legalReferential generated by j<PERSON>ter", "longName": "legalReferential generated by j<PERSON>ter"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/config/LoggingAspectConfiguration.java", "enabled": true, "qualifier": "FIL", "name": "LoggingAspectConfiguration.java", "longName": "src/main/java/com/generix/legalreferential/config/LoggingAspectConfiguration.java", "path": "src/main/java/com/generix/legalreferential/config/LoggingAspectConfiguration.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/mapper/CustomAddressLineMapper.java", "enabled": true, "qualifier": "FIL", "name": "CustomAddressLineMapper.java", "longName": "src/main/java/com/generix/legalreferential/service/mapper/CustomAddressLineMapper.java", "path": "src/main/java/com/generix/legalreferential/service/mapper/CustomAddressLineMapper.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "enabled": true, "qualifier": "FIL", "name": "LoggingAspect.java", "longName": "src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java", "path": "src/main/java/com/generix/legalreferential/aop/logging/LoggingAspect.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/test/java/com/generix/legalreferential/service/dto/CustomAddressLineDTOTest.java", "enabled": true, "qualifier": "UTS", "name": "CustomAddressLineDTOTest.java", "longName": "src/test/java/com/generix/legalreferential/service/dto/CustomAddressLineDTOTest.java", "path": "src/test/java/com/generix/legalreferential/service/dto/CustomAddressLineDTOTest.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/dto/validation/AddressLineDTOValidator.java", "enabled": true, "qualifier": "FIL", "name": "AddressLineDTOValidator.java", "longName": "src/main/java/com/generix/legalreferential/service/dto/validation/AddressLineDTOValidator.java", "path": "src/main/java/com/generix/legalreferential/service/dto/validation/AddressLineDTOValidator.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/domain/RoutingCode.java", "enabled": true, "qualifier": "FIL", "name": "RoutingCode.java", "longName": "src/main/java/com/generix/legalreferential/domain/RoutingCode.java", "path": "src/main/java/com/generix/legalreferential/domain/RoutingCode.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "enabled": true, "qualifier": "FIL", "name": "SirenResource.java", "longName": "src/main/java/com/generix/legalreferential/web/rest/SirenResource.java", "path": "src/main/java/com/generix/legalreferential/web/rest/SirenResource.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/domain/AddressLine.java", "enabled": true, "qualifier": "FIL", "name": "AddressLine.java", "longName": "src/main/java/com/generix/legalreferential/domain/AddressLine.java", "path": "src/main/java/com/generix/legalreferential/domain/AddressLine.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "enabled": true, "qualifier": "FIL", "name": "RoutingCodeResource.java", "longName": "src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java", "path": "src/main/java/com/generix/legalreferential/web/rest/RoutingCodeResource.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/repository/AddressLineRepository.java", "enabled": true, "qualifier": "FIL", "name": "AddressLineRepository.java", "longName": "src/main/java/com/generix/legalreferential/repository/AddressLineRepository.java", "path": "src/main/java/com/generix/legalreferential/repository/AddressLineRepository.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "enabled": true, "qualifier": "FIL", "name": "AddressLineResource.java", "longName": "src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java", "path": "src/main/java/com/generix/legalreferential/web/rest/AddressLineResource.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/service/dto/custom/CustomAddressLineDTO.java", "enabled": true, "qualifier": "FIL", "name": "CustomAddressLineDTO.java", "longName": "src/main/java/com/generix/legalreferential/service/dto/custom/CustomAddressLineDTO.java", "path": "src/main/java/com/generix/legalreferential/service/dto/custom/CustomAddressLineDTO.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "enabled": true, "qualifier": "FIL", "name": "SiretResource.java", "longName": "src/main/java/com/generix/legalreferential/web/rest/SiretResource.java", "path": "src/main/java/com/generix/legalreferential/web/rest/SiretResource.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/web/rest/UserKeycloakResource.java", "enabled": true, "qualifier": "FIL", "name": "UserKeycloakResource.java", "longName": "src/main/java/com/generix/legalreferential/web/rest/UserKeycloakResource.java", "path": "src/main/java/com/generix/legalreferential/web/rest/UserKeycloakResource.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/security/SecurityUtils.java", "enabled": true, "qualifier": "FIL", "name": "SecurityUtils.java", "longName": "src/main/java/com/generix/legalreferential/security/SecurityUtils.java", "path": "src/main/java/com/generix/legalreferential/security/SecurityUtils.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/domain/validation/AddressLineValidator.java", "enabled": true, "qualifier": "FIL", "name": "AddressLineValidator.java", "longName": "src/main/java/com/generix/legalreferential/domain/validation/AddressLineValidator.java", "path": "src/main/java/com/generix/legalreferential/domain/validation/AddressLineValidator.java"}, {"key": "generix_legal-referential_AY-6y0Pj_UiVIgZPRs24:src/main/java/com/generix/legalreferential/security/jwt/TokenProvider.java", "enabled": true, "qualifier": "FIL", "name": "TokenProvider.java", "longName": "src/main/java/com/generix/legalreferential/security/jwt/TokenProvider.java", "path": "src/main/java/com/generix/legalreferential/security/jwt/TokenProvider.java"}], "facets": []}