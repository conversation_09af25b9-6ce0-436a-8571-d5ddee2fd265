pipeline {
    agent any

    tools {
        maven 'Maven3.6.3'
    }
    stages {
        stage('Checkout') {
            steps {
                git credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a', url: 'http://git.chassagne-scm.generixgroup.com/generix/legal-referential.git'
                 sh '''
                git checkout ${BRANCH_NAME}
                '''
            }
        }
        stage("SonarQube Analysis") {
            steps {
                withSonarQubeEnv('Sonarqube') {
                    sh 'mvn clean package sonar:sonar -Dsonar.projectKey=generix_legal-referential_AY-6y0Pj_UiVIgZPRs24'
                }
            }
        }

        stage('Build') {
            steps {
                script {
                    sh 'chmod +x ./mvnw'
                    sh './mvnw compile com.google.cloud.tools:jib-maven-plugin:3.3.1:build -Dimage=chassagne/legal-referential-back:${VERSION_TAG}'
                }
            }
        }
       stage('Push') {
    steps {
        script {
            withCredentials([usernamePassword(credentialsId: 'Docker-Hub-credentials', usernameVariable: 'DOCKER_HUB_USERNAME', passwordVariable: 'DOCKER_HUB_TOKEN')]) {
                sh "./mvnw compile com.google.cloud.tools:jib-maven-plugin:3.3.1:build -Dimage=chassagne/legal-referential-back:${VERSION_TAG} -Djib.to.auth.username=${DOCKER_HUB_USERNAME} -Djib.to.auth.password=${DOCKER_HUB_TOKEN}"
            }
        }
    }
}

    }
    post {
        always {
          cleanWs()
            sh "docker rmi chassagne/legal-referential-back:${VERSION_TAG} -f "
        }
    }
}
