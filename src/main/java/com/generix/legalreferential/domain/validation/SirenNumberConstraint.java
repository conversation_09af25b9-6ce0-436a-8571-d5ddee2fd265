package com.generix.legalreferential.domain.validation;

import java.lang.annotation.*;
import javax.validation.Constraint;
import javax.validation.Payload;

@Documented
@Constraint(validatedBy = SirenNumberValidator.class)
@Target({ ElementType.FIELD, ElementType.PARAMETER, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface SirenNumberConstraint {
    String message() default "Le numéro de siren doint contenir exactement 9 chiffres";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
