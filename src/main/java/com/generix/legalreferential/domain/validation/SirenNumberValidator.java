package com.generix.legalreferential.domain.validation;

import com.generix.legalreferential.config.Patterns;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Ensures that the given string consists of exactly 9 numeric digits.
 *
 * <p>The validation logic checks if the value is non-null and matches the regex pattern {@code \d{9}}.</p>
 *
 * <p>Examples of valid SIREN numbers:</p>
 * <ul>
 *     <li>123456789 (valid)</li>
 *     <li>987654321 (valid)</li>
 * </ul>
 *
 * <p>Examples of invalid SIREN numbers:</p>
 * <ul>
 *     <li>12345 (too short)</li>
 *     <li>1234567890 (too long)</li>
 *     <li>abcdefghi (not digits)</li>
 *     <li>12345abcd (mixed characters)</li>
 * </ul>

 */
public class SirenNumberValidator implements ConstraintValidator<SirenNumberConstraint, String> {

    @Override
    public void initialize(SirenNumberConstraint constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            return true;
        }
        return value.matches(Patterns.SIREN);
    }
}
