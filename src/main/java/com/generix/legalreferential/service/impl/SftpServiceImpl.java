package com.generix.legalreferential.service.impl;

import com.generix.legalreferential.config.SftpServiceConfiguration;
import com.generix.legalreferential.service.SftpService;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * Implementation of SftpService that delegates to SftpServiceConfiguration.
 */
@Service
public class SftpServiceImpl implements SftpService {
    
    private final SftpServiceConfiguration sftpServiceConfiguration;
    
    public SftpServiceImpl(SftpServiceConfiguration sftpServiceConfiguration) {
        this.sftpServiceConfiguration = sftpServiceConfiguration;
    }
    
    @Override
    public String transferFile(File file) {
        return sftpServiceConfiguration.transferFile(file);
    }
}
