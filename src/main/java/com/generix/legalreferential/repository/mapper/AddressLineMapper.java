package com.generix.legalreferential.repository.mapper;

import com.generix.legalreferential.domain.AddressLine;
import com.generix.legalreferential.service.dto.custom.annuaire.AnnuaireAddressLineDTO;
import java.util.List;

/**
 * Repository-level mapper interface for AddressLine entities.
 * This interface is used to decouple the repository layer from service layer mappers.
 */
public interface AddressLineMapper {
    
    /**
     * Maps an AddressLine entity to AnnuaireAddressLineDTO with specific fields.
     * 
     * @param addressLine the AddressLine entity
     * @param fields the list of fields to include
     * @return the mapped DTO
     */
    AnnuaireAddressLineDTO addressLineToAnnuaireAddressLineDTOWithFields(AddressLine addressLine, List<String> fields);
    
    /**
     * Maps an AddressLine entity to a complete AnnuaireAddressLineDTO.
     * 
     * @param addressLine the AddressLine entity
     * @return the mapped DTO
     */
    AnnuaireAddressLineDTO elaborateAnnuaireAddressLineDTO(AddressLine addressLine);
}
