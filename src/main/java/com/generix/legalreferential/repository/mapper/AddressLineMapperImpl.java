package com.generix.legalreferential.repository.mapper;

import com.generix.legalreferential.domain.AddressLine;
import com.generix.legalreferential.service.dto.custom.annuaire.AnnuaireAddressLineDTO;
import com.generix.legalreferential.service.mapper.annuaire.AnnuaireAddressLineMapper;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Implementation of AddressLineMapper that delegates to the service layer mapper.
 * This provides a bridge between repository and service layers while maintaining architectural boundaries.
 */
@Component
public class AddressLineMapperImpl implements AddressLineMapper {
    
    private final AnnuaireAddressLineMapper annuaireAddressLineMapper;
    
    public AddressLineMapperImpl(@Lazy AnnuaireAddressLineMapper annuaireAddressLineMapper) {
        this.annuaireAddressLineMapper = annuaireAddressLineMapper;
    }
    
    @Override
    public AnnuaireAddressLineDTO addressLineToAnnuaireAddressLineDTOWithFields(AddressLine addressLine, List<String> fields) {
        return annuaireAddressLineMapper.addressLineToAnnuaireAddressLineDTOWithFields(addressLine, fields);
    }
    
    @Override
    public AnnuaireAddressLineDTO elaborateAnnuaireAddressLineDTO(AddressLine addressLine) {
        return annuaireAddressLineMapper.elaborateAnnuaireAddressLineDTO(addressLine);
    }
}
