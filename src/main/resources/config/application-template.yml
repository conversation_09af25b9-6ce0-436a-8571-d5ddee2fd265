spring:
  security:
    oauth2:
      client:
        password-user-admin: %PASSWORD_USER_ADMIN% # the password of legalref_root user
        provider:
          oidc:
            issuer-uri: %ISSUER_URI% #exemple https://auth.staging.apps.generix.biz/auth/realms/legalreferential2
  datasource:
    url: %LR_DB_URL%
    username: %LR_DB_USERNAME%
    password: %LR_DB_PWD%
tokenvalidator:
  security:
    url_auth_server: %TOKENVALIDATOR_URL_AUTH_SERVER%
    cache_token_delay: %TOKENVALIDATOR_CACHE_TOKEN_DELAY%
    cache_certs_keys_delay: %TOKENVALIDATOR_CACHE_CERT_DELAY%
application:
  csp:
    url_auth_server: %URL_STAGING%
  data:
    local_out_directory: %LOCAL_OUT_DIRECTORY%
    local_error_directory: %LOCAL_ERROR_DIRECTORY%j
    sftp:
      host: %SFTP_HOST%
      port: 22
      username: %SFTP_USER%
      password: %SFTP_PASSWORD%
      remote_directory: %SFTP_REMOTE_PATH%
  code: %LEGALREF_APP_CODE%
  ppf:
    base-url: %BASE_URL%
    enabled: true
  piste:
    base-url: %BASE_URL%
    scope: %SCOPE%
    client-id: %CLIENT_ID%
    client-secret: %CLIENT_SECRET%
  feign:
    maxAttempts: 3
    backoff: 1000
