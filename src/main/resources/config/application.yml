# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: INFO
    tech.jhipster: INFO
    com.generix.legalreferential: INFO
---
# Conditionally disable springdoc on missing api-docs profile
spring:
  config:
    activate:
      on-profile: '!api-docs'
springdoc:
  api-docs:
    enabled: false
---
management:
  endpoints:
    web:
      base-path: /management
      exposure:
        include:
          [
            'configprops',
            'env',
            'health',
            'info',
            'jhimetrics',
            'jhiopenapigroups',
            'logfile',
            'loggers',
            'prometheus',
            'threaddump',
            'caches',
            'liquibase',
          ]
  endpoint:
    health:
      show-details: when_authorized
      roles: 'LEGALREF_ADMIN'
      probes:
        enabled: true
      group:
        liveness:
          include: livenessState
        readiness:
          include: readinessState,db
    jhimetrics:
      enabled: true
  info:
    git:
      mode: full
    env:
      enabled: true
  health:
    mail:
      enabled: false # When using the MailService, configure an SMTP server and set this to true
  metrics:
    export:
      # Prometheus is the default metrics backend
      prometheus:
        enabled: false
        step: 60
    enable:
      http: true
      jvm: true
      logback: true
      process: true
      system: true
    distribution:
      percentiles-histogram:
        all: true
      percentiles:
        all: 0, 0.5, 0.75, 0.95, 0.99, 1.0
    tags:
      application: ${spring.application.name}
    web:
      server:
        request:
          autotime:
            enabled: true

spring:
  application:
    name: legal-referential
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the JAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: ${spring.profiles.active:defaultProfile}
    config:
      location: ${spring.config.location:/path/to/default/config/application.yml}
      # Uncomment to activate TLS for the dev profile
      #- tls
  jmx:
    enabled: false
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      poolName: Hikari
      auto-commit: false
      schema: public
  data:
    jpa:
      repositories:
        bootstrap-mode: deferred
  jpa:
    open-in-view: false
    database-platform: tech.jhipster.domain.util.FixedPostgreSQL10Dialect
    properties:
      hibernate.jdbc.time_zone: UTC
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: true
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: false
      # modify batch size as necessary
      hibernate.jdbc.batch_size: 25
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.query.fail_on_pagination_over_collection_fetch: true
      hibernate.query.in_clause_parameter_padding: true
      hibernate.default_schema: public
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  liquibase:
    contexts: prod
  messages:
    basename: i18n/messages
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  task:
    execution:
      thread-name-prefix: legal-referential-task-
      pool:
        core-size: 2
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: legal-referential-scheduling-
      pool:
        size: 2
  thymeleaf:
    cache: true
    mode: HTML
  output:
    ansi:
      console-available: true
  security:
    oauth2:
      client:
        login-user-admin: legalref_root
        password-user-admin: Generix@123
        provider:
          oidc:
            issuer-uri: https://auth.staging.apps.generix.biz/auth/realms/legalreferential2
        registration:
          oidc:
            client-id: legalref_client
            scope: openid, profile, email # last one for refresh tokens
  sleuth:
    # https://github.com/spring-projects/spring-framework/issues/27844#issuescomment-**********
    # https://github.com/spring-cloud/spring-cloud-sleuth/issues/2094
    async:
      enabled: false
    enabled: false
    sampler:
      probability: 1 # report 100% of traces
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    base-url: http://localhost:9411
    enabled: false
    locator:
      discovery:
        enabled: true
# ===================================================================
# To enable TLS in production, generate a certificate using:
# keytool -genkey -alias legal-referential -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 3650
#
# You can also use Let's Encrypt:
# See details in topic "Create a Java Keystore (.JKS) from Let's Encrypt Certificates" on https://maximilian-boehm.com/en-gb/blog
#
# Then, modify the server.ssl properties so your "server" configuration looks like:
#
# server:
#   port: 443
#   ssl:
#     key-store: classpath:config/tls/keystore.p12
#     key-store-password: password
#     key-store-type: PKCS12
#     key-alias: selfsigned
#     # The ciphers suite enforce the security by deactivating some old and deprecated SSL cipher, this list was tested against SSL Labs (https://www.ssllabs.com/ssltest/)
#     ciphers: TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 ,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_GCM_SHA384,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA,TLS_RSA_WITH_CAMELLIA_128_CBC_SHA
# ===================================================================
server:
  port: 8081
  shutdown: graceful # see https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-graceful-shutdown
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,application/javascript,application/json,image/svg+xml
    min-response-size: 1024
  forward-headers-strategy: framework
  servlet:
    session:
      cookie:
        http-only: true

springdoc:
  show-actuator: true

# Properties to be exposed on the /info management endpoint
info:
  # Comma separated list of profiles that will trigger the ribbon to show
  display-ribbon-on-profiles: 'dev'

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  http:
    cache: # Used by the CachingHttpHeadersFilter
      timeToLiveInDays: 1461
  cache: # Cache configuration
    ehcache: # Ehcache configuration
      time-to-live-seconds: 3600 # By default objects stay 1 hour in the cache
      max-entries: 1000 # Number of objects in each cache entry
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      queue-size: 512
  clientApp:
    name: 'legal-referentialApp'
  # By default CORS is disabled. Uncomment to enable.
  # cors:
  #   allowed-origins: "http://localhost:8100,http://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params"
  #   allow-credentials: true
  #   max-age: 1800
  cors:
    # Use allowedOrigins to specify a list of allowed origins explicitly
    #allowed-origins: 'http://legal-referential-back-aiofront.chassagne-qa.generixgroup.com,https://legal-referential-back-aiofront.chassagne-qa.generixgroup.com'
    #allowed-origins: '*'
    allowed-origin-patterns: 'http://localhost:*,http://*.host.com*,https://*.generixgroup.com,http://*.generixgroup.com,https://*.generix.biz,http://*.generix.biz,http://legal-ref-front.germanywestcentral.cloudapp.azure.com,https://legal-ref-front.germanywestcentral.cloudapp.azure.com'
    allowed-methods: '*'
    allowed-headers: '*'
    exposed-headers: 'Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params,Access-Control-Request-Method,Access-Control-Request-Headers,Access-control-allow-origin,Access-Control-Allow-Credentials,tenant'
    allow-credentials: true
    max-age: 1800
  mail:
    from: legal-referential@localhost
  api-docs:
    management-include-pattern: ${server.servlet.context-path:}/management/**
    title: Legal Referential API
    description: Legal Referential API documentation
    version: 0.0.1
    terms-of-service-url:
    contact-name:
    contact-url:
    contact-email:
    license: unlicensed
    license-url:
  security:
    content-security-policy: "default-src 'self'; frame-src 'self' data:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://storage.googleapis.com; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:"
    oauth2:
      audience:
        - account
        - api://default
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================
