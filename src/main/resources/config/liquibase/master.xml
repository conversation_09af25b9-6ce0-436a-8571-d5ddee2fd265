<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <property name="now" value="current_timestamp" dbms="postgresql"/>
  <property name="floatType" value="float4" dbms="postgresql"/>
  <property name="clobType" value="longvarchar" dbms="postgresql"/>
  <property name="blobType" value="bytea" dbms="postgresql"/>
  <property name="uuidType" value="uuid" dbms="postgresql"/>
  <property name="datetimeType" value="datetime" dbms="postgresql"/>

  <include file="config/liquibase/changelog/00000000000000_initial_schema.xml"
    relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20230721131954_added_entity_Siren.xml"
    relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20230721131955_added_entity_Siret.xml"
    relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20230721131956_added_entity_RoutingCode.xml"
    relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20230721131957_added_entity_AddressLine.xml"
    relativeToChangelogFile="false"/>
  <!-- jhipster-needle-liquibase-add-changelog - JHipster will add liquibase changelogs here -->
  <include file="config/liquibase/changelog/20230721131955_added_entity_constraints_Siret.xml"
    relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20230721131956_added_entity_constraints_RoutingCode.xml"
    relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20230721131957_added_entity_constraints_AddressLine.xml"
    relativeToChangelogFile="false"/>
  <!-- jhipster-needle-liquibase-add-constraints-changelog - JHipster will add liquibase constraints changelogs here -->
  <!-- jhipster-needle-liquibase-add-incremental-changelog - JHipster will add incremental liquibase changelogs here -->
  <include file="/config/liquibase/changelog/20231109131957_added_index_routing_code.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20231110131957_added_index_siret.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20231110141957_added_index_siren.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20231121091081_AIO-16231_update_all_table.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20231121091182_AIO-16231_insert_default_value.xml"
    relativeToChangelogFile="false"/>
  <include
    file="/config/liquibase/changelog/20231121091283_AIO-16231_update_table_default_value.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20231122101013_AIO-16231_update_sequence.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20231123098025_AIO-16231_add_index.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20231129091400_perf_sql_add_index.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20231205165165_perf_sql_add_index2.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20240307102557_AIO-16996_added_entity_constraints.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20240311113151_AIO-16829_add_role_delete_table.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20240322131026_AIO-16829_new_role_column.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20240412151807_AIO-16829_update_sequences.xml"
    relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20240820105900_AIO-18709_remove_all_table.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20240820105901_added_entity_Siren.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20240820105902_added_entity_Siret.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20240820105903_added_entity_RoutingCode.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20240820105904_added_entity_AddressLine.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20240820105905_added_entity_Platform.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20240820105902_added_entity_constraints_Siret.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20240820105903_added_entity_constraints_RoutingCode.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20240820105904_added_entity_constraints_AddressLine.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20240910165906_added_index.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20240918165504_added_index2.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20240711123704_change_suffix_to_null.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20241107123456_add_matricule_paltform.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250121171850_update_routing_code_address_country_label.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20241225095855_update_entites_constraints.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250109104350_add_attributes_to_address_ligne.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250122112750_update_siret_diffusible.xml" relativeToChangelogFile="false"/><include file="/config/liquibase/changelog/20250122182850_update_platform_platform_id.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250124163250_update_siret_code_postal.xml" relativeToChangelogFile="false"/><include file="/config/liquibase/changelog/20250122182850_update_platform_platform_id.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250124182250_update_siren_company_name.xml" relativeToChangelogFile="false"/><include file="/config/liquibase/changelog/20250122182850_update_platform_platform_id.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250203103710_update_platform_status_type.xml" relativeToChangelogFile="false"/><include file="/config/liquibase/changelog/20250122182850_update_platform_platform_id.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250204143310_update_platform_type_size.xml" relativeToChangelogFile="false"/><include file="/config/liquibase/changelog/20250122182850_update_platform_platform_id.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/202501171037120_added_entity_Tracking.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250225141420_add_index_for_adress_line_id.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250306133312_update_routing_code_constraints.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250307024910_added_column_to_entity_Tracking.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250325133000_update_siret_address_nullable.xml" relativeToChangelogFile="false"/>
  <include file="/config/liquibase/changelog/20250522150912_change_siren_platform_null.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
