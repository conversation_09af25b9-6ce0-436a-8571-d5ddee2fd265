<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity AddressLine.
    -->
    <changeSet id="20230721131957-1" author="jhipster">
        <createTable tableName="address_line">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="address_line_code" type="varchar(125)">
                <constraints nullable="false" />
            </column>
            <column name="application_date" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="code_type_modification" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="modification_id" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column name="entity_type" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="invoicing_line_status" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="platform_type" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="platform_reception_reg_number" type="integer">
                <constraints nullable="false" />
            </column>
            <column name="pdp_social_raison" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="pdp_commercial_name" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="platform_reception_data_contact" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="platform_period_start" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="platform_period_end" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="platform_reception_state" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="management_legal_engagement" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="management_service" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="management_service_or_legal" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="moa" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="moa_only" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="management_state_payment" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="modification_date" type="${datetimeType}">
                <constraints nullable="false" />
            </column>
            <column name="pk_routing_code_id" type="bigint">
                <constraints nullable="true" unique="true" uniqueConstraintName="ux_address_line__pk_routing_code_id" />
            </column>
            <column name="fk_address_line_siren_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="fk_address_line_siret_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="address_line" columnName="application_date" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="address_line" columnName="platform_period_start" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="address_line" columnName="platform_period_end" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="address_line" columnName="modification_date" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20230721131957-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/address_line.csv"
                  separator=";"
                  tableName="address_line"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="address_line_code" type="string"/>
            <column name="application_date" type="date"/>
            <column name="code_type_modification" type="string"/>
            <column name="modification_id" type="string"/>
            <column name="entity_type" type="string"/>
            <column name="invoicing_line_status" type="string"/>
            <column name="platform_type" type="string"/>
            <column name="platform_reception_reg_number" type="numeric"/>
            <column name="pdp_social_raison" type="string"/>
            <column name="pdp_commercial_name" type="string"/>
            <column name="platform_reception_data_contact" type="string"/>
            <column name="platform_period_start" type="date"/>
            <column name="platform_period_end" type="date"/>
            <column name="platform_reception_state" type="string"/>
            <column name="management_legal_engagement" type="boolean"/>
            <column name="management_service" type="boolean"/>
            <column name="management_service_or_legal" type="boolean"/>
            <column name="moa" type="boolean"/>
            <column name="moa_only" type="boolean"/>
            <column name="management_state_payment" type="boolean"/>
            <column name="modification_date" type="date"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
