<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Platform.
    -->
    <changeSet id="20240820105905-1" author="asghaier">
        <createTable tableName="platform">
            <column name="instance_id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="platform_id" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="platform_type" type="varchar(3)">
                <constraints nullable="false" />
            </column>
            <column name="platform_status" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="platform_siren" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="platform_company_name" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="platform_commercial_name" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="platform_contact_or_url" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="registration_begin_date" type="date">
                <constraints nullable="false" />
            </column>
            <column name="registration_end_date" type="date">
                <constraints nullable="false" />
            </column>
            <column name="issuer" type="varchar(250)">
                <constraints nullable="false" />
            </column>
            <column name="client_id" type="varchar(20)">
                <constraints nullable="false" />
            </column>
            <column name="owner" type="varchar(20)">
                <constraints nullable="false" />
            </column>
            <column name="created_date" type="timestamp">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_date" type="timestamp">
                <constraints nullable="false" />
            </column>
            <column name="origin_insert" type="varchar(25)">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20240820105905-1-data" author="asghaier" context="faker">
        <loadData
                  file="config/liquibase/fake-data/platform.csv"
                  separator=";"
                  tableName="platform"
                  usePreparedStatements="true">
            <column name="instance_id" type="numeric"/>
            <column name="platform_id" type="string"/>
            <column name="platform_type" type="string"/>
            <column name="platform_status" type="string"/>
            <column name="platform_siren" type="string"/>
            <column name="platform_company_name" type="string"/>
            <column name="platform_commercial_name" type="string"/>
            <column name="platform_contact_or_url" type="string"/>
            <column name="registration_begin_date" type="date"/>
            <column name="registration_end_date" type="date"/>
            <column name="issuer" type="string"/>
            <column name="client_id" type="string"/>
            <column name="owner" type="string"/>
            <column name="created_date" type="timestamp"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="origin_insert" type="varchar(25)"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
