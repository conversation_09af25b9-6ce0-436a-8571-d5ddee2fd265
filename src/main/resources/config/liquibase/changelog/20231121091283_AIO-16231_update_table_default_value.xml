<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet  id="20231121091281" author="asghaier">
        <sql dbms="postgresql">
            ALTER TABLE "siren" ALTER COLUMN "created_by" TYPE VARCHAR(50), ALTER COLUMN "created_by" SET NOT NULL, ALTER COLUMN "created_by" DROP DEFAULT;
            ALTER TABLE "siren" ALTER COLUMN "last_modified_by" TYPE VARCHAR(50), ALTER COLUMN "last_modified_by" SET NOT NULL, ALTER COLUMN "last_modified_by" DROP DEFAULT;
            ALTER TABLE "siren" ALTER COLUMN "created_date" TYPE TIMESTAMP, ALTER COLUMN "created_date" SET NOT NULL, ALTER COLUMN "created_date" DROP DEFAULT;
            ALTER TABLE "siren" ALTER COLUMN "last_modified_date" TYPE TIMESTAMP, ALTER COLUMN "last_modified_date" SET NOT NULL, ALTER COLUMN "last_modified_date" DROP DEFAULT;
            ALTER TABLE "siren" ALTER COLUMN "origin_insert" TYPE VARCHAR(30), ALTER COLUMN "origin_insert" SET NOT NULL, ALTER COLUMN "origin_insert" DROP DEFAULT;

            ALTER TABLE "siret" ALTER COLUMN "created_by" TYPE VARCHAR(50), ALTER COLUMN "created_by" SET NOT NULL, ALTER COLUMN "created_by" DROP DEFAULT;
            ALTER TABLE "siret" ALTER COLUMN "last_modified_by" TYPE VARCHAR(50), ALTER COLUMN "last_modified_by" SET NOT NULL, ALTER COLUMN "last_modified_by" DROP DEFAULT;
            ALTER TABLE "siret" ALTER COLUMN "created_date" TYPE TIMESTAMP, ALTER COLUMN "created_date" SET NOT NULL, ALTER COLUMN "created_date" DROP DEFAULT;
            ALTER TABLE "siret" ALTER COLUMN "last_modified_date" TYPE TIMESTAMP, ALTER COLUMN "last_modified_date" SET NOT NULL, ALTER COLUMN "last_modified_date" DROP DEFAULT;
            ALTER TABLE "siret" ALTER COLUMN "origin_insert" TYPE VARCHAR(30), ALTER COLUMN "origin_insert" SET NOT NULL, ALTER COLUMN "origin_insert" DROP DEFAULT;

            ALTER TABLE "routing_code" ALTER COLUMN "created_by" TYPE VARCHAR(50), ALTER COLUMN "created_by" SET NOT NULL, ALTER COLUMN "created_by" DROP DEFAULT;
            ALTER TABLE "routing_code" ALTER COLUMN "last_modified_by" TYPE VARCHAR(50), ALTER COLUMN "last_modified_by" SET NOT NULL, ALTER COLUMN "last_modified_by" DROP DEFAULT;
            ALTER TABLE "routing_code" ALTER COLUMN "created_date" TYPE TIMESTAMP, ALTER COLUMN "created_date" SET NOT NULL, ALTER COLUMN "created_date" DROP DEFAULT;
            ALTER TABLE "routing_code" ALTER COLUMN "last_modified_date" TYPE TIMESTAMP, ALTER COLUMN "last_modified_date" SET NOT NULL, ALTER COLUMN "last_modified_date" DROP DEFAULT;
            ALTER TABLE "routing_code" ALTER COLUMN "origin_insert" TYPE VARCHAR(30), ALTER COLUMN "origin_insert" SET NOT NULL, ALTER COLUMN "origin_insert" DROP DEFAULT;

            ALTER TABLE "address_line" ALTER COLUMN "created_by" TYPE VARCHAR(50), ALTER COLUMN "created_by" SET NOT NULL, ALTER COLUMN "created_by" DROP DEFAULT;
            ALTER TABLE "address_line" ALTER COLUMN "last_modified_by" TYPE VARCHAR(50), ALTER COLUMN "last_modified_by" SET NOT NULL, ALTER COLUMN "last_modified_by" DROP DEFAULT;
            ALTER TABLE "address_line" ALTER COLUMN "created_date" TYPE TIMESTAMP, ALTER COLUMN "created_date" SET NOT NULL, ALTER COLUMN "created_date" DROP DEFAULT;
            ALTER TABLE "address_line" ALTER COLUMN "last_modified_date" TYPE TIMESTAMP, ALTER COLUMN "last_modified_date" SET NOT NULL, ALTER COLUMN "last_modified_date" DROP DEFAULT;
            ALTER TABLE "address_line" ALTER COLUMN "origin_insert" TYPE VARCHAR(30), ALTER COLUMN "origin_insert" SET NOT NULL, ALTER COLUMN "origin_insert" DROP DEFAULT;
        </sql>
    </changeSet>
</databaseChangeLog>
