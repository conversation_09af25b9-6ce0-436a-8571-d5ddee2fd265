<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet  id="20231205165165" author="asghaier">
        <sql dbms="postgresql">
            CREATE INDEX IF NOT EXISTS address_line_code_ind ON address_line (address_line_code);
        </sql>
    </changeSet>
</databaseChangeLog>
