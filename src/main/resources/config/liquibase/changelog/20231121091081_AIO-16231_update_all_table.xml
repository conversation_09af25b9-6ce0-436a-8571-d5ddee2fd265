<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet  id="20231121091081" author="asghaier">
        <sql dbms="postgresql">
            ALTER TABLE "siren" ADD "created_by" VARCHAR(50) NULL;
            ALTER TABLE "siren" ADD "last_modified_by" VARCHAR(50) NULL;
            ALTER TABLE "siren" ADD "created_date" TIMESTAMP NULL;
            ALTER TABLE "siren" ADD "last_modified_date" TIMESTAMP NULL;
            ALTER TABLE "siren" ADD "origin_insert" VARCHAR(30) NULL;
            ALTER TABLE "siren" DROP COLUMN "modification_date";

            ALTER TABLE "siret" ADD "created_by" VARCHAR(50) NULL;
            ALTER TABLE "siret" ADD "last_modified_by" VARCHAR(50) NULL;
            ALTER TABLE "siret" ADD "created_date" TIMESTAMP NULL;
            ALTER TABLE "siret" ADD "last_modified_date" TIMESTAMP NULL;
            ALTER TABLE "siret" ADD "origin_insert" VARCHAR(30) NULL;
            ALTER TABLE "siret" DROP COLUMN "modification_date";

            ALTER TABLE "routing_code" ADD "created_by" VARCHAR(50) NULL;
            ALTER TABLE "routing_code" ADD "last_modified_by" VARCHAR(50) NULL;
            ALTER TABLE "routing_code" ADD "created_date" TIMESTAMP NULL;
            ALTER TABLE "routing_code" ADD "last_modified_date" TIMESTAMP NULL;
            ALTER TABLE "routing_code" ADD "origin_insert" VARCHAR(30) NULL;
            ALTER TABLE "routing_code" DROP COLUMN "modification_date";

            ALTER TABLE "address_line" ADD "created_by" VARCHAR(50) NULL;
            ALTER TABLE "address_line" ADD "last_modified_by" VARCHAR(50) NULL;
            ALTER TABLE "address_line" ADD "created_date" TIMESTAMP NULL;
            ALTER TABLE "address_line" ADD "last_modified_date" TIMESTAMP NULL;
            ALTER TABLE "address_line" ADD "origin_insert" VARCHAR(30) NULL;
            ALTER TABLE "address_line" DROP COLUMN "modification_date";

            ALTER TABLE "jhi_user" ADD "origin_insert" VARCHAR(30) NULL;

            ALTER TABLE "siret" ALTER COLUMN "address_ligne_2" TYPE VARCHAR(38), ALTER COLUMN "address_ligne_2" DROP NOT NULL, ALTER COLUMN "address_ligne_2" DROP DEFAULT;
            ALTER TABLE "siret" ALTER COLUMN "address_ligne_3" TYPE VARCHAR(26), ALTER COLUMN "address_ligne_3" DROP NOT NULL, ALTER COLUMN "address_ligne_3" DROP DEFAULT;

        </sql>
    </changeSet>
</databaseChangeLog>
