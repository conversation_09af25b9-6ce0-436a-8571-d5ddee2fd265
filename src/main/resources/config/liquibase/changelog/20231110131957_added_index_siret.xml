<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet  id="20231109131957" author="asghaier">
        <sql dbms="postgresql">
            create index siret_num_ind on siret (siret_num);
            create index siret_adr1_ind on siret (address_ligne_1);
            create index siret_adr1_ind_up on siret ((upper(address_ligne_1)));
            create index siret_adr2_ind on siret (address_ligne_2);
            create index siret_adr2_ind_up on siret ((upper(address_ligne_2)));
            create index siret_adr3_ind on siret (address_ligne_3);
            create index siret_adr3_ind_up on siret ((upper(address_ligne_3)));
            create index siret_postcode_ind on siret (postal_code);
            create index siret_town_ind on siret (town);
            create index siret_town_ind_up on siret ((upper(town)));
            create index siret_country_ind on siret (country);
            create index siret_country_ind_up on siret ((upper(country)));
        </sql>
    </changeSet>
</databaseChangeLog>
