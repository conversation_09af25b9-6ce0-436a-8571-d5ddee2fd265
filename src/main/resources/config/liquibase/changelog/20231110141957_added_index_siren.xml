<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet  id="20231109131957" author="asghaier">
        <sql dbms="postgresql">
            create index siren_num_ind on siren (siren_num);
            create index siren_company_name on siren (company_name);
            create index siren_company_name_up on siren ((upper(company_name)));
            create index siren_entity_type on siren (entity_type);
            create index siren_entity_type_up on siren ((upper(entity_type)));
        </sql>
    </changeSet>
</databaseChangeLog>
