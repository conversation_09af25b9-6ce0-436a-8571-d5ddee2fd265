<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">

    <changeSet id="20250225141419" author="system">
        <sql>
            CREATE EXTENSION IF NOT EXISTS pg_trgm;
        </sql>
    </changeSet>

    <changeSet id="20250225141420" author="system">
        <sql splitStatements="false">
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1
                    FROM pg_indexes
                    WHERE tablename = 'address_line'
                    AND indexname = 'idx_address_line_id_trgm'
                ) THEN
            CREATE INDEX idx_address_line_id_trgm
                ON public.address_line
                USING GIN (address_line_id gin_trgm_ops);
            END IF;
            END $$;
        </sql>
    </changeSet>

</databaseChangeLog>
