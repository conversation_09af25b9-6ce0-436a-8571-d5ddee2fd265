<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity RoutingCode.
    -->
    <changeSet id="**************-1" author="asghaier">
        <createTable tableName="routing_code">
            <column name="instance_id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="routing_code_id" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="status" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="routing_code_label" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="routing_code_type" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="administrative_status" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="legal_engagement_management" type="boolean">
                <constraints nullable="false" />
            </column>
            <column name="address_line_1" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_line_2" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_line_3" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_postal_code" type="varchar(10)">
                <constraints nullable="false" />
            </column>
            <column name="address_city" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_country_subdivision" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_country_code" type="varchar(10)">
                <constraints nullable="false" />
            </column>
            <column name="address_country_label" type="varchar(2)">
                <constraints nullable="false" />
            </column>
            <column name="issuer" type="varchar(250)">
                <constraints nullable="false" />
            </column>
            <column name="client_id" type="varchar(20)">
                <constraints nullable="false" />
            </column>
            <column name="owner" type="varchar(20)">
                <constraints nullable="false" />
            </column>
            <column name="created_date" type="timestamp">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_date" type="timestamp">
                <constraints nullable="false" />
            </column>
            <column name="origin_insert" type="varchar(25)">
                <constraints nullable="false" />
            </column>
            <column name="fk_routing_code_siret_instance_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="**************-1-data" author="asghaier" context="faker">
        <loadData
                  file="config/liquibase/fake-data/routing_code.csv"
                  separator=";"
                  tableName="routing_code"
                  usePreparedStatements="true">
            <column name="instance_id" type="numeric"/>
            <column name="routing_code_id" type="string"/>
            <column name="status" type="string"/>
            <column name="routing_code_label" type="string"/>
            <column name="routing_code_type" type="string"/>
            <column name="administrative_status" type="string"/>
            <column name="legal_engagement_management" type="boolean"/>
            <column name="address_line_1" type="string"/>
            <column name="address_line_2" type="string"/>
            <column name="address_line_3" type="string"/>
            <column name="address_postal_code" type="string"/>
            <column name="address_city" type="string"/>
            <column name="address_country_subdivision" type="string"/>
            <column name="address_country_code" type="string"/>
            <column name="address_country_label" type="string"/>
            <column name="issuer" type="string"/>
            <column name="client_id" type="string"/>
            <column name="owner" type="string"/>
            <column name="created_date" type="timestamp"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="origin_insert" type="varchar(25)"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
