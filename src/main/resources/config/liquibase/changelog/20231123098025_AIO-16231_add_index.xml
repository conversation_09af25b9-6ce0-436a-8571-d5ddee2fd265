<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet  id="20231123098025" author="asghaier">
        <sql dbms="postgresql">
            create index siren_created_by on siren (created_by);
            create index siren_last_modified_by on siren (last_modified_by);
            create index siren_created_date on siren (created_date);
            create index siren_last_modified_date on siren (last_modified_date);
            create index siren_origin_insert on siren (origin_insert);

            create index siret_created_by on siret (created_by);
            create index siret_last_modified_by on siret (last_modified_by);
            create index siret_created_date on siret (created_date);
            create index siret_last_modified_date on siret (last_modified_date);
            create index siret_origin_insert on siret (origin_insert);

            create index routing_code_created_by on routing_code (created_by);
            create index routing_code_last_modified_by on routing_code (last_modified_by);
            create index routing_code_created_date on routing_code (created_date);
            create index routing_code_last_modified_date on routing_code (last_modified_date);
            create index routing_code_origin_insert on routing_code (origin_insert);

            create index address_line_created_by on address_line (created_by);
            create index address_line_last_modified_by on address_line (last_modified_by);
            create index address_line_created_date on address_line (created_date);
            create index address_line_last_modified_date on address_line (last_modified_date);
            create index address_line_origin_insert on address_line (origin_insert);
        </sql>
    </changeSet>
</databaseChangeLog>
