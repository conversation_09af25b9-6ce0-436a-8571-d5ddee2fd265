<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

  <changeSet id="20240311113151-1" author="asghaier">
    <sql dbms="postgresql">
      DROP TABLE jhi_user_authority;
      DROP TABLE jhi_user;
      DROP TABLE jhi_authority;
    </sql>
  </changeSet>

  <changeSet id="20240311113151-2" author="asghaier">
    <sql dbms="postgresql">
      CREATE TABLE "permissions_matrix"
      (
        "id"                 BIGINT      NOT NULL,
        "role"               VARCHAR(25) NOT NULL,
        "read"               VARCHAR(10) NOT NULL,
        "create"             VARCHAR(10) NOT NULL,
        "update"             VARCHAR(10) NOT NULL,
        "delete"             VARCHAR(10) NOT NULL,
        "owner"              VARCHAR(10) NOT NULL,
        PRIMARY KEY ("id")
      );
    </sql>
  </changeSet>


  <changeSet id="20240311113151-3" author="asghaier">
    <createSequence sequenceName="sequence_generator_permissions_matrix" startValue="1"
      incrementBy="50"/>
  </changeSet>

  <changeSet id="20240311113151-4" author="asghaier">
    <sql dbms="postgresql">select setval('sequence_generator_permissions_matrix', max(id) + 1)
                           from permissions_matrix</sql>
    <sql
      dbms="h2">alter sequence sequence_generator_permissions_matrix restart with (select max(id)+1 from permissions_matrix)</sql>
  </changeSet>

  <changeSet id="20240311113151-5" author="asghaier">
    <validCheckSum>ANY</validCheckSum>
    <sql dbms="postgresql">
      INSERT INTO permissions_matrix ("id", "role", "read", "create", "update", "delete", "owner")
      VALUES (1, 'LEGALREF_BUSINESS', 'GRANTED', 'FORBIDDEN', 'FORBIDDEN', 'FORBIDDEN', 'FORBIDDEN');

      INSERT INTO permissions_matrix ("id", "role", "read", "create", "update", "delete", "owner")
      VALUES (2, 'LEGALREF_CUSTOMER', 'RESTRICTED', 'GRANTED', 'RESTRICTED', 'FORBIDDEN', 'FORBIDDEN');

      INSERT INTO permissions_matrix ("id", "role", "read", "create", "update", "delete", "owner")
      VALUES (3, 'LEGALREF_ADMIN', 'GRANTED', 'GRANTED', 'GRANTED', 'GRANTED', 'CRUD');

      INSERT INTO permissions_matrix ("id", "role", "read", "create", "update", "delete", "owner")
      VALUES (4, 'LEGALREF_SYNCHRONIZER', 'FORBIDDEN', 'GRANTED', 'GRANTED', 'FORBIDDEN', 'FORBIDDEN');
    </sql>
  </changeSet>

</databaseChangeLog>
