<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Siret.
    -->
    <changeSet id="20240820105902-1" author="asghaier">
        <createTable tableName="siret">
            <column name="instance_id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="siret" type="varchar(14)">
                <constraints nullable="false" />
            </column>
            <column name="status" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="establishment_type" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="denomination" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="address_line_1" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_line_2" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_line_3" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_city" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_country_subdivision" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="address_country_code" type="varchar(10)">
                <constraints nullable="false" />
            </column>
            <column name="address_country_label" type="varchar(2)">
                <constraints nullable="false" />
            </column>
            <column name="b_2_g_moa" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="b_2_g_moa_only" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="b_2_g_payment_status_management" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="b_2_g_legal_engagement_management" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="b_2_g_legal_or_service_engagement_management" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="b_2_g_service_code_management" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="diffusible" type="boolean">
                <constraints nullable="false" />
            </column>
            <column name="address_postal_code" type="varchar(5)">
                <constraints nullable="false" />
            </column>
            <column name="issuer" type="varchar(250)">
                <constraints nullable="false" />
            </column>
            <column name="client_id" type="varchar(20)">
                <constraints nullable="false" />
            </column>
            <column name="owner" type="varchar(20)">
                <constraints nullable="false" />
            </column>
            <column name="created_date" type="timestamp">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_date" type="timestamp">
                <constraints nullable="false" />
            </column>
            <column name="origin_insert" type="varchar(25)">
                <constraints nullable="false" />
            </column>
            <column name="fk_siret_siren_instance_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20240820105902-1-data" author="asghaier" context="faker">
        <loadData
                  file="config/liquibase/fake-data/siret.csv"
                  separator=";"
                  tableName="siret"
                  usePreparedStatements="true">
            <column name="instance_id" type="numeric"/>
            <column name="siret" type="string"/>
            <column name="status" type="string"/>
            <column name="establishment_type" type="string"/>
            <column name="denomination" type="string"/>
            <column name="address_line_1" type="string"/>
            <column name="address_line_2" type="string"/>
            <column name="address_line_3" type="string"/>
            <column name="address_city" type="string"/>
            <column name="address_country_subdivision" type="string"/>
            <column name="address_country_code" type="string"/>
            <column name="address_country_label" type="string"/>
            <column name="b_2_g_moa" type="boolean"/>
            <column name="b_2_g_moa_only" type="boolean"/>
            <column name="b_2_g_payment_status_management" type="boolean"/>
            <column name="b_2_g_legal_engagement_management" type="boolean"/>
            <column name="b_2_g_legal_or_service_engagement_management" type="boolean"/>
            <column name="b_2_g_service_code_management" type="boolean"/>
            <column name="diffusible" type="boolean"/>
            <column name="address_postal_code" type="string"/>
            <column name="issuer" type="string"/>
            <column name="client_id" type="string"/>
            <column name="owner" type="string"/>
            <column name="created_date" type="timestamp"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="origin_insert" type="varchar(25)"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
