<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                       ">

    <changeSet id="20241225095855" author="system">
        <dropNotNullConstraint tableName="address_line" columnName="effect_end_date" columnDataType="${datetimeType}"/>
    </changeSet>

    <changeSet id="202412250100705" author="system">
        <modifyDataType tableName="address_line" columnName="client_id" newDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="202412250100708" author="system">
        <modifyDataType tableName="address_line" columnName="owner" newDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="202412250100805" author="system">
        <modifyDataType tableName="siren" columnName="client_id" newDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="202412250100908" author="system">
        <modifyDataType tableName="siren" columnName="owner" newDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="***************" author="system">
        <modifyDataType tableName="siret" columnName="client_id" newDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="202412250101008" author="system">
        <modifyDataType tableName="siret" columnName="owner" newDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="***************" author="system">
        <modifyDataType tableName="siret" columnName="client_id" newDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="***************" author="system">
        <modifyDataType tableName="siret" columnName="owner" newDataType="VARCHAR(255)"/>
    </changeSet>
    <changeSet id="***************" author="system">
        <modifyDataType tableName="routing_code" columnName="client_id" newDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="***************" author="system">
        <modifyDataType tableName="routing_code" columnName="owner" newDataType="VARCHAR(255)"/>
    </changeSet>

</databaseChangeLog>
