<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet  id="**************" author="asghaier">
        <sql dbms="postgresql">
            create index routing_code_label_ind on routing_code (label);
            create index routing_code_label_up_ind on routing_code ((upper(routing_code)));
            create index routing_code_code on routing_code (routing_code);
            create index routing_code_code_up on routing_code ((upper(routing_code)));
        </sql>
    </changeSet>
</databaseChangeLog>
