<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Siret.
    -->
    <changeSet id="20230721131955-1" author="jhipster">
        <createTable tableName="siret">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="siret_num" type="varchar(14)">
                <constraints nullable="false" />
            </column>
            <column name="principal_establishment" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="label" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="address_ligne_1" type="varchar(109)">
                <constraints nullable="false" />
            </column>
            <column name="address_ligne_2" type="varchar(38)">
                <constraints nullable="false" />
            </column>
            <column name="address_ligne_3" type="varchar(26)">
                <constraints nullable="false" />
            </column>
            <column name="town" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="postal_code" type="integer">
                <constraints nullable="false" />
            </column>
            <column name="country" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="modification_date" type="${datetimeType}">
                <constraints nullable="false" />
            </column>
            <column name="fk_siret_siren_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="siret" columnName="modification_date" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20230721131955-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/siret.csv"
                  separator=";"
                  tableName="siret"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="siret_num" type="string"/>
            <column name="principal_establishment" type="string"/>
            <column name="label" type="string"/>
            <column name="address_ligne_1" type="string"/>
            <column name="address_ligne_2" type="string"/>
            <column name="address_ligne_3" type="string"/>
            <column name="town" type="string"/>
            <column name="postal_code" type="numeric"/>
            <column name="country" type="string"/>
            <column name="modification_date" type="date"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
