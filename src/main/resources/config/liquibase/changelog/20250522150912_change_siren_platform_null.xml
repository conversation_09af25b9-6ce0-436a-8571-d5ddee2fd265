<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="20250522150912-1" author="system">
        <comment>Make platform_siren field in platform table nullable</comment>
        <dropNotNullConstraint tableName="platform" columnName="platform_siren" columnDataType="varchar(100)"/>
    </changeSet>

</databaseChangeLog>
