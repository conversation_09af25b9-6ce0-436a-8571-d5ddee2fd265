<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet  id="**************" author="asghaier">
        <sql dbms="postgresql">
            UPDATE "siren" SET "created_by"='liquibase', "last_modified_by"='liquibase', "created_date"= NOW(), "last_modified_date"= NOW(), "origin_insert"='app'  WHERE  1=1;

            UPDATE "siret" SET "created_by"='liquibase', "last_modified_by"='liquibase', "created_date"= NOW(), "last_modified_date"= NOW(), "origin_insert"='app'  WHERE  1=1;

            UPDATE "routing_code" SET "created_by"='liquibase', "last_modified_by"='liquibase', "created_date"= NOW(), "last_modified_date"= NOW(), "origin_insert"='app'  WHERE  1=1;

            UPDATE "address_line" SET "created_by"='liquibase', "last_modified_by"='liquibase', "created_date"= NOW(), "last_modified_date"= NOW(), "origin_insert"='app'  WHERE  1=1;

        </sql>
    </changeSet>
</databaseChangeLog>
