<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="202501171037120-1" author="sofiene">
        <sql>
            CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        </sql>
    </changeSet>

    <changeSet id="202501171037120-2" author="sofiene">
        <createTable tableName="tracking">
            <column name="request_id" type="${uuidType}">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="request_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="requester_siren" type="VARCHAR(9)">
                <constraints nullable="false"/>
            </column>
            <column name="request_content" type="BYTEA"/>
            <column name="request_status" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="comment" type="VARCHAR(255)"/>
        </createTable>

        <addDefaultValue
            columnName="request_id"
            columnDataType="${uuidType}"
            defaultValueComputed="uuid_generate_v4()"
            tableName="tracking"/>

    </changeSet>

</databaseChangeLog>
