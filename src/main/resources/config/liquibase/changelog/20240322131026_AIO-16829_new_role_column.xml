<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="**************-1" author="asghaier">
        <sql dbms="postgresql">
            ALTER TABLE "address_line"
                DROP COLUMN "created_by",
                DROP COLUMN "last_modified_by";
            ALTER TABLE "routing_code"
                DROP COLUMN "created_by",
                DROP COLUMN "last_modified_by";
            ALTER TABLE "siret"
                DROP COLUMN "created_by",
                DROP COLUMN "last_modified_by";
            ALTER TABLE "siren"
                DROP COLUMN "created_by",
                DROP COLUMN "last_modified_by";
        </sql>
    </changeSet>
    <changeSet id="**************-2" author="asghaier">
        <sql dbms="postgresql">
            ALTER TABLE "siren" ADD "issuer" VARCHAR(200) NULL;
            ALTER TABLE "siren" ADD "client_id" VARCHAR(20) NULL;
            ALTER TABLE "siren" ADD "owner" VARCHAR(20) NULL;

            ALTER TABLE "siret" ADD "issuer" VARCHAR(200) NULL;
            ALTER TABLE "siret" ADD "client_id" VARCHAR(20) NULL;
            ALTER TABLE "siret" ADD "owner" VARCHAR(20) NULL;

            ALTER TABLE "routing_code" ADD "issuer" VARCHAR(200) NULL;
            ALTER TABLE "routing_code" ADD "client_id" VARCHAR(20) NULL;
            ALTER TABLE "routing_code" ADD "owner" VARCHAR(20) NULL;

            ALTER TABLE "address_line" ADD "issuer" VARCHAR(200) NULL;
            ALTER TABLE "address_line" ADD "client_id" VARCHAR(20) NULL;
            ALTER TABLE "address_line" ADD "owner" VARCHAR(20) NULL;
        </sql>
    </changeSet>
    <changeSet id="**************-3" author="asghaier">
        <sql dbms="postgresql">
            UPDATE "siren" SET "issuer"='https://auth.staging.apps.generix.biz/auth/realms/legalreferential2', "client_id"='legalref_client', "owner"='legalref_admin' WHERE  1=1;
            UPDATE "siret" SET "issuer"='https://auth.staging.apps.generix.biz/auth/realms/legalreferential2', "client_id"='legalref_client', "owner"='legalref_admin' WHERE  1=1;
            UPDATE "routing_code" SET "issuer"='https://auth.staging.apps.generix.biz/auth/realms/legalreferential2', "client_id"='legalref_client', "owner"='legalref_admin' WHERE  1=1;
            UPDATE "address_line" SET "issuer"='https://auth.staging.apps.generix.biz/auth/realms/legalreferential2', "client_id"='legalref_client', "owner"='legalref_admin' WHERE  1=1;
        </sql>
    </changeSet>
    <changeSet id="**************-4" author="asghaier">
        <sql dbms="postgresql">
            ALTER TABLE "siren" ALTER COLUMN "issuer" TYPE VARCHAR(200), ALTER COLUMN "issuer" SET NOT NULL, ALTER COLUMN "issuer" DROP DEFAULT;
            ALTER TABLE "siren" ALTER COLUMN "client_id" TYPE VARCHAR(20), ALTER COLUMN "client_id" SET NOT NULL, ALTER COLUMN "client_id" DROP DEFAULT;
            ALTER TABLE "siren" ALTER COLUMN "owner" TYPE VARCHAR(20), ALTER COLUMN "owner" DROP NOT NULL, ALTER COLUMN "owner" DROP DEFAULT;

            ALTER TABLE "siret" ALTER COLUMN "issuer" TYPE VARCHAR(200), ALTER COLUMN "issuer" SET NOT NULL, ALTER COLUMN "issuer" DROP DEFAULT;
            ALTER TABLE "siret" ALTER COLUMN "client_id" TYPE VARCHAR(20), ALTER COLUMN "client_id" SET NOT NULL, ALTER COLUMN "client_id" DROP DEFAULT;
            ALTER TABLE "siret" ALTER COLUMN "owner" TYPE VARCHAR(20), ALTER COLUMN "owner" DROP NOT NULL, ALTER COLUMN "owner" DROP DEFAULT;

            ALTER TABLE "routing_code" ALTER COLUMN "issuer" TYPE VARCHAR(200), ALTER COLUMN "issuer" SET NOT NULL, ALTER COLUMN "issuer" DROP DEFAULT;
            ALTER TABLE "routing_code" ALTER COLUMN "client_id" TYPE VARCHAR(20), ALTER COLUMN "client_id" SET NOT NULL, ALTER COLUMN "client_id" DROP DEFAULT;
            ALTER TABLE "routing_code" ALTER COLUMN "owner" TYPE VARCHAR(20), ALTER COLUMN "owner" DROP NOT NULL, ALTER COLUMN "owner" DROP DEFAULT;

            ALTER TABLE "address_line" ALTER COLUMN "issuer" TYPE VARCHAR(200), ALTER COLUMN "issuer" SET NOT NULL, ALTER COLUMN "issuer" DROP DEFAULT;
            ALTER TABLE "address_line" ALTER COLUMN "client_id" TYPE VARCHAR(20), ALTER COLUMN "client_id" SET NOT NULL, ALTER COLUMN "client_id" DROP DEFAULT;
            ALTER TABLE "address_line" ALTER COLUMN "owner" TYPE VARCHAR(20), ALTER COLUMN "owner" DROP NOT NULL, ALTER COLUMN "owner" DROP DEFAULT;
        </sql>
    </changeSet>


</databaseChangeLog>
