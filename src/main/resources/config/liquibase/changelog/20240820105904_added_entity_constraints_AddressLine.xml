<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <!--
        Added the constraints for entity AddressLine.
    -->
    <changeSet id="**************-2" author="asghaier">

        <addForeignKeyConstraint baseColumnNames="fk_address_line_routing_code_instance_id"
                                 baseTableName="address_line"
                                 constraintName="fk_address_line__fk_address_line_routing_code_instance_id"
                                 referencedColumnNames="instance_id"
                                 referencedTableName="routing_code"/>

        <addForeignKeyConstraint baseColumnNames="fk_address_line_siren_instance_id"
                                 baseTableName="address_line"
                                 constraintName="fk_address_line__fk_address_line_siren_instance_id"
                                 referencedColumnNames="instance_id"
                                 referencedTableName="siren"/>

        <addForeignKeyConstraint baseColumnNames="fk_address_line_siret_instance_id"
                                 baseTableName="address_line"
                                 constraintName="fk_address_line__fk_address_line_siret_instance_id"
                                 referencedColumnNames="instance_id"
                                 referencedTableName="siret"/>

        <addForeignKeyConstraint baseColumnNames="fk_address_line_platform_instance_id"
                                 baseTableName="address_line"
                                 constraintName="fk_address_line__fk_address_line_platform_instance_id"
                                 referencedColumnNames="instance_id"
                                 referencedTableName="platform"/>
    </changeSet>
</databaseChangeLog>
