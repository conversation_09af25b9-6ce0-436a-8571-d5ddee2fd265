<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="20250325133000-1" author="system">
        <comment>Make address fields in siret table nullable</comment>
        <dropNotNullConstraint tableName="siret" columnName="address_line_1" columnDataType="varchar(255)"/>
        <dropNotNullConstraint tableName="siret" columnName="address_line_2" columnDataType="varchar(255)"/>
        <dropNotNullConstraint tableName="siret" columnName="address_line_3" columnDataType="varchar(255)"/>
        <dropNotNullConstraint tableName="siret" columnName="address_city" columnDataType="varchar(255)"/>
        <dropNotNullConstraint tableName="siret" columnName="address_country_subdivision" columnDataType="varchar(255)"/>
        <dropNotNullConstraint tableName="siret" columnName="address_country_code" columnDataType="varchar(10)"/>
        <dropNotNullConstraint tableName="siret" columnName="address_country_label" columnDataType="varchar(2)"/>
        <dropNotNullConstraint tableName="siret" columnName="address_postal_code" columnDataType="varchar(10)"/>
    </changeSet>

</databaseChangeLog>
