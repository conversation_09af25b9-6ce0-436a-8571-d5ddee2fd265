<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">


    <changeSet  id="20240910165906" author="asghaier">
        <sql dbms="postgresql">
            create index siren_siren on siren (siren);
            create index siren_entity_type on siren (entity_type);

            create index siret_siret on siret (siret);
            create index siret_denomination on siret (denomination);
            create index siret_address_line_1 on siret (address_line_1);
            create index siret_address_line_2 on siret (address_line_2);
            create index siret_address_line_3 on siret (address_line_3);
            create index siret_address_postal_code on siret (address_postal_code);
            create index siret_address_city on siret (address_city);
            create index siret_address_country_code on siret (address_country_code);
            create index fk_siret_siren_instance_id_ind on siret (fk_siret_siren_instance_id);

            create index routing_code_routing_code_label on routing_code (routing_code_label);
            create index routing_code_routing_code_id on routing_code (routing_code_id);
            create index routing_code_address_line_1 on routing_code (address_line_1);
            create index routing_code_address_line_2 on routing_code (address_line_2);
            create index routing_code_address_line_3 on routing_code (address_line_2);
            create index routing_code_address_postal_code on routing_code (address_postal_code);
            create index routing_code_address_city on routing_code (address_city);
            create index routing_code_address_country_code on routing_code (address_country_code);
            create index fk_routing_code_siret_instance_id_ind on routing_code (fk_routing_code_siret_instance_id);

            create index address_line_address_suffix on address_line (address_suffix);
            create index address_line_effect_begin_date on address_line (effect_begin_date);
            create index address_line_effect_end_date on address_line (effect_end_date);
            create index fk_address_line_routing_code_instance_id_ind on address_line (fk_address_line_routing_code_instance_id);
            create index fk_address_line_siren_instance_id_ind on address_line (fk_address_line_siren_instance_id);
            create index fk_address_line_siret_instance_id_ind on address_line (fk_address_line_siret_instance_id);
            create index fk_address_line_platform_instance_id_ind on address_line (fk_address_line_platform_instance_id);

            create index platform_platform_id on platform (platform_id);
            create index platform_platform_type on platform (platform_type);
            create index platform_platform_status on platform (platform_status);
            create index platform_platform_company_name on platform (platform_company_name);
            create index platform_platform_commercial_name on platform (platform_commercial_name);

        </sql>
    </changeSet>
</databaseChangeLog>
