<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
    ">

    <changeSet id="20241107123457" author="system">
        <update tableName="permissions_matrix">
            <column name="read" value="GRANTED"/>
            <where>role = 'LEGALREF_BUSINESS'</where>
        </update>
        <update tableName="permissions_matrix">
            <column name="create" value="GRANTED"/>
            <where>role = 'LEGALREF_BUSINESS'</where>
        </update>
        <update tableName="permissions_matrix">
            <column name="update" value="GRANTED"/>
            <where>role = 'LEGALREF_BUSINESS'</where>
        </update>
        <update tableName="permissions_matrix">
            <column name="delete" value="GRANTED"/>
            <where>role = 'LEGALREF_BUSINESS'</where>
        </update>
        <update tableName="permissions_matrix">
            <column name="owner" value="GRANTED"/>
            <where>role = 'LEGALREF_BUSINESS'</where>
        </update>
    </changeSet>

</databaseChangeLog>
