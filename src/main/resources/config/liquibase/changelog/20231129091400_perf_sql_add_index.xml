<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet  id="**************" author="asghaier">
        <sql dbms="postgresql">
            CREATE INDEX IF NOT EXISTS fk_siret_siren_id_ind ON siret (fk_siret_siren_id);

            CREATE INDEX IF NOT EXISTS fk_routing_code_siret_id_ind ON routing_code (fk_routing_code_siret_id);

            CREATE INDEX IF NOT EXISTS pk_routing_code_id_ind ON address_line (pk_routing_code_id);
            CREATE INDEX IF NOT EXISTS fk_address_line_siren_id_ind ON address_line (fk_address_line_siren_id);
            CREATE INDEX IF NOT EXISTS fk_address_line_siret_id_ind ON address_line (fk_address_line_siret_id);

        </sql>
    </changeSet>
</databaseChangeLog>
