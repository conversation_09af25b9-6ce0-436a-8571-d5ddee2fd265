<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
    ">

    <changeSet id="********-01-update-routing-code-nullable" author="Mansour SRIDI">
        <comment>Allow address_country_label in routing_code table to be null</comment>

        <dropNotNullConstraint tableName="routing_code" columnName="address_country_label"/>
    </changeSet>
</databaseChangeLog>
