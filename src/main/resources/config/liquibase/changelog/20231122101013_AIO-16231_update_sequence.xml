<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="**************-1" author="asghaier">
        <createSequence sequenceName="sequence_generator_siren" startValue="1" incrementBy="50"/>
        <createSequence sequenceName="sequence_generator_siret" startValue="1" incrementBy="50"/>
        <createSequence sequenceName="sequence_generator_routing" startValue="1" incrementBy="50"/>
        <createSequence sequenceName="sequence_generator_address" startValue="1" incrementBy="50"/>
    </changeSet>

    <changeSet id="**************-2"  author="asghaier">
        <sql dbms="postgresql">select setval('sequence_generator_siren', max(id)+1) from siren</sql>
        <sql dbms="h2">alter sequence sequence_generator_siren restart with (select max(id)+1 from siren)</sql>
    </changeSet>

    <changeSet id="**************-3"  author="asghaier">
        <sql dbms="postgresql">select setval('sequence_generator_siret', max(id)+1) from siret</sql>
        <sql dbms="h2">alter sequence sequence_generator_siret restart with (select max(id)+1 from siret)</sql>
    </changeSet>

    <changeSet id="**************-4"  author="asghaier">
        <sql dbms="postgresql">select setval('sequence_generator_routing', max(id)+1) from routing_code</sql>
        <sql dbms="h2">alter sequence sequence_generator_routing restart with (select max(id)+1 from routing_code)</sql>
    </changeSet>

    <changeSet id="**************-5"  author="asghaier">
        <sql dbms="postgresql">select setval('sequence_generator_address', max(id)+1) from address_line</sql>
        <sql dbms="h2">alter sequence sequence_generator_address restart with (select max(id)+1 from address_line)</sql>
    </changeSet>

</databaseChangeLog>
