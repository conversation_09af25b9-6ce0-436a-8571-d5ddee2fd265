# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    tech.jhipster: DEBUG
    org.hibernate.SQL: DEBUG
    com.generix.legalreferential: DEBUG

management:
  metrics:
    export:
      prometheus:
        enabled: true

spring:
  devtools:
    restart:
      enabled: true
      additional-exclude: static/**
    livereload:
      enabled: false # we use Webpack dev server + BrowserSync for livereload
  jackson:
    serialization:
      indent-output: true
    deserialization:
      fail-on-unknown-properties: true

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: *****************************************
    username: postgres
    password: 123456789
    hikari:
      poolName: Hikari
      auto-commit: false
      schema: public
  jpa:
    database-platform: tech.jhipster.domain.util.FixedPostgreSQL10Dialect
  liquibase:
    # Remove 'faker' if you do not want the sample data to be loaded automatically
    contexts: dev
  messages:
    cache-duration: PT1S # 1 second, see the ISO 8601 standard
  thymeleaf:
    cache: false

server:
  port: 8081

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  cache: # Cache configuration
    ehcache: # Ehcache configuration
      time-to-live-seconds: 3600 # By default objects stay 1 hour in the cache
      max-entries: 100 # Number of objects in each cache entry
  # CORS is disabled by default on microservices, as you should access them through a gateway.
  # If you want to enable it, please uncomment the configuration below.
  # cors:
  #   allowed-origins: "http://localhost:9000,https://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count"
  #   allow-credentials: true
  #   max-age: 1800
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      queue-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
tokenvalidator:
  security:
    url_auth_server: https://auth.staging.apps.generix.biz/auth
    cache_token_delay: 60
    cache_certs_keys_delay: 600

application:
  csp:
    url_auth_server: staging_url
  data:
    local_out_directory: /c:/generyxWs/legalreferential/out
    local_error_directory: /c:/generyxWs/legalreferential/error
    sftp:
      host: eu-central-1.sftpcloud.io
      port: 22
      username: b215cc429fb24520a8cd14870aaf09b8
      password: Ma72lkhQQ3XQbVsEiami0LkRsse3CLNj
      remote_directory: /out/
  code: 'CCCCCC'
  ppf:
    base-url: https://legalref-api.test.apps.generix.biz/
    enabled: true
  piste:
    base-url: https://auth.staging.apps.generix.biz/auth/realms/legalreferential2
    scope: openid
    client-id: piste-test
    client-secret: v7g1xx9edxP6iocqVVyscYLazVD1OhmI
  feign:
    maxAttempts: 3
    backoff: 1000
