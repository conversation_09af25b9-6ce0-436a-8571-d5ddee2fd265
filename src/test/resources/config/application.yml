# ===================================================================
# Spring Boot configuration.
#
# This configuration is used for unit/integration tests.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

spring:
  application:
    name: legalReferential
  # Replace by 'prod, faker' to add the faker context and have sample data loaded in production
  liquibase:
    contexts: test
  jackson:
    serialization:
      write-durations-as-timestamps: false
  mail:
    host: localhost
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  messages:
    basename: i18n/messages
  task:
    execution:
      thread-name-prefix: legal-referential-task-
      pool:
        core-size: 1
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: legal-referential-scheduling-
      pool:
        size: 20
  thymeleaf:
    mode: HTML
  # Allow SecurityConfiguration to initialize w/o specifying an empty issuer-uri is OK
  security:
    oauth2:
      client:
        base-uri: http://DO_NOT_CALL:9080/
        realms-name: jhipster
        login-user-admin: admin
        password-user-admin: admin
        provider:
          oidc:
            issuer-uri: http://DO_NOT_CALL:9080/realms/jhipster

server:
  port: 10344
  address: localhost

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================
jhipster:
  clientApp:
    name: 'legalReferentialApp'
  mail:
    from: <EMAIL>
    base-url: http://127.0.0.1:8080
  logging:
    # To test json console appender
    use-json-format: false
    logstash:
      enabled: false
      host: localhost
      port: 5000
      queue-size: 512

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
management:
  health:
    mail:
      enabled: false
tokenvalidator:
  security:
    url_auth_server: https://auth.staging.apps.generix.biz/auth
    cache_token_delay: 60
    cache_certs_keys_delay: 600

application:
  csp:
    url_auth_server: staging_url
  data:
    local_out_directory: /c:/generyxWs/legalreferential/out
    local_error_directory: /c:/generyxWs/legalreferential/error
    sftp:
      host: eu-central-1.sftpcloud.io
      port: 22
      username: b5cfeb967750452cafeecb0a09216378
      password: 4Vj4RxjbY8ZNeacess8ts8JvF00c9Cz1
      remote_directory: /flux13/in/
  code: 'CCCCCC'
  ppf:
    base-url: https://legalref-api.test.apps.generix.biz/
    enabled: false
  piste:
    base-url: https://auth.staging.apps.generix.biz/auth/realms/legalreferential2
    scope: openid
    client-id: piste-test
    client-secret: v7g1xx9edxP6iocqVVyscYLazVD1OhmI
  feign:
    maxAttempts: 3
    backoff: 1000
