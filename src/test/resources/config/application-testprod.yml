# ===================================================================
# Spring Boot configuration.
#
# This configuration is used for unit/integration tests with testcontainers database containers.
#
# To activate this configuration launch integration tests with the 'testcontainers' profile
#
# More information on database containers: https://www.testcontainers.org/modules/databases/
# ===================================================================

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      poolName: Hikari
      auto-commit: false
      maximum-pool-size: 1
  jpa:
    database-platform: tech.jhipster.domain.util.FixedPostgreSQL10Dialect
    open-in-view: false
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    properties:
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: false
      hibernate.hbm2ddl.auto: validate
      hibernate.jdbc.time_zone: UTC
      hibernate.query.fail_on_pagination_over_collection_fetch: true
tokenvalidator:
  security:
    url-base-keycloak: https://auth.staging.apps.generix.biz/auth
    delay-cache-token: 7200
    delay-cache-certs-keys: 86400
application:
  csp:
    url_auth_server: staging_url
  data:
    local_out_directory: /c:/generyxWs/legalreferential/out
    local_error_directory: /c:/generyxWs/legalreferential/error
    sftp:
      host: eu-central-1.sftpcloud.io
      port: 22
      username: b5cfeb967750452cafeecb0a09216378
      password: 4Vj4RxjbY8ZNeacess8ts8JvF00c9Cz1
      remote_directory: /flux13/in/
  code: application_code
