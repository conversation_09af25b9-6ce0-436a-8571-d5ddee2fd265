package com.generix.legalreferential.service.common;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import java.net.URI;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@ExtendWith(MockitoExtension.class)
class UtilsServiceTest {

    @BeforeEach
    void setup() {
        RequestContextHolder.resetRequestAttributes();
    }

    @Test
    void testGetCurrentRequestUri_WithActiveRequest() {
        MockHttpServletRequest mockRequest = new MockHttpServletRequest();
        mockRequest.setRequestURI("/api/test");
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockRequest));

        URI uri = UtilsService.getCurrentRequestUri();
        assertNotNull(uri);
        assertEquals("/api/test", uri.toString());
    }

    @Test
    void testGetCurrentRequestUri_WithoutActiveRequest() {
        RequestContextHolder.resetRequestAttributes();
        assertNull(UtilsService.getCurrentRequestUri());
    }

    @Test
    void testGetFieldPath() {
        JsonMappingException.Reference ref1 = new JsonMappingException.Reference(null, "field1");
        JsonMappingException.Reference ref2 = new JsonMappingException.Reference(null, "field2");
        InvalidFormatException ex = new InvalidFormatException(null, "Test Message", null, String.class);
        ex.prependPath(ref1);
        ex.prependPath(ref2);

        String fieldPath = UtilsService.getFieldPath(ex);
        assertEquals("field2.field1", fieldPath);
    }

    @Test
    void testGetIdentifiantAdressageForAddressLine() {
        String result = UtilsService.getIdentifiantAdressageForAddressLine("123456789", "SUF", "98765432100012", "RT123");
        assertEquals("123456789_SUF_98765432100012_RT123", result);
    }

    @Test
    void testGetIdentifiantAdressageForAddressLine_WithNullValues() {
        String result = UtilsService.getIdentifiantAdressageForAddressLine("123456789", null, null, null);
        assertEquals("123456789", result);
    }

    @Test
    void testGetIdentifiantAdressageForAddressLine_WithEmptyStrings() {
        String result = UtilsService.getIdentifiantAdressageForAddressLine("123456789", "", "", "");
        assertEquals("123456789", result);
    }

    @Test
    void testGetIdentifiantAdressageForAddressLine_ThrowsExceptionForEmptySiren() {
        IllegalArgumentException thrown = assertThrows(
            IllegalArgumentException.class,
            () -> UtilsService.getIdentifiantAdressageForAddressLine("   ", "SUF", "98765432100012", "RT123")
        );
        assertEquals("Le SIREN ne peut pas être null.", thrown.getMessage());
    }
}
