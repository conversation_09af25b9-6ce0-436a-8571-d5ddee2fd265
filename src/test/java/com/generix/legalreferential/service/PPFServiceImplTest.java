package com.generix.legalreferential.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.generix.legalreferential.domain.AddressLine;
import com.generix.legalreferential.repository.AddressLineRepository;
import com.generix.legalreferential.service.dto.custom.annuaire.AnnuaireAddressLineDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.PeriodeEffetDTO;
import com.generix.legalreferential.service.dto.custom.request.AnnuaireAdressLineRequestBody;
import com.generix.legalreferential.service.dto.custom.response.AnnuaireAddressLineRequestResponse;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PPFServiceImplTest {

    @Mock
    private AddressLineRepository addressLineRepository;

    @InjectMocks
    private PPFServiceImpl ppfService;

    @BeforeEach
    void setUp() {
        lenient().when(addressLineRepository.getMaxInstanceId()).thenReturn(100L);
    }

    @Test
    void testAddAnnuaireAdresseLine() {
        // Mock input data
        AnnuaireAdressLineRequestBody request = new AnnuaireAdressLineRequestBody();
        PeriodeEffetDTO periodeEffet = new PeriodeEffetDTO();
        periodeEffet.setDateDebutEffet("2025-01-01");
        request.setPeriodeEffet(periodeEffet);

        // Call service method
        AnnuaireAddressLineRequestResponse response = ppfService.addAnnuaireAdresseLine(request, List.of("test1", "test2"));

        // Validate response
        assertNotNull(response);
        assertEquals(101L, response.getIdInstance());
        assertEquals("adr101", response.getIdentifiantAdressage());
        assertEquals("2025-01-01", response.getDateDebutEffet());
    }

    @Test
    void testDeleteAnnuaireAdresseLine() {
        Boolean result = ppfService.deleteAnnuaireAdresseLine(123L);
        assertTrue(result);
    }

    @Test
    void testGetLigneAnnuaireAdresseLineByInstance() {
        AnnuaireAddressLineDTO dto = ppfService.getLigneAnnuaireAdresseLineByInstance(123L, List.of("champ1", "champ2"));
        assertNotNull(dto);
    }

    @Test
    void testPatchLigneAnnuaireAdresseLine() {
        AddressLine addressLine = new AddressLine();
        addressLine.setInstanceID(200L);
        addressLine.setAddressLineID("adr200");
        addressLine.setEffectBeginDate(java.time.LocalDate.parse("2024-12-30"));

        AnnuaireAdressLineRequestBody updateBody = new AnnuaireAdressLineRequestBody();
        PeriodeEffetDTO periodeEffet = new PeriodeEffetDTO();
        periodeEffet.setDateDebutEffet("2025-02-15");
        updateBody.setPeriodeEffet(periodeEffet);

        AnnuaireAddressLineRequestResponse response = ppfService.patchLigneAnnuaireAdresseLine(200L, updateBody, addressLine);

        assertNotNull(response);
        assertEquals(200L, response.getIdInstance());
        assertEquals("adr200", response.getIdentifiantAdressage());
        assertEquals("2025-02-15", response.getDateDebutEffet());
    }

    @Test
    void testPatchLigneAnnuaireAdresseLine_NullDate() {
        AddressLine addressLine = new AddressLine();
        addressLine.setInstanceID(300L);
        addressLine.setAddressLineID("adr300");
        addressLine.setEffectBeginDate(java.time.LocalDate.parse("2024-11-20"));

        AnnuaireAdressLineRequestBody updateBody = new AnnuaireAdressLineRequestBody();
        updateBody.setPeriodeEffet(null); // No update in request

        AnnuaireAddressLineRequestResponse response = ppfService.patchLigneAnnuaireAdresseLine(300L, updateBody, addressLine);

        assertNotNull(response);
        assertEquals(300L, response.getIdInstance());
        assertEquals("adr300", response.getIdentifiantAdressage());
        assertEquals("2024-11-20", response.getDateDebutEffet()); // Should fall back to existing date
    }
}
