package com.generix.legalreferential.service.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import com.generix.legalreferential.service.*;
import com.generix.legalreferential.service.dto.*;
import java.time.LocalDate;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ValidatorServiceImplTest {

    @InjectMocks
    private ValidatorServiceImpl validatorService;

    @Mock
    private SirenService sirenService;

    @Mock
    private SiretService siretService;

    @Mock
    private PlatformService platformService;

    @Mock
    private RoutingCodeService routingCodeService;

    @Mock
    private AddressLineService addressLineService;

    @Test
    void testIsSiretAndSuffixeCoexist() {
        assertTrue(validatorService.isSiretAndSuffixeCoexist("**************", "001"));
        assertFalse(validatorService.isSiretAndSuffixeCoexist(null, "001"));
        assertFalse(validatorService.isSiretAndSuffixeCoexist("**************", null));
        assertFalse(validatorService.isSiretAndSuffixeCoexist("", ""));
    }

    @Test
    void testIsSiretStartsWithSiren() {
        assertTrue(validatorService.isSiretStartsWithSiren("*********", "**************"));
        assertFalse(validatorService.isSiretStartsWithSiren("*********", "**************"));
        assertFalse(validatorService.isSiretStartsWithSiren(null, "**************"));
        assertFalse(validatorService.isSiretStartsWithSiren("*********", null));
    }

    @Test
    void testIsRoutingCodeExistWithoutSiret() {
        assertTrue(validatorService.isRoutingCodeExistWithoutSiret(null, "RC123"));
        assertFalse(validatorService.isRoutingCodeExistWithoutSiret("**************", "RC123"));
        assertFalse(validatorService.isRoutingCodeExistWithoutSiret(null, null));
    }

    @Test
    void testIsDateRangeValid() {
        LocalDate today = LocalDate.now();
        String futureDate1 = today.plusDays(5).toString();
        String futureDate2 = today.plusDays(6).toString();
        String pastDate = today.minusDays(5).toString();

        assertTrue(validatorService.isDateRangeValidAndGreaterOrEqualToday(futureDate1, futureDate2));
        assertFalse(validatorService.isDateRangeValidAndGreaterOrEqualToday(pastDate, futureDate1));
        assertFalse(validatorService.isDateRangeValidAndGreaterOrEqualToday(futureDate1, pastDate));
        assertFalse(validatorService.isDateRangeValidAndGreaterOrEqualToday(pastDate, null));
        assertTrue(validatorService.isDateRangeValidAndGreaterOrEqualToday(futureDate1, null));
        assertTrue(validatorService.isDateRangeValidAndGreaterOrEqualToday(today.toString(), null));
        assertTrue(validatorService.isDateRangeValidAndGreaterOrEqualToday(today.toString(), futureDate1));
    }

    @Test
    void testIsDateRangeValidStrict() {
        LocalDate today = LocalDate.now();
        String futureDate1 = today.plusDays(5).toString();
        String futureDate2 = today.plusDays(6).toString();

        assertTrue(validatorService.isDateRangeValidAndGreaterThenToday(futureDate1, futureDate2));
        assertFalse(validatorService.isDateRangeValidAndGreaterThenToday(today.toString(), futureDate1));
        assertTrue(validatorService.isDateRangeValidAndGreaterThenToday(futureDate1, null));
        assertFalse(validatorService.isDateRangeValidAndGreaterThenToday(today.toString(), null));
    }

    @Test
    void testIsSirenPresent() {
        when(sirenService.findLatestActiveSiren("*********")).thenReturn(Optional.of(new SirenDTO()));
        when(sirenService.findLatestActiveSiren("*********")).thenReturn(Optional.empty());

        assertTrue(validatorService.isSirenPresent("*********"));
        assertFalse(validatorService.isSirenPresent("*********"));
    }

    @Test
    void testIsPlatformPresent() {
        when(platformService.findByMatricule("PLATFORM_001")).thenReturn(Optional.of(new PlatformDTO()));
        when(platformService.findByMatricule("PLATFORM_002")).thenReturn(Optional.empty());

        assertTrue(validatorService.isPlatformPresent("PLATFORM_001"));
        assertFalse(validatorService.isPlatformPresent("PLATFORM_002"));
    }

    @Test
    void testIsSiretPresent() {
        when(siretService.findLatestActiveSiret("**************")).thenReturn(Optional.of(new SiretDTO()));
        when(siretService.findLatestActiveSiret("*********09876")).thenReturn(Optional.empty());

        assertTrue(validatorService.isSiretPresent("**************"));
        assertFalse(validatorService.isSiretPresent("*********09876"));
    }

    @Test
    void testIsRoutingCodePresent() {
        when(routingCodeService.findByRoutingCodeID("RC123")).thenReturn(Optional.of(new RoutingCodeDTO()));
        when(routingCodeService.findByRoutingCodeID("RC999")).thenReturn(Optional.empty());

        assertTrue(validatorService.isRoutingCodePresent("RC123"));
        assertFalse(validatorService.isRoutingCodePresent("RC999"));
    }

    @Test
    void testIsAddressLineExists() {
        when(addressLineService.isUniqueForPeriod(any(), any(), any(), any(), any(), any(), any())).thenReturn(false);

        assertTrue(
            validatorService.isAddressLineExists("*********", "001", "**************", "RC123", "PLATFORM_001", "2025-01-01", "2025-12-31")
        );
    }

    @Test
    void testHasOldDateFinWhenUpdatingDateFin() {
        AddressLineDTO addressLineDTO = new AddressLineDTO();
        addressLineDTO.setEffectEndDate(LocalDate.of(2025, 1, 1));

        assertTrue(validatorService.hasOldDateFinWhenUpdatingDateFin(addressLineDTO, "2025-01-01"));
        assertFalse(validatorService.hasOldDateFinWhenUpdatingDateFin(addressLineDTO, null));
    }

    @Test
    void testIsNewDateFinValidWhenUpdating() {
        AddressLineDTO addressLineDTO = new AddressLineDTO();
        addressLineDTO.setEffectBeginDate(LocalDate.of(2025, 1, 1));

        assertTrue(validatorService.isNewDateFinValidWhenUpdating(addressLineDTO, "2025-12-31"));
        assertFalse(validatorService.isNewDateFinValidWhenUpdating(addressLineDTO, "2024-12-31"));
    }

    @Test
    void testIsAbleToUpdatePlatform() {
        assertTrue(validatorService.isAbleToUpdatePlatform(LocalDate.now().plusDays(1), "PLATFORM_001"));
        assertFalse(validatorService.isAbleToUpdatePlatform(LocalDate.now().minusDays(1), "PLATFORM_001"));
    }

    @Test
    void testIsAbleToDeleteAddressLine() {
        assertTrue(validatorService.isAbleToDeleteAddressLine(LocalDate.now().plusDays(1)));
        assertFalse(validatorService.isAbleToDeleteAddressLine(LocalDate.now().minusDays(1)));
    }
}
