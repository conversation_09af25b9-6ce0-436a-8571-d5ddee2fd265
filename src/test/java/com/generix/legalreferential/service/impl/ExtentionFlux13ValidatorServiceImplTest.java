package com.generix.legalreferential.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.generix.legalreferential.domain.enumeration.NatureEtablissement;
import com.generix.legalreferential.domain.enumeration.Operation;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.service.*;
import com.generix.legalreferential.service.dto.custom.annuaire.AnnuaireAdresseDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.InformationAdressageDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.PeriodeEffetDTO;
import com.generix.legalreferential.service.dto.custom.flux13.AnnuaireActualisationRequestDTO;
import com.generix.legalreferential.service.dto.custom.flux13.CodeRoutageRequestDTO;
import com.generix.legalreferential.service.dto.custom.flux13.LigneAnnuaireRequestDTO;
import com.generix.legalreferential.service.error.AnnuaireException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.zalando.problem.Status;

@ExtendWith(MockitoExtension.class)
class ExtentionFlux13ValidatorServiceImplTest {
    //
    //    @InjectMocks
    //    private ExtentionFlux13ValidatorServiceImpl extentionFlux13ValidatorService;
    //
    //    @Mock
    //    private ValidatorService mockValidatorService;
    //
    //    @Mock
    //    private AddressLineService mockAddressLineService;
    //
    //    @Mock
    //    private SirenService mockSirenService;
    //
    //    @Mock
    //    private SiretService mockSiretService;
    //
    //    @Mock
    //    private RoutingCodeService mockRoutingCodeService;
    //
    //    private static AnnuaireActualisationRequestDTO requestDTO;
    //    private static List<LigneAnnuaireRequestDTO> listLigneAnnuaireRequestDTO;
    //    private static List<CodeRoutageRequestDTO> listCodeRoutageRequestDTO;
    //
    //
    //    /**
    //     * Sets up the validator for validation before each test.
    //     * This method is executed before each test method to initialize the
    //     * validator used for constraint validation.
    //     */
    //    @NotNull
    //    private static LigneAnnuaireRequestDTO initLigneAnnuaireRequestDTO() {
    //        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = new LigneAnnuaireRequestDTO();
    //        ligneAnnuaireRequestDTO.setOperation(Operation.Creation);
    //        PeriodeEffetDTO periodeEffetDTO = new PeriodeEffetDTO();
    //        periodeEffetDTO.setDateDebutEffet("2023-01-01");
    //        periodeEffetDTO.setDateFinEffet("2023-01-31");
    //        ligneAnnuaireRequestDTO.setPeriodeEffet(periodeEffetDTO);
    //        InformationAdressageDTO informationAdressageDTO = new InformationAdressageDTO();
    //        informationAdressageDTO.setSiren("702042755");
    //        informationAdressageDTO.setSiret("70204275500240");
    //        informationAdressageDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
    //        informationAdressageDTO.setSuffixeAdressage("00000");
    //        informationAdressageDTO.setMatriculePlateforme("0145");
    //        ligneAnnuaireRequestDTO.setInformationAdressage(informationAdressageDTO);
    //        return ligneAnnuaireRequestDTO;
    //    }
    //
    //    /**
    //     * Creates and returns a {@link CodeRoutageRequestDTO} with predefined valid data.
    //     *
    //     * @return a valid instance of {@link CodeRoutageRequestDTO}
    //     */
    //    @NotNull
    //    private static CodeRoutageRequestDTO initCodeRoutageRequestDTO() {
    //        CodeRoutageRequestDTO codeRoutageRequestDTO = new CodeRoutageRequestDTO();
    //        codeRoutageRequestDTO.setNatureEtablissement(NatureEtablissement.Privée);
    //        codeRoutageRequestDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
    //        codeRoutageRequestDTO.setSiret("70204275500240");
    //        codeRoutageRequestDTO.setTypeIdentifiantRoutage("0224");
    //        codeRoutageRequestDTO.setLibelleCodeRoutage("Libellé Code routage");
    //        codeRoutageRequestDTO.setGestionEngagementJuridique(true);
    //        codeRoutageRequestDTO.setEtatAdministratif(Statut.A);
    //        AnnuaireAdresseDTO annuaireAdresseDTO = new AnnuaireAdresseDTO();
    //        annuaireAdresseDTO.setLigneAdresse1("16 BIS RUE HENRI BARBUSSE");
    //        annuaireAdresseDTO.setLigneAdresse2("CEDEX 1");
    //        annuaireAdresseDTO.setLigneAdresse3("Bâtiment le Callipso");
    //        annuaireAdresseDTO.setCodePostal("38100");
    //        annuaireAdresseDTO.setSubDivisionPays("Bretagne");
    //        annuaireAdresseDTO.setLocalite("Grenoble");
    //        annuaireAdresseDTO.setCodePays("FR");
    //        annuaireAdresseDTO.setLibellePays("France");
    //        codeRoutageRequestDTO.setAdresse(annuaireAdresseDTO);
    //        return codeRoutageRequestDTO;
    //    }
    //
    //    @NotNull
    //    private static AnnuaireActualisationRequestDTO initAnnuaireActualisationRequestDTO() {
    //        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();
    //
    //        listLigneAnnuaireRequestDTO = new ArrayList<>();
    //        CodeRoutageRequestDTO codeRoutageRequestDTO = initCodeRoutageRequestDTO();
    //        listLigneAnnuaireRequestDTO.add(codeRoutageRequestDTO);
    //
    //        List<LigneAnnuaireRequestDTO> validLignesAdressage = new ArrayList<>();
    //        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = initLigneAnnuaireRequestDTO();
    //        validLignesAdressage.add(ligneAnnuaireRequestDTO);
    //
    //        dto.setCodesRoutage(validCodesRoutage);
    //        dto.setLignesAdressage(validLignesAdressage);
    //
    //        return dto;
    //    }
    //
    //    @BeforeAll
    //    static void beforeAll() {
    //        requestDTO = initAnnuaireActualisationRequestDTO();
    //    }
    //
    //    @BeforeEach
    //    void beforeEach() {
    //        extentionFlux13ValidatorService = new ExtentionFlux13ValidatorServiceImpl(mockValidatorService, mockAddressLineService, mockSirenService, mockSiretService,mockRoutingCodeService);
    //    }
    //
    //    /*** 1. Test for SIRET record presence and activity ***/
    //    @Test
    //    void shouldThrowExceptionWhenSiretNotActive() {
    //        when(mockSiretService.findLatestActiveSiret(anyString())).thenReturn(Optional.empty());
    //
    //        AnnuaireException exception = assertThrows(AnnuaireException.class,
    //            () -> extentionFlux13ValidatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    //
    //        assertTrue(exception.getStatus().equals(Status.BAD_REQUEST));
    //        assertTrue(exception.getMessage().contains("Il n'existe pas de Siret actif"));
    //    }
    //
    ////    /*** 2. Test for duplicate RoutingCode with same SIRET ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenDuplicateRoutingCodeExists() {
    ////        codeRoutageRequestDTO.setSiret("**************");
    ////        codeRoutageRequestDTO.setIdentifiantRoutage("RC1");
    ////        requestDTO.setCodesRoutage(List.of(codeRoutageRequestDTO, codeRoutageRequestDTO));
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Il existe déjà un autre RoutingCode"));
    ////    }
    ////
    ////    /*** 3. Test for SIRET and suffix coexistence ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenSiretAndSuffixCoexist() {
    ////        when(mockValidatorService.isSiretAndSuffixeCoexist(any(), any())).thenReturn(true);
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Un Suffixe d'addressage ne peut être indiqué"));
    ////    }
    ////
    ////    /*** 4. Test for SIRET starting with SIREN ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenSiretDoesNotStartWithSiren() {
    ////        when(mockValidatorService.isSiretStartsWithSiren(any(), any())).thenReturn(false);
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Le numéro Siret doit commencer par le Siren spécifié"));
    ////    }
    ////
    ////    /*** 5. Test for RoutingCode existence without SIRET ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenRoutingCodeExistsWithoutSiret() {
    ////        when(mockValidatorService.isRoutingCodeExistWithoutSiret(any(), any())).thenReturn(true);
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Le Code Routage ne doit pas exister sans un Siret"));
    ////    }
    ////
    ////    /*** 6. Test for invalid date range in Creation ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenInvalidDateRangeInCreation() {
    ////        when(mockValidatorService.isDateRangeValid(any(), any())).thenReturn(false);
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Aujourd'hui <= dateDebutEffet < dateFinEffet"));
    ////    }
    ////
    ////    /*** 7. Test for invalid date range in Annulation ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenInvalidDateRangeInAnnulation() {
    ////        when(mockValidatorService.isDateRangeValidStrict(any(), any())).thenReturn(false);
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Aujourd'hui < dateDebutEffet"));
    ////    }
    ////
    ////    /*** 8. Test for duplicate address line in request ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenDuplicateAddressLineExists() {
    ////        when(mockValidatorService.compareTwoLigneAnnuaireRequestDTO(any(), any())).thenReturn(true);
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Il existe déjà un autre adresse line"));
    ////    }
    ////
    ////    /*** 9. Test for platform presence ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenPlatformNotPresent() {
    ////        when(mockValidatorService.isPlatformPresent(any())).thenReturn(false);
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Il n'existe pas de plateforme"));
    ////    }
    ////
    ////    /*** 10. Test for active SIREN presence ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenActiveSirenNotPresent() {
    ////        when(mockSirenService.findLatestActiveSiren(any())).thenReturn(Optional.empty());
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Il n'existe pas de Siren actif"));
    ////    }
    ////
    ////    /*** 11. Test for SIRET belonging to SIREN ***/
    ////    @Test
    ////    void shouldThrowExceptionWhenSiretNotMatchingSiren() {
    ////        SiretDTO siretDTO = new SiretDTO();
    ////        siretDTO.setFkSiretSiren(new SirenDTO("DifferentID"));
    ////        when(mockSiretService.findLatestActiveSiret(any())).thenReturn(Optional.of(siretDTO));
    ////
    ////        AnnuaireException exception = assertThrows(AnnuaireException.class,
    ////            () -> validatorService.validateAnnuaireActualisationRequestDTO(requestDTO));
    ////
    ////        assertTrue(exception.getMessage().contains("Le SIRET fourni ne correspond pas au SIREN indiqué"));
    ////    }
    //
    //    // Add remaining tests covering all 21 validation checks...
}
