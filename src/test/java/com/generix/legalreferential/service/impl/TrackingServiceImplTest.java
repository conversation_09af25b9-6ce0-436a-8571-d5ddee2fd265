package com.generix.legalreferential.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.generix.legalreferential.domain.Tracking;
import com.generix.legalreferential.domain.enumeration.Flux13RequestStatus;
import com.generix.legalreferential.repository.TrackingRepository;
import com.generix.legalreferential.service.dto.TrackingDTO;
import com.generix.legalreferential.service.mapper.TrackingMapper;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.*;

class TrackingServiceImplTest {

    @Mock
    private TrackingRepository trackingRepository;

    @Mock
    private TrackingMapper trackingMapper;

    @InjectMocks
    private TrackingServiceImpl trackingService;

    private TrackingDTO trackingDTO;
    private Tracking tracking;
    private UUID trackingId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        trackingId = UUID.randomUUID();

        trackingDTO = new TrackingDTO();
        trackingDTO.setRequestId(trackingId);
        trackingDTO.setRequestDate(LocalDateTime.now());
        trackingDTO.setRequesterSiren("12345678901234");
        trackingDTO.setRequestStatus(Flux13RequestStatus.Pending);
        trackingDTO.setComment("Test comment");

        tracking = new Tracking();
        tracking.setRequestId(trackingId);
        tracking.setRequestDate(trackingDTO.getRequestDate());
        tracking.setRequesterSiren(trackingDTO.getRequesterSiren());
        tracking.setRequestStatus(trackingDTO.getRequestStatus());
        tracking.setComment(trackingDTO.getComment());
    }

    @Test
    void testSave() {
        when(trackingMapper.toEntity(trackingDTO)).thenReturn(tracking);
        when(trackingRepository.save(tracking)).thenReturn(tracking);
        when(trackingMapper.toDto(tracking)).thenReturn(trackingDTO);

        TrackingDTO result = trackingService.save(trackingDTO);

        assertNotNull(result);
        assertEquals(trackingId, result.getRequestId());
        verify(trackingMapper, times(1)).toEntity(trackingDTO);
        verify(trackingRepository, times(1)).save(tracking);
        verify(trackingMapper, times(1)).toDto(tracking);
    }

    @Test
    void testUpdate() {
        when(trackingMapper.toEntity(trackingDTO)).thenReturn(tracking);
        when(trackingRepository.save(tracking)).thenReturn(tracking);
        when(trackingMapper.toDto(tracking)).thenReturn(trackingDTO);

        TrackingDTO result = trackingService.update(trackingDTO);

        assertNotNull(result);
        assertEquals(trackingId, result.getRequestId());
        verify(trackingMapper, times(1)).toEntity(trackingDTO);
        verify(trackingRepository, times(1)).save(tracking);
        verify(trackingMapper, times(1)).toDto(tracking);
    }

    @Test
    void testPartialUpdate() {
        when(trackingRepository.findById(trackingId)).thenReturn(Optional.of(tracking));
        doNothing().when(trackingMapper).partialUpdate(tracking, trackingDTO);
        when(trackingRepository.save(tracking)).thenReturn(tracking);
        when(trackingMapper.toDto(tracking)).thenReturn(trackingDTO);

        Optional<TrackingDTO> result = trackingService.partialUpdate(trackingDTO);

        assertTrue(result.isPresent());
        assertEquals(trackingId, result.get().getRequestId());
        verify(trackingRepository, times(1)).findById(trackingId);
        verify(trackingMapper, times(1)).partialUpdate(tracking, trackingDTO);
        verify(trackingRepository, times(1)).save(tracking);
        verify(trackingMapper, times(1)).toDto(tracking);
    }

    @Test
    void testFindAll() {
        Pageable pageable = PageRequest.of(0, 10);
        List<Tracking> trackingList = List.of(tracking);
        Page<Tracking> trackingPage = new PageImpl<>(trackingList, pageable, trackingList.size());

        when(trackingRepository.findAll(pageable)).thenReturn(trackingPage);
        when(trackingMapper.toDto(any(Tracking.class))).thenReturn(trackingDTO);

        Page<TrackingDTO> result = trackingService.findAll(pageable);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(trackingRepository, times(1)).findAll(pageable);
        verify(trackingMapper, times(1)).toDto(any(Tracking.class));
    }

    @Test
    void testFindOne() {
        when(trackingRepository.findById(trackingId)).thenReturn(Optional.of(tracking));
        when(trackingMapper.toDto(tracking)).thenReturn(trackingDTO);

        Optional<TrackingDTO> result = trackingService.findOne(trackingId);

        assertTrue(result.isPresent());
        assertEquals(trackingId, result.get().getRequestId());
        verify(trackingRepository, times(1)).findById(trackingId);
        verify(trackingMapper, times(1)).toDto(tracking);
    }

    @Test
    void testFindOne_NotFound() {
        when(trackingRepository.findById(trackingId)).thenReturn(Optional.empty());

        Optional<TrackingDTO> result = trackingService.findOne(trackingId);

        assertFalse(result.isPresent());
        verify(trackingRepository, times(1)).findById(trackingId);
        verify(trackingMapper, never()).toDto(any(Tracking.class));
    }

    @Test
    void testDelete() {
        doNothing().when(trackingRepository).deleteById(trackingId);

        trackingService.delete(trackingId);

        verify(trackingRepository, times(1)).deleteById(trackingId);
    }
}
