package com.generix.legalreferential.service.impl;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.testng.AssertJUnit.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.generix.legalreferential.config.SftpServiceConfiguration;
import com.generix.legalreferential.domain.enumeration.Flux13RequestStatus;
import com.generix.legalreferential.service.ExtentionFlux13ValidatorService;
import com.generix.legalreferential.service.TrackingService;
import com.generix.legalreferential.service.dto.TrackingDTO;
import com.generix.legalreferential.service.dto.custom.flux13.AnnuaireActualisationRequestDTO;
import com.generix.legalreferential.service.dto.custom.flux13.AnnuaireActualisationServiceResponseDTO;
import com.generix.legalreferential.service.error.AnnuaireException;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.sql.Blob;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import javax.sql.rowset.serial.SerialBlob;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.zalando.problem.Status;

@ExtendWith(MockitoExtension.class)
class ExtentionFlux13ServiceImplTest {

    @Mock
    private TrackingService trackingService;

    @Mock
    private ExtentionFlux13ValidatorService extentionFlux13ValidatorService;

    @Mock
    private SftpServiceConfiguration sftpServiceConfiguration;

    @InjectMocks
    private ExtentionFlux13ServiceImpl extentionFlux13Service;

    private AnnuaireActualisationRequestDTO requestDTO;
    private TrackingDTO trackingDTO;

    @BeforeEach
    void setUp() throws Exception {
        requestDTO = new AnnuaireActualisationRequestDTO(); // Mock request DTO
        String jsonPayload = new ObjectMapper().writeValueAsString(requestDTO);
        Blob requestContent = new SerialBlob(jsonPayload.getBytes(StandardCharsets.UTF_8));
        trackingDTO = new TrackingDTO(LocalDateTime.now(), "123456789", requestContent, Flux13RequestStatus.Pending);
        trackingDTO.setRequestId(java.util.UUID.randomUUID());

        // Set application code using reflection
        ReflectionTestUtils.setField(extentionFlux13Service, "applicationCode", "testCode");

        // Mock Tracking Service Save
        lenient().when(trackingService.save(any(TrackingDTO.class))).thenReturn(trackingDTO);

        // Mock validator service (no exception means validation passes)
        lenient().doNothing().when(extentionFlux13ValidatorService).validateAnnuaireActualisationRequestDTO(any());

        // Mock SFTP service
        lenient().when(sftpServiceConfiguration.transferFile(any(File.class))).thenReturn("/path/to/transferred/file.xml");
    }

    @Test
    void testSuccess() throws Exception {
        // Act
        AnnuaireActualisationServiceResponseDTO result = extentionFlux13Service.actualisationAnnuaire(requestDTO, "123456789");

        // Assert
        assertNotNull(result);
        assertEquals(trackingDTO.getRequestDate(), result.getTrackingDTO().getRequestDate());
        verify(trackingService, times(1)).save(any(TrackingDTO.class));
    }

    @Test
    void testDirectoryCreation() throws Exception {
        // Act
        AnnuaireActualisationServiceResponseDTO result = extentionFlux13Service.actualisationAnnuaire(requestDTO, "123456789");

        // Assert - Just verify the service returns a valid response
        assertNotNull(result);
        assertNotNull(result.getTrackingDTO());
        verify(trackingService, times(1)).save(any(TrackingDTO.class));
    }

    @Test
    void testXmlFileGeneration() throws Exception {
        // Act
        AnnuaireActualisationServiceResponseDTO result = extentionFlux13Service.actualisationAnnuaire(requestDTO, "123456789");

        // Assert - Just verify the service returns a valid response and SFTP was called
        assertNotNull(result);
        assertNotNull(result.getTrackingDTO());
        verify(trackingService, times(1)).save(any(TrackingDTO.class));
        verify(sftpServiceConfiguration, times(1)).transferFile(any(File.class));
    }

    @Test
    void testTrackingSaveFailure() {
        // Mock failure case
        when(trackingService.save(any(TrackingDTO.class))).thenReturn(null);

        // Act & Assert
        AnnuaireException thrown = assertThrows(
            AnnuaireException.class,
            () -> extentionFlux13Service.actualisationAnnuaire(requestDTO, "123456789")
        );

        assertEquals(Status.INTERNAL_SERVER_ERROR, thrown.getStatus());
        verify(trackingService, times(1)).save(any(TrackingDTO.class));
    }
}
