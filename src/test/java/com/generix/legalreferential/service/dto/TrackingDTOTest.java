package com.generix.legalreferential.service.dto;

import static org.junit.jupiter.api.Assertions.*;

import com.generix.legalreferential.domain.enumeration.Flux13RequestStatus;
import java.sql.Blob;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TrackingDTOTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testValidTrackingDTO() throws SQLException {
        TrackingDTO dto = new TrackingDTO();
        dto.setRequestId(UUID.randomUUID());
        dto.setRequestDate(LocalDateTime.now());
        dto.setRequesterSiren("123456789"); // 9 chars - valid SIREN
        dto.setRequestContent(new javax.sql.rowset.serial.SerialBlob("Sample content".getBytes()));
        dto.setRequestStatus(Flux13RequestStatus.Pending);
        dto.setUpdatedDate(LocalDateTime.now());
        dto.setComment("This is a valid comment.");

        Set<ConstraintViolation<TrackingDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "DTO should be valid");
    }

    @Test
    void testNullRequestDate() {
        TrackingDTO dto = new TrackingDTO();
        dto.setRequestId(UUID.randomUUID());
        dto.setRequestDate(null); // Invalid
        dto.setRequesterSiren("123456789");
        dto.setRequestContent(createBlob("Test"));
        dto.setRequestStatus(Flux13RequestStatus.Pending);
        dto.setComment("Valid comment");

        Set<ConstraintViolation<TrackingDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "Request date should not be null");
    }

    @Test
    void testRequesterSirenTooLong() {
        TrackingDTO dto = new TrackingDTO();
        dto.setRequestId(UUID.randomUUID());
        dto.setRequestDate(LocalDateTime.now());
        dto.setRequesterSiren("123456789012345"); // 15 chars
        dto.setRequestContent(createBlob("Test"));
        dto.setRequestStatus(Flux13RequestStatus.Pending);
        dto.setComment("Valid comment");

        Set<ConstraintViolation<TrackingDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "Requester siren should have max 14 characters");
    }

    @Test
    void testNullRequestContent() {
        TrackingDTO dto = new TrackingDTO();
        dto.setRequestId(UUID.randomUUID());
        dto.setRequestDate(LocalDateTime.now());
        dto.setRequesterSiren("123456789");
        dto.setRequestContent(null); // Invalid
        dto.setRequestStatus(Flux13RequestStatus.Pending);
        dto.setComment("Valid comment");

        Set<ConstraintViolation<TrackingDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "Request content should not be null");
    }

    @Test
    void testNullRequestStatus() {
        TrackingDTO dto = new TrackingDTO();
        dto.setRequestId(UUID.randomUUID());
        dto.setRequestDate(LocalDateTime.now());
        dto.setRequesterSiren("123456789");
        dto.setRequestContent(createBlob("Test"));
        dto.setRequestStatus(null); // Invalid
        dto.setComment("Valid comment");

        Set<ConstraintViolation<TrackingDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "Request status should not be null");
    }

    @Test
    void testCommentMaxLength() {
        TrackingDTO dto = new TrackingDTO();
        dto.setRequestId(UUID.randomUUID());
        dto.setRequestDate(LocalDateTime.now());
        dto.setRequesterSiren("123456789");
        dto.setRequestContent(createBlob("Test"));
        dto.setRequestStatus(Flux13RequestStatus.Pending);
        dto.setComment("A".repeat(256)); // Invalid

        Set<ConstraintViolation<TrackingDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "Comment should have max 255 characters");
    }

    // Helper method to create a Blob
    private Blob createBlob(String content) {
        try {
            return new javax.sql.rowset.serial.SerialBlob(content.getBytes());
        } catch (SQLException e) {
            throw new RuntimeException("Error creating Blob", e);
        }
    }
}
