package com.generix.legalreferential.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class SirenDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(SirenDTO.class);
        SirenDTO sirenDTO1 = new SirenDTO();
        sirenDTO1.setInstanceID(1L);
        SirenDTO sirenDTO2 = new SirenDTO();
        assertThat(sirenDTO1).isNotEqualTo(sirenDTO2);
        sirenDTO2.setInstanceID(sirenDTO1.getInstanceID());
        assertThat(sirenDTO1).isEqualTo(sirenDTO2);
        sirenDTO2.setInstanceID(2L);
        assertThat(sirenDTO1).isNotEqualTo(sirenDTO2);
        sirenDTO1.setInstanceID(null);
        assertThat(sirenDTO1).isNotEqualTo(sirenDTO2);
    }
}
