package com.generix.legalreferential.service.dto.custom.flux13;

import static org.junit.jupiter.api.Assertions.*;

import com.generix.legalreferential.domain.enumeration.NatureEtablissement;
import com.generix.legalreferential.domain.enumeration.Operation;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.service.dto.custom.annuaire.AnnuaireAdresseDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.InformationAdressageDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.PeriodeEffetDTO;
import com.generix.legalreferential.service.mapper.flux13.AnnuaireActualisationDtoXmlMapper;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class AnnuaireActualisationXmlDTOTest {

    private static JAXBContext jaxbContext;

    @BeforeAll
    static void setUp() throws JAXBException {
        jaxbContext = JAXBContext.newInstance(AnnuaireActualisationXmlDTO.class);
    }

    /**
     * Sets up the validator for validation before each test.
     * This method is executed before each test method to initialize the
     * validator used for constraint validation.
     */
    @NotNull
    private static LigneAnnuaireRequestDTO getLigneAnnuaireRequestDTO() {
        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = new LigneAnnuaireRequestDTO();
        ligneAnnuaireRequestDTO.setOperation(Operation.Creation);
        PeriodeEffetDTO periodeEffetDTO = new PeriodeEffetDTO();
        periodeEffetDTO.setDateDebutEffet("2023-01-01");
        periodeEffetDTO.setDateFinEffet("2023-01-31");
        ligneAnnuaireRequestDTO.setPeriodeEffet(periodeEffetDTO);
        InformationAdressageDTO informationAdressageDTO = new InformationAdressageDTO();
        informationAdressageDTO.setSiren("702042755");
        informationAdressageDTO.setSiret("70204275500240");
        informationAdressageDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
        informationAdressageDTO.setSuffixeAdressage("00000");
        informationAdressageDTO.setMatriculePlateforme("0145");
        ligneAnnuaireRequestDTO.setInformationAdressage(informationAdressageDTO);
        return ligneAnnuaireRequestDTO;
    }

    /**
     * Creates and returns a {@link CodeRoutageRequestDTO} with predefined valid data.
     *
     * @return a valid instance of {@link CodeRoutageRequestDTO}
     */
    @NotNull
    private static CodeRoutageRequestDTO getCodeRoutageRequestDTO() {
        CodeRoutageRequestDTO codeRoutageRequestDTO = new CodeRoutageRequestDTO();
        codeRoutageRequestDTO.setNatureEtablissement(NatureEtablissement.Privée);
        codeRoutageRequestDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
        codeRoutageRequestDTO.setSiret("70204275500240");
        codeRoutageRequestDTO.setTypeIdentifiantRoutage("0224");
        codeRoutageRequestDTO.setLibelleCodeRoutage("Libellé Code routage");
        codeRoutageRequestDTO.setGestionEngagementJuridique(true);
        codeRoutageRequestDTO.setEtatAdministratif(Statut.A);
        AnnuaireAdresseDTO annuaireAdresseDTO = new AnnuaireAdresseDTO();
        annuaireAdresseDTO.setLigneAdresse1("16 BIS RUE HENRI BARBUSSE");
        annuaireAdresseDTO.setLigneAdresse2("CEDEX 1");
        annuaireAdresseDTO.setLigneAdresse3("Bâtiment le Callipso");
        annuaireAdresseDTO.setCodePostal("38100");
        annuaireAdresseDTO.setSubDivisionPays("Bretagne");
        annuaireAdresseDTO.setLocalite("Grenoble");
        annuaireAdresseDTO.setCodePays("FR");
        annuaireAdresseDTO.setLibellePays("France");
        codeRoutageRequestDTO.setAdresse(annuaireAdresseDTO);
        return codeRoutageRequestDTO;
    }

    @Test
    void testMarshall() throws JAXBException {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();

        List<CodeRoutageRequestDTO> validCodesRoutage = new ArrayList<>();
        CodeRoutageRequestDTO codeRoutageRequestDTO = getCodeRoutageRequestDTO();
        validCodesRoutage.add(codeRoutageRequestDTO);

        List<LigneAnnuaireRequestDTO> validLignesAdressage = new ArrayList<>();
        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = getLigneAnnuaireRequestDTO();
        validLignesAdressage.add(ligneAnnuaireRequestDTO);

        dto.setCodesRoutage(validCodesRoutage);
        dto.setLignesAdressage(validLignesAdressage);

        AnnuaireActualisationXmlDTO annuaireActualisationXmlDTO = AnnuaireActualisationDtoXmlMapper.INSTANCE.mapToAnnuaireActualisationXmlDTO(
            dto
        );

        annuaireActualisationXmlDTO.setXmlnsAc("custom-annuaire");
        annuaireActualisationXmlDTO.setXmlnsXsi("http://www.w3.org/2001/XMLSchema-instance");

        StringWriter writer = new StringWriter();
        Marshaller marshaller = jaxbContext.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
        marshaller.marshal(annuaireActualisationXmlDTO, writer);

        String xmlOutput = writer.toString();
        assertNotNull(xmlOutput);
        assertTrue(xmlOutput.contains("<AnnuaireActualisation"));
        assertTrue(xmlOutput.contains("xmlns:ac=\"custom-annuaire\""));
        assertTrue(xmlOutput.contains("xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\""));
    }

    @Test
    void testUnmarshall() throws JAXBException {
        String xmlInput =
            "  <AnnuaireActualisation xmlns:ac=\"annuaire-common\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
            "                <BlocCodesRoutage></BlocCodesRoutage>\n" +
            "                <BlocLignesAnnuaire></BlocLignesAnnuaire>\n" +
            "            </AnnuaireActualisation>";

        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        AnnuaireActualisationXmlDTO dto = (AnnuaireActualisationXmlDTO) unmarshaller.unmarshal(new StringReader(xmlInput));

        assertNotNull(dto);
        assertEquals("annuaire-common", dto.getXmlnsAc());
        assertEquals("http://www.w3.org/2001/XMLSchema-instance", dto.getXmlnsXsi());
        assertNotNull(dto.getBlocCodesRoutage());
        assertNotNull(dto.getBlocLignesAnnuaire());
    }
}
