package com.generix.legalreferential.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class SiretDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(SiretDTO.class);
        SiretDTO siretDTO1 = new SiretDTO();
        siretDTO1.setInstanceID(1L);
        SiretDTO siretDTO2 = new SiretDTO();
        assertThat(siretDTO1).isNotEqualTo(siretDTO2);
        siretDTO2.setInstanceID(siretDTO1.getInstanceID());
        assertThat(siretDTO1).isEqualTo(siretDTO2);
        siretDTO2.setInstanceID(2L);
        assertThat(siretDTO1).isNotEqualTo(siretDTO2);
        siretDTO1.setInstanceID(null);
        assertThat(siretDTO1).isNotEqualTo(siretDTO2);
    }
}
