package com.generix.legalreferential.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class RoutingCodeDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(RoutingCodeDTO.class);
        RoutingCodeDTO routingCodeDTO1 = new RoutingCodeDTO();
        routingCodeDTO1.setInstanceID(1L);
        RoutingCodeDTO routingCodeDTO2 = new RoutingCodeDTO();
        assertThat(routingCodeDTO1).isNotEqualTo(routingCodeDTO2);
        routingCodeDTO2.setInstanceID(routingCodeDTO1.getInstanceID());
        assertThat(routingCodeDTO1).isEqualTo(routingCodeDTO2);
        routingCodeDTO2.setInstanceID(2L);
        assertThat(routingCodeDTO1).isNotEqualTo(routingCodeDTO2);
        routingCodeDTO1.setInstanceID(null);
        assertThat(routingCodeDTO1).isNotEqualTo(routingCodeDTO2);
    }
}
