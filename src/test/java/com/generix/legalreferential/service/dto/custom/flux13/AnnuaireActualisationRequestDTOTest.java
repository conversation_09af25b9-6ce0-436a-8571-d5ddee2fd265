package com.generix.legalreferential.service.dto.custom.flux13;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.generix.legalreferential.domain.enumeration.NatureEtablissement;
import com.generix.legalreferential.domain.enumeration.Operation;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.service.dto.custom.annuaire.AnnuaireAdresseDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.InformationAdressageDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.PeriodeEffetDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AnnuaireActualisationRequestDTOTest {

    private Validator validator;

    /**
     * Sets up the validator for validation before each test.
     * This method is executed before each test method to initialize the
     * validator used for constraint validation.
     */
    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    /**
     * Sets up the validator for validation before each test.
     * This method is executed before each test method to initialize the
     * validator used for constraint validation.
     */
    @NotNull
    private static LigneAnnuaireRequestDTO getLigneAnnuaireRequestDTO() {
        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = new LigneAnnuaireRequestDTO();
        ligneAnnuaireRequestDTO.setOperation(Operation.Creation);
        PeriodeEffetDTO periodeEffetDTO = new PeriodeEffetDTO();
        periodeEffetDTO.setDateDebutEffet("2023-01-01");
        periodeEffetDTO.setDateFinEffet("2023-01-31");
        ligneAnnuaireRequestDTO.setPeriodeEffet(periodeEffetDTO);
        InformationAdressageDTO informationAdressageDTO = new InformationAdressageDTO();
        informationAdressageDTO.setSiren("702042755");
        informationAdressageDTO.setSiret("70204275500240");
        informationAdressageDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
        informationAdressageDTO.setSuffixeAdressage("00000");
        informationAdressageDTO.setMatriculePlateforme("0145");
        ligneAnnuaireRequestDTO.setInformationAdressage(informationAdressageDTO);
        return ligneAnnuaireRequestDTO;
    }

    /**
     * Creates and returns a {@link CodeRoutageRequestDTO} with predefined valid data.
     *
     * @return a valid instance of {@link CodeRoutageRequestDTO}
     */
    @NotNull
    private static CodeRoutageRequestDTO getCodeRoutageRequestDTO() {
        CodeRoutageRequestDTO codeRoutageRequestDTO = new CodeRoutageRequestDTO();
        codeRoutageRequestDTO.setNatureEtablissement(NatureEtablissement.Privée);
        codeRoutageRequestDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
        codeRoutageRequestDTO.setSiret("70204275500240");
        codeRoutageRequestDTO.setTypeIdentifiantRoutage("0224");
        codeRoutageRequestDTO.setLibelleCodeRoutage("Libellé Code routage");
        codeRoutageRequestDTO.setGestionEngagementJuridique(true);
        codeRoutageRequestDTO.setEtatAdministratif(Statut.A);
        AnnuaireAdresseDTO annuaireAdresseDTO = new AnnuaireAdresseDTO();
        annuaireAdresseDTO.setLigneAdresse1("16 BIS RUE HENRI BARBUSSE");
        annuaireAdresseDTO.setLigneAdresse2("CEDEX 1");
        annuaireAdresseDTO.setLigneAdresse3("Bâtiment le Callipso");
        annuaireAdresseDTO.setCodePostal("38100");
        annuaireAdresseDTO.setSubDivisionPays("Bretagne");
        annuaireAdresseDTO.setLocalite("Grenoble");
        annuaireAdresseDTO.setCodePays("FR");
        annuaireAdresseDTO.setLibellePays("France");
        codeRoutageRequestDTO.setAdresse(annuaireAdresseDTO);
        return codeRoutageRequestDTO;
    }

    /**
     * Tests the validation of a valid {@link AnnuaireActualisationRequestDTO}.
     * Ensures the DTO is valid when it contains correctly populated lists of
     * {@link CodeRoutageRequestDTO} and {@link LigneAnnuaireRequestDTO}.
     */
    @Test
    void testValidDTO() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();

        List<CodeRoutageRequestDTO> validCodesRoutage = new ArrayList<>();
        CodeRoutageRequestDTO codeRoutageRequestDTO = getCodeRoutageRequestDTO();
        validCodesRoutage.add(codeRoutageRequestDTO);

        List<LigneAnnuaireRequestDTO> validLignesAdressage = new ArrayList<>();
        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = getLigneAnnuaireRequestDTO();
        validLignesAdressage.add(ligneAnnuaireRequestDTO);

        dto.setCodesRoutage(validCodesRoutage);
        dto.setLignesAdressage(validLignesAdressage);

        Set<ConstraintViolation<AnnuaireActualisationRequestDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "DTO should be valid with correct data.");
    }

    /**
     * Tests the validation when invalid objects are present in the lists of
     * {@link CodeRoutageRequestDTO} and {@link LigneAnnuaireRequestDTO}.
     * Ensures that the DTO is invalid when the lists contain objects that do not
     * conform to the expected structure.
     */
    @Test
    void testInvalidObjectsInLists() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();

        List<CodeRoutageRequestDTO> invalidCodesRoutage = new ArrayList<>();
        CodeRoutageRequestDTO invalidCode = new CodeRoutageRequestDTO();
        invalidCodesRoutage.add(invalidCode);

        List<LigneAnnuaireRequestDTO> invalidLignesAdressage = new ArrayList<>();
        LigneAnnuaireRequestDTO invalidLigne = new LigneAnnuaireRequestDTO();
        invalidLignesAdressage.add(invalidLigne);

        dto.setCodesRoutage(invalidCodesRoutage);
        dto.setLignesAdressage(invalidLignesAdressage);

        Set<ConstraintViolation<AnnuaireActualisationRequestDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "DTO should be invalid when it contains invalid objects.");
    }

    /**
     * Tests the validation of a valid {@link AnnuaireActualisationRequestDTO}
     * when the {@link LigneAnnuaireRequestDTO} list is null.
     * Ensures the DTO is valid even if the {@link LigneAnnuaireRequestDTO} list is null.
     */
    @Test
    void testValidDTOWithNullLignesAdressage() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();

        List<CodeRoutageRequestDTO> validCodesRoutage = new ArrayList<>();
        CodeRoutageRequestDTO codeRoutageRequestDTO = getCodeRoutageRequestDTO();
        validCodesRoutage.add(codeRoutageRequestDTO);

        dto.setCodesRoutage(validCodesRoutage);

        Set<ConstraintViolation<AnnuaireActualisationRequestDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "DTO should be valid with with null LignesAdressage.");
    }

    /**
     * Tests the validation of a valid {@link AnnuaireActualisationRequestDTO}
     * when the {@link CodeRoutageRequestDTO} list is null.
     * Ensures the DTO is valid even if the {@link CodeRoutageRequestDTO} list is null.
     */
    @Test
    void testValidDTOWithNullCodesRoutage() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();

        List<LigneAnnuaireRequestDTO> validLignesAdressage = new ArrayList<>();
        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = getLigneAnnuaireRequestDTO();
        validLignesAdressage.add(ligneAnnuaireRequestDTO);

        dto.setLignesAdressage(validLignesAdressage);

        Set<ConstraintViolation<AnnuaireActualisationRequestDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "DTO should be valid with null CodesRoutage.");
    }

    /**
     * Tests the validation of an empty {@link AnnuaireActualisationRequestDTO}.
     * Ensures that the DTO is invalid when both lists are null.
     */
    @Test
    void testWithEmptyDTO() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();

        Set<ConstraintViolation<AnnuaireActualisationRequestDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "DTO should not be valid when lists are null.");
    }

    /**
     * Tests the validation of an {@link AnnuaireActualisationRequestDTO} with empty lists.
     * Ensures that the DTO is invalid when the lists are empty.
     */
    @Test
    void testEmptyLists() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();
        dto.setCodesRoutage(new ArrayList<>());
        dto.setLignesAdressage(new ArrayList<>());

        Set<ConstraintViolation<AnnuaireActualisationRequestDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "DTO should not be valid when lists are empty.");
    }
}
