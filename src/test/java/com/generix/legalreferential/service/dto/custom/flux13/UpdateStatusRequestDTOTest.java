package com.generix.legalreferential.service.dto.custom.flux13;

import static org.junit.jupiter.api.Assertions.*;

import com.generix.legalreferential.domain.enumeration.Flux13RequestStatus;
import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class UpdateStatusRequestDTOTest {

    private static Validator validator;

    @BeforeAll
    static void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testValidDTO() {
        UpdateStatusRequestDTO dto = new UpdateStatusRequestDTO();
        dto.setStatut(Flux13RequestStatus.Accepted); // Assuming RequestStatus is an Enum
        dto.setRaison("Valid reason");

        Set<ConstraintViolation<UpdateStatusRequestDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "Valid DTO should not produce validation errors");
    }

    @Test
    void testInvalidWhenStatutIsNull() {
        UpdateStatusRequestDTO dto = new UpdateStatusRequestDTO();
        dto.setStatut(null); // Violation of @NotNull
        dto.setRaison("Some reason");

        Set<ConstraintViolation<UpdateStatusRequestDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "DTO with null statut should be invalid");
        assertEquals(1, violations.size());
        assertTrue(violations.iterator().next().getPropertyPath().toString().equals("statut"));
        assertTrue(violations.iterator().next().getMessageTemplate().equals("{javax.validation.constraints.NotNull.message}"));
    }

    @Test
    void testValidWhenRaisonIsNull() {
        UpdateStatusRequestDTO dto = new UpdateStatusRequestDTO();
        dto.setStatut(Flux13RequestStatus.Rejected);
        dto.setRaison(null); // @Size(max = 255) does not require non-null

        Set<ConstraintViolation<UpdateStatusRequestDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "DTO with null raison should be valid");
    }

    @Test
    void testValidWhenRaisonIsEmpty() {
        UpdateStatusRequestDTO dto = new UpdateStatusRequestDTO();
        dto.setStatut(Flux13RequestStatus.Rejected);
        dto.setRaison(""); // Empty but within 255 characters

        Set<ConstraintViolation<UpdateStatusRequestDTO>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), "DTO with empty raison should be valid");
    }

    @Test
    void testInvalidWhenRaisonExceedsMaxSize() {
        UpdateStatusRequestDTO dto = new UpdateStatusRequestDTO();
        dto.setStatut(Flux13RequestStatus.Pending);
        dto.setRaison("a".repeat(256)); // Exceeds 255 character limit

        Set<ConstraintViolation<UpdateStatusRequestDTO>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty(), "DTO with raison longer than 255 should be invalid");
        assertEquals(1, violations.size());
        assertTrue(violations.iterator().next().getPropertyPath().toString().equals("raison"));
        assertTrue(violations.iterator().next().getMessageTemplate().contains("{javax.validation.constraints.Size.message}"));
    }
}
