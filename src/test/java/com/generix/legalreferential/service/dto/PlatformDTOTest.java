package com.generix.legalreferential.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PlatformDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(PlatformDTO.class);
        PlatformDTO platformDTO1 = new PlatformDTO();
        platformDTO1.setInstanceID(1L);
        PlatformDTO platformDTO2 = new PlatformDTO();
        assertThat(platformDTO1).isNotEqualTo(platformDTO2);
        platformDTO2.setInstanceID(platformDTO1.getInstanceID());
        assertThat(platformDTO1).isEqualTo(platformDTO2);
        platformDTO2.setInstanceID(2L);
        assertThat(platformDTO1).isNotEqualTo(platformDTO2);
        platformDTO1.setInstanceID(null);
        assertThat(platformDTO1).isNotEqualTo(platformDTO2);
    }
}
