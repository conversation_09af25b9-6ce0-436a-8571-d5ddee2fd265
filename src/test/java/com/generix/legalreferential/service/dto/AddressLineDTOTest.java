package com.generix.legalreferential.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AddressLineDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(AddressLineDTO.class);
        AddressLineDTO addressLineDTO1 = new AddressLineDTO();
        addressLineDTO1.setInstanceID(1L);
        AddressLineDTO addressLineDTO2 = new AddressLineDTO();
        assertThat(addressLineDTO1).isNotEqualTo(addressLineDTO2);
        addressLineDTO2.setInstanceID(addressLineDTO1.getInstanceID());
        assertThat(addressLineDTO1).isEqualTo(addressLineDTO2);
        addressLineDTO2.setInstanceID(2L);
        assertThat(addressLineDTO1).isNotEqualTo(addressLineDTO2);
        addressLineDTO1.setInstanceID(null);
        assertThat(addressLineDTO1).isNotEqualTo(addressLineDTO2);
    }
}
