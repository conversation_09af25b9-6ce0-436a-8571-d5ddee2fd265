package com.generix.legalreferential.service.dto.custom.flux13.validation;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

import com.generix.legalreferential.domain.enumeration.NatureEtablissement;
import com.generix.legalreferential.domain.enumeration.Operation;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.service.dto.custom.annuaire.AnnuaireAdresseDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.InformationAdressageDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.PeriodeEffetDTO;
import com.generix.legalreferential.service.dto.custom.flux13.AnnuaireActualisationRequestDTO;
import com.generix.legalreferential.service.dto.custom.flux13.CodeRoutageRequestDTO;
import com.generix.legalreferential.service.dto.custom.flux13.LigneAnnuaireRequestDTO;
import java.util.Collections;
import java.util.List;
import javax.validation.ConstraintValidatorContext;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AnnuaireActualisationRequestDTOValidatorTest {

    private AnnuaireActualisationRequestDTOValidator validator;
    private ConstraintValidatorContext context;

    @NotNull
    private static LigneAnnuaireRequestDTO getLigneAnnuaireRequestDTO() {
        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = new LigneAnnuaireRequestDTO();
        ligneAnnuaireRequestDTO.setOperation(Operation.Creation);
        PeriodeEffetDTO periodeEffetDTO = new PeriodeEffetDTO();
        periodeEffetDTO.setDateDebutEffet("2023-01-01");
        periodeEffetDTO.setDateFinEffet("2023-01-31");
        ligneAnnuaireRequestDTO.setPeriodeEffet(periodeEffetDTO);
        InformationAdressageDTO informationAdressageDTO = new InformationAdressageDTO();
        informationAdressageDTO.setSiren("702042755");
        informationAdressageDTO.setSiret("70204275500240");
        informationAdressageDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
        informationAdressageDTO.setSuffixeAdressage("00000");
        informationAdressageDTO.setMatriculePlateforme("0145");
        ligneAnnuaireRequestDTO.setInformationAdressage(informationAdressageDTO);
        return ligneAnnuaireRequestDTO;
    }

    /**
     * Creates and returns a {@link CodeRoutageRequestDTO} with predefined valid data.
     *
     * @return a valid instance of {@link CodeRoutageRequestDTO}
     */
    @NotNull
    private static CodeRoutageRequestDTO getCodeRoutageRequestDTO() {
        CodeRoutageRequestDTO codeRoutageRequestDTO = new CodeRoutageRequestDTO();
        codeRoutageRequestDTO.setNatureEtablissement(NatureEtablissement.Privée);
        codeRoutageRequestDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
        codeRoutageRequestDTO.setSiret("70204275500240");
        codeRoutageRequestDTO.setTypeIdentifiantRoutage("0224");
        codeRoutageRequestDTO.setLibelleCodeRoutage("Libellé Code routage");
        codeRoutageRequestDTO.setGestionEngagementJuridique(true);
        codeRoutageRequestDTO.setEtatAdministratif(Statut.A);
        AnnuaireAdresseDTO annuaireAdresseDTO = new AnnuaireAdresseDTO();
        annuaireAdresseDTO.setLigneAdresse1("16 BIS RUE HENRI BARBUSSE");
        annuaireAdresseDTO.setLigneAdresse2("CEDEX 1");
        annuaireAdresseDTO.setLigneAdresse3("Bâtiment le Callipso");
        annuaireAdresseDTO.setCodePostal("38100");
        annuaireAdresseDTO.setSubDivisionPays("Bretagne");
        annuaireAdresseDTO.setLocalite("Grenoble");
        annuaireAdresseDTO.setCodePays("FR");
        annuaireAdresseDTO.setLibellePays("France");
        codeRoutageRequestDTO.setAdresse(annuaireAdresseDTO);
        return codeRoutageRequestDTO;
    }

    @BeforeEach
    void setUp() {
        validator = new AnnuaireActualisationRequestDTOValidator();
        context = mock(ConstraintValidatorContext.class);
    }

    @Test
    void testValidWithCodesRoutage() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();
        CodeRoutageRequestDTO codeRoutageRequestDTO = getCodeRoutageRequestDTO();
        dto.setCodesRoutage(List.of(codeRoutageRequestDTO));
        dto.setLignesAdressage(Collections.emptyList());

        assertTrue(validator.isValid(dto, context));
    }

    @Test
    void testValidWithLignesAdressage() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();
        dto.setCodesRoutage(Collections.emptyList());
        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = getLigneAnnuaireRequestDTO();
        dto.setLignesAdressage(List.of(ligneAnnuaireRequestDTO));

        assertTrue(validator.isValid(dto, context));
    }

    @Test
    void testInvalidWithEmptyFields() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();
        dto.setCodesRoutage(Collections.emptyList());
        dto.setLignesAdressage(Collections.emptyList());

        assertFalse(validator.isValid(dto, context));
    }

    @Test
    void testInvalidWithNullFields() {
        AnnuaireActualisationRequestDTO dto = new AnnuaireActualisationRequestDTO();
        dto.setCodesRoutage(null);
        dto.setLignesAdressage(null);

        assertFalse(validator.isValid(dto, context));
    }

    @Test
    void testInvalidWithNullObject() {
        assertFalse(validator.isValid(null, context));
    }
}
