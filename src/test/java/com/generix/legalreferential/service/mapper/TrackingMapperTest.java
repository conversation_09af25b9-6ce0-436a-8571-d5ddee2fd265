package com.generix.legalreferential.service.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.generix.legalreferential.domain.Tracking;
import com.generix.legalreferential.domain.enumeration.Flux13RequestStatus;
import com.generix.legalreferential.service.dto.TrackingDTO;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class TrackingMapperTest {

    private TrackingMapper trackingMapper;

    @BeforeEach
    void setUp() {
        trackingMapper = Mappers.getMapper(TrackingMapper.class);
    }

    @Test
    void testToEntity() throws SQLException {
        TrackingDTO dto = new TrackingDTO();
        dto.setRequestId(UUID.randomUUID());
        dto.setRequestDate(LocalDateTime.now());
        dto.setRequesterSiren("12345678901234");
        dto.setRequestContent(new javax.sql.rowset.serial.SerialBlob(new byte[] { 1, 2, 3 }));
        dto.setRequestStatus(Flux13RequestStatus.Pending);
        dto.setComment("Test comment");

        Tracking entity = trackingMapper.toEntity(dto);
        assertNotNull(entity);
        assertEquals(dto.getRequestId(), entity.getRequestId());
        assertEquals(dto.getRequestDate(), entity.getRequestDate());
        assertEquals(dto.getRequesterSiren(), entity.getRequesterSiren());
        assertEquals(dto.getRequestStatus(), entity.getRequestStatus());
        assertEquals(dto.getComment(), entity.getComment());
    }

    @Test
    void testToDto() throws SQLException {
        Tracking entity = new Tracking();
        entity.setRequestId(UUID.randomUUID());
        entity.setRequestDate(LocalDateTime.now());
        entity.setRequesterSiren("12345678901234");
        entity.setRequestContent(new javax.sql.rowset.serial.SerialBlob(new byte[] { 4, 5, 6 }));
        entity.setRequestStatus(Flux13RequestStatus.Accepted);
        entity.setComment("Entity comment");

        TrackingDTO dto = trackingMapper.toDto(entity);
        assertNotNull(dto);
        assertEquals(entity.getRequestId(), dto.getRequestId());
        assertEquals(entity.getRequestDate(), dto.getRequestDate());
        assertEquals(entity.getRequesterSiren(), dto.getRequesterSiren());
        assertEquals(entity.getRequestStatus(), dto.getRequestStatus());
        assertEquals(entity.getComment(), dto.getComment());
    }

    @Test
    void testToEntityList() {
        TrackingDTO dto1 = new TrackingDTO();
        dto1.setRequestId(UUID.randomUUID());
        dto1.setRequestDate(LocalDateTime.now());
        dto1.setRequesterSiren("12345678901234");
        dto1.setRequestStatus(Flux13RequestStatus.Pending);

        TrackingDTO dto2 = new TrackingDTO();
        dto2.setRequestId(UUID.randomUUID());
        dto2.setRequestDate(LocalDateTime.now());
        dto2.setRequesterSiren("56789012345678");
        dto2.setRequestStatus(Flux13RequestStatus.Rejected);

        List<Tracking> entityList = trackingMapper.toEntity(List.of(dto1, dto2));
        assertNotNull(entityList);
        assertEquals(2, entityList.size());
        assertEquals(dto1.getRequesterSiren(), entityList.get(0).getRequesterSiren());
        assertEquals(dto2.getRequesterSiren(), entityList.get(1).getRequesterSiren());
    }

    @Test
    void testToDtoList() {
        Tracking entity1 = new Tracking();
        entity1.setRequestId(UUID.randomUUID());
        entity1.setRequestDate(LocalDateTime.now());
        entity1.setRequesterSiren("12345678901234");
        entity1.setRequestStatus(Flux13RequestStatus.Pending);

        Tracking entity2 = new Tracking();
        entity2.setRequestId(UUID.randomUUID());
        entity2.setRequestDate(LocalDateTime.now());
        entity2.setRequesterSiren("56789012345678");
        entity2.setRequestStatus(Flux13RequestStatus.Accepted);

        List<TrackingDTO> dtoList = trackingMapper.toDto(List.of(entity1, entity2));
        assertNotNull(dtoList);
        assertEquals(2, dtoList.size());
        assertEquals(entity1.getRequesterSiren(), dtoList.get(0).getRequesterSiren());
        assertEquals(entity2.getRequesterSiren(), dtoList.get(1).getRequesterSiren());
    }

    @Test
    void testPartialUpdate() {
        Tracking entity = new Tracking();
        entity.setRequestId(UUID.randomUUID());
        entity.setRequestDate(LocalDateTime.now());
        entity.setRequesterSiren("11111111111111");
        entity.setRequestStatus(Flux13RequestStatus.Pending);
        entity.setComment("Original Comment");

        TrackingDTO dto = new TrackingDTO();
        dto.setRequesterSiren("22222222222222"); // Updating only this field

        trackingMapper.partialUpdate(entity, dto);

        assertEquals("22222222222222", entity.getRequesterSiren());
        assertEquals(Flux13RequestStatus.Pending, entity.getRequestStatus()); // Should remain unchanged
        assertEquals("Original Comment", entity.getComment()); // Should remain unchanged
    }
}
