package com.generix.legalreferential.service.mapper.flux13;

import static org.junit.jupiter.api.Assertions.*;

import com.generix.legalreferential.domain.enumeration.Nature;
import com.generix.legalreferential.domain.enumeration.Operation;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.service.dto.custom.annuaire.AnnuaireAdresseDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.InformationAdressageDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.PeriodeEffetDTO;
import com.generix.legalreferential.service.dto.custom.flux13.*;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class AnnuaireActualisationDtoXmlMapperTest {

    private AnnuaireActualisationDtoXmlMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(AnnuaireActualisationDtoXmlMapper.class);
    }

    @Test
    void testConvertToStatut() {
        assertEquals("I", mapper.convertToStatut(Statut.F));
        assertEquals("A", mapper.convertToStatut(Statut.A));
        assertNull(mapper.convertToStatut(null));
    }

    @Test
    void testConvertToNature() {
        assertEquals(Nature.D, mapper.convertToNature(Operation.Creation));
        assertEquals(Nature.D, mapper.convertToNature(Operation.Modification));
        assertEquals(Nature.M, mapper.convertToNature(Operation.Annulation));
        assertNull(mapper.convertToNature(null));
    }

    @Test
    void testMapToCodeRoutage() {
        CodeRoutageRequestDTO requestDTO = new CodeRoutageRequestDTO();
        requestDTO.setEtatAdministratif(Statut.F);
        requestDTO.setSiret("12345678901234");
        requestDTO.setIdentifiantRoutage("R001");
        requestDTO.setLibelleCodeRoutage("Test Routage");

        AnnuaireAdresseDTO adresse = new AnnuaireAdresseDTO();
        adresse.setLigneAdresse1("123 Main St");
        adresse.setLigneAdresse2("Apt 4B");
        adresse.setLigneAdresse3("Building 5");
        adresse.setLocalite("Paris");
        adresse.setCodePostal("75000");
        adresse.setSubDivisionPays("FR-IDF");
        adresse.setCodePays("FR");

        requestDTO.setAdresse(adresse);

        CodeRoutageXmlDTO result = mapper.mapToCodeRoutageXmlDTO(requestDTO);
        assertNotNull(result);
        assertEquals("12345678901234", result.getIdSIRET().getValue());
        assertEquals("R001", result.getIdRoutage().getValue());
        assertEquals("Test Routage", result.getNom());
        assertEquals("123 Main St", result.getLigneAdresse1());
        assertEquals("Apt 4B", result.getLigneAdresse2());
        assertEquals("Building 5", result.getLigneAdresse3());
        assertEquals("Paris", result.getLocalite());
        assertEquals("75000", result.getCp());
        assertEquals("FR-IDF", result.getSubdivisionPays());
        assertEquals("FR", result.getCodePays());
    }

    @Test
    void testMapToDateEffet() {
        PeriodeEffetDTO periodeEffet = new PeriodeEffetDTO();
        periodeEffet.setDateDebutEffet("2024-01-01");
        periodeEffet.setDateFinEffet("2025-01-01");

        DateEffetXmlDTO result = mapper.mapToDateEffetXmlDTO(periodeEffet);
        assertNotNull(result);
        assertEquals("20240101", result.getDateDebut());
        assertEquals("20250101", result.getDateFin());
    }

    @Test
    void testMapToIdentifiant() {
        InformationAdressageDTO info = new InformationAdressageDTO();
        info.setSiren("123456789");
        info.setSiret("12345678901234");
        info.setIdentifiantRoutage("R001");
        info.setSuffixeAdressage("Suffix Test");

        IdentifiantXmlDTO result = mapper.mapToIdentifiantXmlDTO(info);
        assertNotNull(result);
        assertEquals("123456789", result.getIdLinSIREN().getValue());
        assertEquals("12345678901234", result.getIdLinSIRET().getValue());
        assertEquals("R001", result.getIdLinRoutage().getValue());
        assertEquals("Suffix Test", result.getSuffixe());
    }

    @Test
    void testMapToLigneAnnuaire() {
        LigneAnnuaireRequestDTO request = new LigneAnnuaireRequestDTO();
        request.setOperation(Operation.Creation);

        PeriodeEffetDTO periode = new PeriodeEffetDTO();
        periode.setDateDebutEffet("2024-01-01");
        periode.setDateFinEffet("2025-01-01");
        request.setPeriodeEffet(periode);

        InformationAdressageDTO info = new InformationAdressageDTO();
        info.setMatriculePlateforme("0045");
        request.setInformationAdressage(info);

        LigneAnnuaireXmlDTO result = mapper.mapToLigneAnnuaireXmlDTO(request);
        assertNotNull(result);
        assertEquals(Nature.D, result.getNature());
        assertEquals("20240101", result.getDateEffet().getDateDebut());
        assertEquals("20250101", result.getDateEffet().getDateFin());
        assertEquals("0045", result.getIdPlateforme());
    }

    @Test
    void testMapToAnnuaireActualisation() {
        AnnuaireActualisationRequestDTO requestDTO = new AnnuaireActualisationRequestDTO();

        CodeRoutageRequestDTO code1 = new CodeRoutageRequestDTO();
        code1.setSiret("SIRET1");

        CodeRoutageRequestDTO code2 = new CodeRoutageRequestDTO();
        code2.setSiret("SIRET2");

        requestDTO.setCodesRoutage(List.of(code1, code2));

        LigneAnnuaireRequestDTO ligne1 = new LigneAnnuaireRequestDTO();
        ligne1.setOperation(Operation.Creation);

        LigneAnnuaireRequestDTO ligne2 = new LigneAnnuaireRequestDTO();
        ligne1.setOperation(Operation.Annulation);

        requestDTO.setLignesAdressage(List.of(ligne1, ligne2));

        AnnuaireActualisationXmlDTO result = mapper.mapToAnnuaireActualisationXmlDTO(requestDTO);
        assertNotNull(result);
        assertEquals(2, result.getBlocCodesRoutage().getCodesRoutage().size());
        assertEquals(2, result.getBlocLignesAnnuaire().getLignesAdressage().size());
    }
}
