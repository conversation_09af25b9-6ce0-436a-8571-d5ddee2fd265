package com.generix.legalreferential.security;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.security.oauth2.core.oidc.endpoint.OidcParameterNames.ID_TOKEN;

import com.generix.legalreferential.domain.enumeration.AuthoritiesConstants;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.oidc.OidcIdToken;
import org.springframework.security.oauth2.core.oidc.user.DefaultOidcUser;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;

/**
 * Test class for the {@link SecurityUtils} utility class.
 */
class SecurityUtilsUnitTest {

    @BeforeEach
    @AfterEach
    void cleanup() {
        SecurityContextHolder.clearContext();
    }

    @Test
    void testGetCurrentUserLogin() {
        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
        securityContext.setAuthentication(new UsernamePasswordAuthenticationToken("admin", "admin"));
        SecurityContextHolder.setContext(securityContext);
        Optional<String> login = SecurityUtils.getCurrentUserLogin();
        assertThat(login).contains("admin");
    }

    @Test
    void testGetCurrentUserLoginForOAuth2() {
        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
        Map<String, Object> claims = new HashMap<>();
        claims.put("groups", AuthoritiesConstants.LEGALREF_ADMIN.name());
        claims.put("sub", 123);
        claims.put("preferred_username", "admin");
        OidcIdToken idToken = new OidcIdToken(ID_TOKEN, Instant.now(), Instant.now().plusSeconds(60), claims);
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_ADMIN.name()));
        OidcUser user = new DefaultOidcUser(authorities, idToken);
        OAuth2AuthenticationToken auth2AuthenticationToken = new OAuth2AuthenticationToken(user, authorities, "oidc");
        securityContext.setAuthentication(auth2AuthenticationToken);
        SecurityContextHolder.setContext(securityContext);

        Optional<String> login = SecurityUtils.getCurrentUserLogin();

        assertThat(login).contains("admin");
    }

    @Test
    void testExtractAuthorityFromClaims() {
        Map<String, Object> claims = new HashMap<>();
        claims.put(
            "groups",
            Arrays.asList(
                AuthoritiesConstants.LEGALREF_ADMIN.name(),
                AuthoritiesConstants.LEGALREF_BUSINESS.name(),
                AuthoritiesConstants.LEGALREF_CUSTOMER.name(),
                AuthoritiesConstants.LEGALREF_SYNCHRONIZER.name()
            )
        );

        List<GrantedAuthority> expectedAuthorities = Arrays.asList(
            new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_ADMIN.name()),
            new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_BUSINESS.name()),
            new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_CUSTOMER.name()),
            new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_SYNCHRONIZER.name())
        );

        List<GrantedAuthority> authorities = SecurityUtils.extractAuthorityFromClaims(claims);

        assertThat(authorities).isNotNull().isNotEmpty().hasSize(4).containsAll(expectedAuthorities);
    }

    @Test
    void testExtractAuthorityFromClaims_NamespacedRoles() {
        Map<String, Object> claims = new HashMap<>();
        claims.put(
            SecurityUtils.CLAIMS_NAMESPACE + "roles",
            Arrays.asList(
                AuthoritiesConstants.LEGALREF_ADMIN.name(),
                AuthoritiesConstants.LEGALREF_BUSINESS.name(),
                AuthoritiesConstants.LEGALREF_CUSTOMER.name(),
                AuthoritiesConstants.LEGALREF_SYNCHRONIZER.name()
            )
        );

        List<GrantedAuthority> expectedAuthorities = Arrays.asList(
            new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_ADMIN.name()),
            new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_BUSINESS.name()),
            new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_CUSTOMER.name()),
            new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_SYNCHRONIZER.name())
        );

        List<GrantedAuthority> authorities = SecurityUtils.extractAuthorityFromClaims(claims);

        assertThat(authorities).isNotNull().isNotEmpty().hasSize(4).containsAll(expectedAuthorities);
    }

    @Test
    void testIsAuthenticated() {
        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
        securityContext.setAuthentication(new UsernamePasswordAuthenticationToken("admin", "admin"));
        SecurityContextHolder.setContext(securityContext);
        boolean isAuthenticated = SecurityUtils.isAuthenticated();
        assertThat(isAuthenticated).isTrue();
    }

    @Test
    void testAnonymousIsNotAuthenticated() {
        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority(AuthoritiesConstants.ANONYMOUS.name()));
        securityContext.setAuthentication(new UsernamePasswordAuthenticationToken("anonymous", "anonymous", authorities));
        SecurityContextHolder.setContext(securityContext);
        boolean isAuthenticated = SecurityUtils.isAuthenticated();
        assertThat(isAuthenticated).isFalse();
    }

    @Test
    void testHasCurrentUserThisAuthority() {
        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_CUSTOMER.name()));
        securityContext.setAuthentication(new UsernamePasswordAuthenticationToken("user", "user", authorities));
        SecurityContextHolder.setContext(securityContext);

        assertThat(SecurityUtils.hasCurrentUserThisAuthority(AuthoritiesConstants.LEGALREF_CUSTOMER.name())).isTrue();
        assertThat(SecurityUtils.hasCurrentUserThisAuthority(AuthoritiesConstants.LEGALREF_ADMIN.name())).isFalse();
    }

    @Test
    void testHasCurrentUserAnyOfAuthorities() {
        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_CUSTOMER.name()));
        securityContext.setAuthentication(new UsernamePasswordAuthenticationToken("user", "user", authorities));
        SecurityContextHolder.setContext(securityContext);

        assertThat(
            SecurityUtils.hasCurrentUserAnyOfAuthorities(
                AuthoritiesConstants.LEGALREF_CUSTOMER.name(),
                AuthoritiesConstants.LEGALREF_ADMIN.name()
            )
        )
            .isTrue();
        assertThat(
            SecurityUtils.hasCurrentUserAnyOfAuthorities(AuthoritiesConstants.ANONYMOUS.name(), AuthoritiesConstants.LEGALREF_ADMIN.name())
        )
            .isFalse();
    }

    @Test
    void testHasCurrentUserNoneOfAuthorities() {
        SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority(AuthoritiesConstants.LEGALREF_CUSTOMER.name()));
        securityContext.setAuthentication(new UsernamePasswordAuthenticationToken("user", "user", authorities));
        SecurityContextHolder.setContext(securityContext);

        assertThat(
            SecurityUtils.hasCurrentUserNoneOfAuthorities(
                AuthoritiesConstants.LEGALREF_CUSTOMER.name(),
                AuthoritiesConstants.LEGALREF_ADMIN.name()
            )
        )
            .isFalse();
        assertThat(
            SecurityUtils.hasCurrentUserNoneOfAuthorities(
                AuthoritiesConstants.ANONYMOUS.name(),
                AuthoritiesConstants.LEGALREF_ADMIN.toString()
            )
        )
            .isTrue();
    }
}
