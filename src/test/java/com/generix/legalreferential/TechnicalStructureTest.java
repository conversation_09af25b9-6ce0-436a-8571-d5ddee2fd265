package com.generix.legalreferential;

import static com.tngtech.archunit.base.DescribedPredicate.alwaysTrue;
import static com.tngtech.archunit.core.domain.JavaClass.Predicates.belongToAnyOf;
import static com.tngtech.archunit.library.Architectures.layeredArchitecture;

import com.tngtech.archunit.core.importer.ImportOption.DoNotIncludeTests;
import com.tngtech.archunit.junit.AnalyzeClasses;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;

@AnalyzeClasses(packagesOf = LegalReferentialApp.class, importOptions = DoNotIncludeTests.class)
class TechnicalStructureTest {

    // prettier-ignore
  @ArchTest
  final ArchRule respectsTechnicalArchitectureLayers = layeredArchitecture()
      .layer("Config").definedBy("..config..")
      .layer("Web").definedBy("..web..")
      .optionalLayer("Service").definedBy("..service..")
      .layer("Security").definedBy("..security..")
      .layer("Persistence").definedBy("..repository..")
      .layer("Domain").definedBy("..domain..")
      .layer("Logging").definedBy("..aop..")

      .whereLayer("Config").mayNotBeAccessedByAnyLayer()
      .whereLayer("Web").mayOnlyBeAccessedByLayers("Config")
      .whereLayer("Service").mayOnlyBeAccessedByLayers("Web", "Config")
      .whereLayer("Security")
      .mayOnlyBeAccessedByLayers("Config", "Service", "Web", "Domain", "Logging")
      .whereLayer("Persistence").mayOnlyBeAccessedByLayers("Service", "Security", "Web", "Config")
      .whereLayer("Domain")
      .mayOnlyBeAccessedByLayers("Persistence", "Service", "Security", "Web", "Config")

      .ignoreDependency(belongToAnyOf(LegalReferentialApp.class), alwaysTrue())
      .ignoreDependency(alwaysTrue(), belongToAnyOf(
          com.generix.legalreferential.config.Constants.class,
          com.generix.legalreferential.config.ApplicationProperties.class
      ));
}
