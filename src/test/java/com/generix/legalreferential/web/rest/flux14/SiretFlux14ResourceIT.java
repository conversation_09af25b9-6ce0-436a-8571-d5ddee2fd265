package com.generix.legalreferential.web.rest.flux14;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.Siren;
import com.generix.legalreferential.domain.Siret;
import com.generix.legalreferential.domain.enumeration.EstablishmentType;
import com.generix.legalreferential.domain.enumeration.Motif;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.domain.enumeration.TypeEntiteDestinataire;
import com.generix.legalreferential.repository.SirenRepository;
import com.generix.legalreferential.repository.SiretRepository;
import com.generix.legalreferential.security.SecurityUtils;
import com.generix.legalreferential.service.dto.custom.flux14.RequestFlux14SiretDTO;
import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link SiretFlux14Resource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_SYNCHRONIZER" })
class SiretFlux14ResourceIT {

    private static final Long DEFAULT_ID_INSTANCE = 100L;
    public static final String DEFAULT_SIRET = "12345678912345";
    private static final String DEFAULT_SIREN = "123456789";
    private static final EstablishmentType DEFAULT_ESTABLISHMENT_TYPE = EstablishmentType.P;
    private static final String DEFAULT_DENOMINATION = "Boulangerie Pascale";
    private static final Statut DEFAULT_STATUT = Statut.I;
    private static final Motif DEFAULT_MOTIF = Motif.C;

    private static final Statut DEFAULT_STATUS = Statut.A;

    private static final String DEFAULT_ADDRESS_LINE_1 = "AAAAAAAAAA";

    private static final String DEFAULT_ISSUER = "https://auth.staging.apps.generix.biz/auth/realms/legalreferential2";
    private static final String DEFAULT_CLIENTID = "legalref_client";
    private static final String DEFAULT_OWNER = "legalref_admin";

    private static final String DEFAULT_ADDRESS_LINE_2 = "AAAAAAAAAA";

    private static final String DEFAULT_ADDRESS_LINE_3 = "AAAAAAAAAA";

    private static final String DEFAULT_ADDRESS_CITY = "AAAAAAAAAA";

    private static final String DEFAULT_ADDRESS_COUNTRY_SUBDIVISION = "AAAAAAAAAA";

    private static final String DEFAULT_ADDRESS_COUNTRY_CODE = "AAAAAAAAAA";

    private static final String DEFAULT_ADDRESS_COUNTRY_LABEL = "AA";

    private static final Boolean DEFAULT_B_2_G_MOA = false;

    private static final Boolean DEFAULT_B_2_G_MOA_ONLY = false;

    private static final Boolean DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT = false;

    private static final Boolean DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT = false;

    private static final Boolean DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT = false;

    private static final Boolean DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT = false;

    private static final Boolean DEFAULT_DIFFUSIBLE = false;

    private static final String DEFAULT_ADDRESS_POSTAL_CODE = "AAAAA";

    @Autowired
    private MockMvc restMockMvc;

    @Autowired
    private SiretRepository siretRepository;

    @Autowired
    private SirenRepository sirenRepository;

    @BeforeAll
    public static void initForAllTest() {
        SiretFlux14ResourceIT.initStaticSecurityUtils();
    }

    private static void initStaticSecurityUtils() {
        if (mockedStatic == null) { // Prevent re-registering
            mockedStatic = Mockito.mockStatic(SecurityUtils.class);

            mockedStatic
                .when(() -> SecurityUtils.getIssuerCurrentUser())
                .thenReturn("https://auth.staging.apps.generix.biz/auth/realms/legalreferential2");
            mockedStatic.when(() -> SecurityUtils.getClientIDCurrentUser()).thenReturn("client_app");
            mockedStatic.when(() -> SecurityUtils.getPreferredUsernameCurrentUser()).thenReturn("admin");
        }
    }

    public static MockedStatic<SecurityUtils> mockedStatic;

    private RequestFlux14SiretDTO createValidRequestDTO() {
        RequestFlux14SiretDTO requestDTO = new RequestFlux14SiretDTO();
        requestDTO.setIdInstance(DEFAULT_ID_INSTANCE);
        requestDTO.setSiret(DEFAULT_SIRET);
        requestDTO.setTypeEtablissement(DEFAULT_ESTABLISHMENT_TYPE);
        requestDTO.setDenomination(DEFAULT_DENOMINATION);
        requestDTO.setStatut(DEFAULT_STATUT);
        requestDTO.setMotif(DEFAULT_MOTIF);
        return requestDTO;
    }

    public static Siren createValidSiren() {
        Siren siren = new Siren().siren(DEFAULT_SIREN).status(Statut.A).instanceID(DEFAULT_ID_INSTANCE).companyName("eeee");
        siren.setEntityType(TypeEntiteDestinataire.PUBLIQUE);

        return siren;
    }

    public static Siret createSiretEntity() {
        Siret siret = new Siret()
            .siret(DEFAULT_SIRET)
            .status(DEFAULT_STATUS)
            .establishmentType(DEFAULT_ESTABLISHMENT_TYPE)
            .denomination(DEFAULT_DENOMINATION)
            .addressLine1(DEFAULT_ADDRESS_LINE_1)
            .addressLine2(DEFAULT_ADDRESS_LINE_2)
            .addressLine3(DEFAULT_ADDRESS_LINE_3)
            .addressCity(DEFAULT_ADDRESS_CITY)
            .addressCountrySubdivision(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryCode(DEFAULT_ADDRESS_COUNTRY_CODE)
            .addressCountryLabel(DEFAULT_ADDRESS_COUNTRY_LABEL)
            .b2gMoa(DEFAULT_B_2_G_MOA)
            .b2gMoaOnly(DEFAULT_B_2_G_MOA_ONLY)
            .b2gPaymentStatusManagement(DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT)
            .b2gLegalEngagementManagement(DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT)
            .b2gLegalOrServiceEngagementManagement(DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT)
            .b2gServiceCodeManagement(DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT)
            .diffusible(DEFAULT_DIFFUSIBLE)
            .addressPostalCode(DEFAULT_ADDRESS_POSTAL_CODE);
        siret.setInstanceID(DEFAULT_ID_INSTANCE);
        siret.setIssuer(SiretFlux14ResourceIT.DEFAULT_ISSUER);
        siret.setClientID(SiretFlux14ResourceIT.DEFAULT_CLIENTID);
        siret.setOwner(SiretFlux14ResourceIT.DEFAULT_OWNER);
        return siret;
    }

    @Test
    @Transactional
    void patchSiret_Success() throws Exception {
        // Initialize the database
        Siret existingSiret = createSiretEntity();
        Siren validSiren = createValidSiren();
        sirenRepository.save(validSiren);
        existingSiret.setFkSiretSiren(validSiren);
        siretRepository.saveAndFlush(existingSiret);

        // Create the Request DTO
        RequestFlux14SiretDTO requestDTO = createValidRequestDTO();
        requestDTO.setMotif(Motif.P);
        // Perform the request
        restMockMvc
            .perform(put("/v1/siret").contentType(MediaType.APPLICATION_JSON).content(TestUtil.convertObjectToJsonBytes(requestDTO)))
            .andExpect(status().isOk());

        // Validate the Siret in the database
        Siret updatedSiret = siretRepository.findById(DEFAULT_ID_INSTANCE).orElseThrow();
        assertThat(updatedSiret.getSiret()).isEqualTo(DEFAULT_SIRET);
        assertThat(updatedSiret.getEstablishmentType()).isEqualTo(DEFAULT_ESTABLISHMENT_TYPE);
        assertThat(updatedSiret.getDenomination()).isEqualTo(DEFAULT_DENOMINATION);
        assertThat(updatedSiret.getStatus()).isEqualTo(DEFAULT_STATUT);
    }

    @Test
    @Transactional
    void patchSiret_BadRequest_MissingIdInstance() throws Exception {
        // Create an invalid Request DTO (missing idInstance)
        RequestFlux14SiretDTO requestDTO = createValidRequestDTO();
        requestDTO.setIdInstance(null);

        // Perform the request
        restMockMvc
            .perform(put("/v1/siret").contentType(MediaType.APPLICATION_JSON).content(TestUtil.convertObjectToJsonBytes(requestDTO)))
            .andExpect(status().isBadRequest());
    }

    @Test
    @Transactional
    void deleteSiret_NotFound() throws Exception {
        // Create the Request DTO for a non-existent Siret
        RequestFlux14SiretDTO requestDTO = createValidRequestDTO();
        requestDTO.setMotif(Motif.S);
        // Perform the request
        restMockMvc
            .perform(put("/v1/siret").contentType(MediaType.APPLICATION_JSON).content(TestUtil.convertObjectToJsonBytes(requestDTO)))
            .andExpect(status().isNotFound()); // Assuming deletion alert is triggered if not found
    }

    @Test
    @Transactional
    void deleteSiret_Success() throws Exception {
        Siret Siret = createSiretEntity();
        siretRepository.saveAndFlush(Siret);
        RequestFlux14SiretDTO validRequestDTO = createValidRequestDTO();
        validRequestDTO.setMotif(Motif.S);
        restMockMvc
            .perform(put("/v1/siret").contentType(MediaType.APPLICATION_JSON).content(TestUtil.convertObjectToJsonBytes(validRequestDTO)))
            .andExpect(status().isNotFound()); // Assuming deletion alert is triggered if not found
    }

    @AfterAll
    public static void tearDown() {
        if (SiretFlux14ResourceIT.mockedStatic != null) {
            SiretFlux14ResourceIT.mockedStatic.close();
            SiretFlux14ResourceIT.mockedStatic = null;
        }
    }
}
