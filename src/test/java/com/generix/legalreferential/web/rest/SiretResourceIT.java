package com.generix.legalreferential.web.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.AddressLine;
import com.generix.legalreferential.domain.RoutingCode;
import com.generix.legalreferential.domain.Siren;
import com.generix.legalreferential.domain.Siret;
import com.generix.legalreferential.domain.enumeration.EstablishmentType;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.repository.SiretRepository;
import com.generix.legalreferential.security.SecurityUtils;
import com.generix.legalreferential.service.dto.SiretDTO;
import com.generix.legalreferential.service.mapper.SiretMapper;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link SiretResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_ADMIN" })
class SiretResourceIT {

    private static final String DEFAULT_SIRET = "AAAAAAAAAA";
    private static final String UPDATED_SIRET = "BBBBBBBBBB";

    private static final Statut DEFAULT_STATUS = Statut.A;
    private static final Statut UPDATED_STATUS = Statut.I;

    private static final EstablishmentType DEFAULT_ESTABLISHMENT_TYPE = EstablishmentType.P;
    private static final EstablishmentType UPDATED_ESTABLISHMENT_TYPE = EstablishmentType.S;

    private static final String DEFAULT_DENOMINATION = "AAAAAAAAAA";
    private static final String UPDATED_DENOMINATION = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_LINE_1 = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_LINE_1 = "BBBBBBBBBB";

    private static final String DEFAULT_ISSUER = "https://auth.staging.apps.generix.biz/auth/realms/legalreferential2";
    private static final String DEFAULT_CLIENTID = "legalref_client";
    private static final String DEFAULT_OWNER = "legalref_admin";

    private static final String DEFAULT_ADDRESS_LINE_2 = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_LINE_2 = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_LINE_3 = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_LINE_3 = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_CITY = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_CITY = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_COUNTRY_SUBDIVISION = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_COUNTRY_SUBDIVISION = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_COUNTRY_CODE = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_COUNTRY_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_COUNTRY_LABEL = "AA";
    private static final String UPDATED_ADDRESS_COUNTRY_LABEL = "BB";

    private static final Boolean DEFAULT_B_2_G_MOA = false;
    private static final Boolean UPDATED_B_2_G_MOA = true;

    private static final Boolean DEFAULT_B_2_G_MOA_ONLY = false;
    private static final Boolean UPDATED_B_2_G_MOA_ONLY = true;

    private static final Boolean DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT = false;
    private static final Boolean UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT = true;

    private static final Boolean DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT = false;
    private static final Boolean UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT = true;

    private static final Boolean DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT = false;
    private static final Boolean UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT = true;

    private static final Boolean DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT = false;
    private static final Boolean UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT = true;

    private static final Boolean DEFAULT_DIFFUSIBLE = false;
    private static final Boolean UPDATED_DIFFUSIBLE = true;

    private static final String DEFAULT_ADDRESS_POSTAL_CODE = "AAAAA";
    private static final String UPDATED_ADDRESS_POSTAL_CODE = "BBBBB";

    private static final String ENTITY_API_URL = "/api/sirets";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong count = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private SiretRepository siretRepository;

    @Autowired
    private SiretMapper siretMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restSiretMockMvc;

    private Siret siret;

    public static MockedStatic<SecurityUtils> mockedStatic;

    /**
     * Create an entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it, if they test an entity
     * which requires the current entity.
     */
    public static Siret createEntity(EntityManager em) {
        Siret siret = new Siret()
            .siret(DEFAULT_SIRET)
            .status(DEFAULT_STATUS)
            .establishmentType(DEFAULT_ESTABLISHMENT_TYPE)
            .denomination(DEFAULT_DENOMINATION)
            .addressLine1(DEFAULT_ADDRESS_LINE_1)
            .addressLine2(DEFAULT_ADDRESS_LINE_2)
            .addressLine3(DEFAULT_ADDRESS_LINE_3)
            .addressCity(DEFAULT_ADDRESS_CITY)
            .addressCountrySubdivision(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryCode(DEFAULT_ADDRESS_COUNTRY_CODE)
            .addressCountryLabel(DEFAULT_ADDRESS_COUNTRY_LABEL)
            .b2gMoa(DEFAULT_B_2_G_MOA)
            .b2gMoaOnly(DEFAULT_B_2_G_MOA_ONLY)
            .b2gPaymentStatusManagement(DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT)
            .b2gLegalEngagementManagement(DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT)
            .b2gLegalOrServiceEngagementManagement(DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT)
            .b2gServiceCodeManagement(DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT)
            .diffusible(DEFAULT_DIFFUSIBLE)
            .addressPostalCode(DEFAULT_ADDRESS_POSTAL_CODE);
        siret.setInstanceID(100L);
        siret.setIssuer(SiretResourceIT.DEFAULT_ISSUER);
        siret.setClientID(SiretResourceIT.DEFAULT_CLIENTID);
        siret.setOwner(SiretResourceIT.DEFAULT_OWNER);
        return siret;
    }

    /**
     * Create an updated entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it, if they test an entity
     * which requires the current entity.
     */
    public static Siret createUpdatedEntity(EntityManager em) {
        Siret siret = new Siret()
            .siret(UPDATED_SIRET)
            .status(UPDATED_STATUS)
            .establishmentType(UPDATED_ESTABLISHMENT_TYPE)
            .denomination(UPDATED_DENOMINATION)
            .addressLine1(UPDATED_ADDRESS_LINE_1)
            .addressLine2(UPDATED_ADDRESS_LINE_2)
            .addressLine3(UPDATED_ADDRESS_LINE_3)
            .addressCity(UPDATED_ADDRESS_CITY)
            .addressCountrySubdivision(UPDATED_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryCode(UPDATED_ADDRESS_COUNTRY_CODE)
            .addressCountryLabel(UPDATED_ADDRESS_COUNTRY_LABEL)
            .b2gMoa(UPDATED_B_2_G_MOA)
            .b2gMoaOnly(UPDATED_B_2_G_MOA_ONLY)
            .b2gPaymentStatusManagement(UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT)
            .b2gLegalEngagementManagement(UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT)
            .b2gLegalOrServiceEngagementManagement(UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT)
            .b2gServiceCodeManagement(UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT)
            .diffusible(UPDATED_DIFFUSIBLE)
            .addressPostalCode(UPDATED_ADDRESS_POSTAL_CODE);
        siret.setIssuer(SiretResourceIT.DEFAULT_ISSUER);
        siret.setClientID(SiretResourceIT.DEFAULT_CLIENTID);
        siret.setOwner(SiretResourceIT.DEFAULT_OWNER);
        return siret;
    }

    @AfterAll
    public static void teardown() {
        mockedStatic.close();
    }

    @BeforeAll
    public static void initForAllTest() {
        initStaticSecurityUtils();
    }

    private static void initStaticSecurityUtils() {
        mockedStatic = Mockito.mockStatic(SecurityUtils.class);

        mockedStatic
            .when(() -> SecurityUtils.getIssuerCurrentUser())
            .thenReturn("https://auth.staging.apps.generix.biz/auth/realms/legalreferential2");
        mockedStatic.when(() -> SecurityUtils.getClientIDCurrentUser()).thenReturn("client_app");
        mockedStatic.when(() -> SecurityUtils.getPreferredUsernameCurrentUser()).thenReturn("admin");
    }

    @BeforeEach
    public void initTest() {
        this.siret = SiretResourceIT.createEntity(this.em);
        Siren siren = SirenResourceIT.createEntity(this.em);
        this.em.persist(siren);
        this.siret.setFkSiretSiren(siren);
    }

    @Test
    @Transactional
    void createSiret() throws Exception {
        int databaseSizeBeforeCreate = this.siretRepository.findAll().size();
        // Create the Siret
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);
        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isCreated());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeCreate + 1);
        Siret testSiret = siretList.get(siretList.size() - 1);
        assertThat(testSiret.getSiret()).isEqualTo(DEFAULT_SIRET);
        assertThat(testSiret.getStatus()).isEqualTo(DEFAULT_STATUS);
        assertThat(testSiret.getEstablishmentType()).isEqualTo(DEFAULT_ESTABLISHMENT_TYPE);
        assertThat(testSiret.getDenomination()).isEqualTo(DEFAULT_DENOMINATION);
        assertThat(testSiret.getAddressLine1()).isEqualTo(DEFAULT_ADDRESS_LINE_1);
        assertThat(testSiret.getAddressLine2()).isEqualTo(DEFAULT_ADDRESS_LINE_2);
        assertThat(testSiret.getAddressLine3()).isEqualTo(DEFAULT_ADDRESS_LINE_3);
        assertThat(testSiret.getAddressCity()).isEqualTo(DEFAULT_ADDRESS_CITY);
        assertThat(testSiret.getAddressCountrySubdivision()).isEqualTo(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION);
        assertThat(testSiret.getAddressCountryCode()).isEqualTo(DEFAULT_ADDRESS_COUNTRY_CODE);
        assertThat(testSiret.getAddressCountryLabel()).isEqualTo(DEFAULT_ADDRESS_COUNTRY_LABEL);
        assertThat(testSiret.getB2gMoa()).isEqualTo(DEFAULT_B_2_G_MOA);
        assertThat(testSiret.getB2gMoaOnly()).isEqualTo(DEFAULT_B_2_G_MOA_ONLY);
        assertThat(testSiret.getB2gPaymentStatusManagement()).isEqualTo(DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT);
        assertThat(testSiret.getB2gLegalEngagementManagement()).isEqualTo(DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT);
        assertThat(testSiret.getB2gLegalOrServiceEngagementManagement()).isEqualTo(DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT);
        assertThat(testSiret.getB2gServiceCodeManagement()).isEqualTo(DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT);
        assertThat(testSiret.getDiffusible()).isEqualTo(DEFAULT_DIFFUSIBLE);
        assertThat(testSiret.getAddressPostalCode()).isEqualTo(DEFAULT_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void createSiretWithExistingId() throws Exception {
        // Create the Siret with an existing ID
        this.siret.setInstanceID(null);
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        int databaseSizeBeforeCreate = this.siretRepository.findAll().size();

        // An entity with an existing ID cannot be created, so this API call must fail
        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkSiretIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setSiret(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setStatus(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEstablishmentTypeIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setEstablishmentType(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDenominationIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setDenomination(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressLine1IsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setAddressLine1(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressLine2IsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setAddressLine2(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressLine3IsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setAddressLine3(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressCityIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setAddressCity(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressCountrySubdivisionIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setAddressCountrySubdivision(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressCountryCodeIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setAddressCountryCode(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressCountryLabelIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setAddressCountryLabel(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressPostalCodeIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.siretRepository.findAll().size();
        // set the field null
        this.siret.setAddressPostalCode(null);

        // Create the Siret, which fails.
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        this.restSiretMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllSirets() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList
        this.restSiretMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.siret.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].siret").value(hasItem(DEFAULT_SIRET)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].establishmentType").value(hasItem(DEFAULT_ESTABLISHMENT_TYPE.toString())))
            .andExpect(jsonPath("$.[*].denomination").value(hasItem(DEFAULT_DENOMINATION)))
            .andExpect(jsonPath("$.[*].addressLine1").value(hasItem(DEFAULT_ADDRESS_LINE_1)))
            .andExpect(jsonPath("$.[*].addressLine2").value(hasItem(DEFAULT_ADDRESS_LINE_2)))
            .andExpect(jsonPath("$.[*].addressLine3").value(hasItem(DEFAULT_ADDRESS_LINE_3)))
            .andExpect(jsonPath("$.[*].addressCity").value(hasItem(DEFAULT_ADDRESS_CITY)))
            .andExpect(jsonPath("$.[*].addressCountrySubdivision").value(hasItem(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION)))
            .andExpect(jsonPath("$.[*].addressCountryCode").value(hasItem(DEFAULT_ADDRESS_COUNTRY_CODE)))
            .andExpect(jsonPath("$.[*].addressCountryLabel").value(hasItem(DEFAULT_ADDRESS_COUNTRY_LABEL)))
            .andExpect(jsonPath("$.[*].b2gMoa").value(hasItem(DEFAULT_B_2_G_MOA.booleanValue())))
            .andExpect(jsonPath("$.[*].b2gMoaOnly").value(hasItem(DEFAULT_B_2_G_MOA_ONLY.booleanValue())))
            .andExpect(jsonPath("$.[*].b2gPaymentStatusManagement").value(hasItem(DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT.booleanValue())))
            .andExpect(
                jsonPath("$.[*].b2gLegalEngagementManagement").value(hasItem(DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT.booleanValue()))
            )
            .andExpect(
                jsonPath("$.[*].b2gLegalOrServiceEngagementManagement")
                    .value(hasItem(DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT.booleanValue()))
            )
            .andExpect(jsonPath("$.[*].b2gServiceCodeManagement").value(hasItem(DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT.booleanValue())))
            .andExpect(jsonPath("$.[*].diffusible").value(hasItem(DEFAULT_DIFFUSIBLE.booleanValue())))
            .andExpect(jsonPath("$.[*].addressPostalCode").value(hasItem(DEFAULT_ADDRESS_POSTAL_CODE)));
    }

    @Test
    @Transactional
    void getSiret() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get the siret
        this.restSiretMockMvc.perform(get(ENTITY_API_URL_ID, this.siret.getInstanceID()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.instanceID").value(this.siret.getInstanceID().intValue()))
            .andExpect(jsonPath("$.siret").value(DEFAULT_SIRET))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.establishmentType").value(DEFAULT_ESTABLISHMENT_TYPE.toString()))
            .andExpect(jsonPath("$.denomination").value(DEFAULT_DENOMINATION))
            .andExpect(jsonPath("$.addressLine1").value(DEFAULT_ADDRESS_LINE_1))
            .andExpect(jsonPath("$.addressLine2").value(DEFAULT_ADDRESS_LINE_2))
            .andExpect(jsonPath("$.addressLine3").value(DEFAULT_ADDRESS_LINE_3))
            .andExpect(jsonPath("$.addressCity").value(DEFAULT_ADDRESS_CITY))
            .andExpect(jsonPath("$.addressCountrySubdivision").value(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION))
            .andExpect(jsonPath("$.addressCountryCode").value(DEFAULT_ADDRESS_COUNTRY_CODE))
            .andExpect(jsonPath("$.addressCountryLabel").value(DEFAULT_ADDRESS_COUNTRY_LABEL))
            .andExpect(jsonPath("$.b2gMoa").value(DEFAULT_B_2_G_MOA.booleanValue()))
            .andExpect(jsonPath("$.b2gMoaOnly").value(DEFAULT_B_2_G_MOA_ONLY.booleanValue()))
            .andExpect(jsonPath("$.b2gPaymentStatusManagement").value(DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT.booleanValue()))
            .andExpect(jsonPath("$.b2gLegalEngagementManagement").value(DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT.booleanValue()))
            .andExpect(
                jsonPath("$.b2gLegalOrServiceEngagementManagement")
                    .value(DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT.booleanValue())
            )
            .andExpect(jsonPath("$.b2gServiceCodeManagement").value(DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT.booleanValue()))
            .andExpect(jsonPath("$.diffusible").value(DEFAULT_DIFFUSIBLE.booleanValue()))
            .andExpect(jsonPath("$.addressPostalCode").value(DEFAULT_ADDRESS_POSTAL_CODE));
    }

    @Test
    @Transactional
    void getSiretsByIdFiltering() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        Long id = this.siret.getInstanceID();

        defaultSiretShouldBeFound("instanceID.equals=" + id);
        defaultSiretShouldNotBeFound("instanceID.notEquals=" + id);

        defaultSiretShouldBeFound("instanceID.greaterThanOrEqual=" + id);
        defaultSiretShouldNotBeFound("instanceID.greaterThan=" + id);

        defaultSiretShouldBeFound("instanceID.lessThanOrEqual=" + id);
        defaultSiretShouldNotBeFound("instanceID.lessThan=" + id);
    }

    @Test
    @Transactional
    void getAllSiretsBySiretIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where siret equals to DEFAULT_SIRET
        defaultSiretShouldBeFound("siret.equals=" + DEFAULT_SIRET);

        // Get all the siretList where siret equals to UPDATED_SIRET
        defaultSiretShouldNotBeFound("siret.equals=" + UPDATED_SIRET);
    }

    @Test
    @Transactional
    void getAllSiretsBySiretIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where siret in DEFAULT_SIRET or UPDATED_SIRET
        defaultSiretShouldBeFound("siret.in=" + DEFAULT_SIRET + "," + UPDATED_SIRET);

        // Get all the siretList where siret equals to UPDATED_SIRET
        defaultSiretShouldNotBeFound("siret.in=" + UPDATED_SIRET);
    }

    @Test
    @Transactional
    void getAllSiretsBySiretIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where siret is not null
        defaultSiretShouldBeFound("siret.specified=true");

        // Get all the siretList where siret is null
        defaultSiretShouldNotBeFound("siret.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsBySiretContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where siret contains DEFAULT_SIRET
        defaultSiretShouldBeFound("siret.contains=" + DEFAULT_SIRET);

        // Get all the siretList where siret contains UPDATED_SIRET
        defaultSiretShouldNotBeFound("siret.contains=" + UPDATED_SIRET);
    }

    @Test
    @Transactional
    void getAllSiretsBySiretNotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where siret does not contain DEFAULT_SIRET
        defaultSiretShouldNotBeFound("siret.doesNotContain=" + DEFAULT_SIRET);

        // Get all the siretList where siret does not contain UPDATED_SIRET
        defaultSiretShouldBeFound("siret.doesNotContain=" + UPDATED_SIRET);
    }

    @Test
    @Transactional
    void getAllSiretsByStatusIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where status equals to DEFAULT_STATUS
        defaultSiretShouldBeFound("status.equals=" + DEFAULT_STATUS);

        // Get all the siretList where status equals to UPDATED_STATUS
        defaultSiretShouldNotBeFound("status.equals=" + UPDATED_STATUS);
    }

    @Test
    @Transactional
    void getAllSiretsByStatusIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where status in DEFAULT_STATUS or UPDATED_STATUS
        defaultSiretShouldBeFound("status.in=" + DEFAULT_STATUS + "," + UPDATED_STATUS);

        // Get all the siretList where status equals to UPDATED_STATUS
        defaultSiretShouldNotBeFound("status.in=" + UPDATED_STATUS);
    }

    @Test
    @Transactional
    void getAllSiretsByStatusIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where status is not null
        defaultSiretShouldBeFound("status.specified=true");

        // Get all the siretList where status is null
        defaultSiretShouldNotBeFound("status.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByEstablishmentTypeIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where establishmentType equals to DEFAULT_ESTABLISHMENT_TYPE
        defaultSiretShouldBeFound("establishmentType.equals=" + DEFAULT_ESTABLISHMENT_TYPE);

        // Get all the siretList where establishmentType equals to UPDATED_ESTABLISHMENT_TYPE
        defaultSiretShouldNotBeFound("establishmentType.equals=" + UPDATED_ESTABLISHMENT_TYPE);
    }

    @Test
    @Transactional
    void getAllSiretsByEstablishmentTypeIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where establishmentType in DEFAULT_ESTABLISHMENT_TYPE or UPDATED_ESTABLISHMENT_TYPE
        defaultSiretShouldBeFound("establishmentType.in=" + DEFAULT_ESTABLISHMENT_TYPE + "," + UPDATED_ESTABLISHMENT_TYPE);

        // Get all the siretList where establishmentType equals to UPDATED_ESTABLISHMENT_TYPE
        defaultSiretShouldNotBeFound("establishmentType.in=" + UPDATED_ESTABLISHMENT_TYPE);
    }

    @Test
    @Transactional
    void getAllSiretsByEstablishmentTypeIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where establishmentType is not null
        defaultSiretShouldBeFound("establishmentType.specified=true");

        // Get all the siretList where establishmentType is null
        defaultSiretShouldNotBeFound("establishmentType.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByDenominationIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where denomination equals to DEFAULT_DENOMINATION
        defaultSiretShouldBeFound("denomination.equals=" + DEFAULT_DENOMINATION);

        // Get all the siretList where denomination equals to UPDATED_DENOMINATION
        defaultSiretShouldNotBeFound("denomination.equals=" + UPDATED_DENOMINATION);
    }

    @Test
    @Transactional
    void getAllSiretsByDenominationIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where denomination in DEFAULT_DENOMINATION or UPDATED_DENOMINATION
        defaultSiretShouldBeFound("denomination.in=" + DEFAULT_DENOMINATION + "," + UPDATED_DENOMINATION);

        // Get all the siretList where denomination equals to UPDATED_DENOMINATION
        defaultSiretShouldNotBeFound("denomination.in=" + UPDATED_DENOMINATION);
    }

    @Test
    @Transactional
    void getAllSiretsByDenominationIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where denomination is not null
        defaultSiretShouldBeFound("denomination.specified=true");

        // Get all the siretList where denomination is null
        defaultSiretShouldNotBeFound("denomination.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByDenominationContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where denomination contains DEFAULT_DENOMINATION
        defaultSiretShouldBeFound("denomination.contains=" + DEFAULT_DENOMINATION);

        // Get all the siretList where denomination contains UPDATED_DENOMINATION
        defaultSiretShouldNotBeFound("denomination.contains=" + UPDATED_DENOMINATION);
    }

    @Test
    @Transactional
    void getAllSiretsByDenominationNotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where denomination does not contain DEFAULT_DENOMINATION
        defaultSiretShouldNotBeFound("denomination.doesNotContain=" + DEFAULT_DENOMINATION);

        // Get all the siretList where denomination does not contain UPDATED_DENOMINATION
        defaultSiretShouldBeFound("denomination.doesNotContain=" + UPDATED_DENOMINATION);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine1IsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine1 equals to DEFAULT_ADDRESS_LINE_1
        defaultSiretShouldBeFound("addressLine1.equals=" + DEFAULT_ADDRESS_LINE_1);

        // Get all the siretList where addressLine1 equals to UPDATED_ADDRESS_LINE_1
        defaultSiretShouldNotBeFound("addressLine1.equals=" + UPDATED_ADDRESS_LINE_1);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine1IsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine1 in DEFAULT_ADDRESS_LINE_1 or UPDATED_ADDRESS_LINE_1
        defaultSiretShouldBeFound("addressLine1.in=" + DEFAULT_ADDRESS_LINE_1 + "," + UPDATED_ADDRESS_LINE_1);

        // Get all the siretList where addressLine1 equals to UPDATED_ADDRESS_LINE_1
        defaultSiretShouldNotBeFound("addressLine1.in=" + UPDATED_ADDRESS_LINE_1);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine1IsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine1 is not null
        defaultSiretShouldBeFound("addressLine1.specified=true");

        // Get all the siretList where addressLine1 is null
        defaultSiretShouldNotBeFound("addressLine1.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine1ContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine1 contains DEFAULT_ADDRESS_LINE_1
        defaultSiretShouldBeFound("addressLine1.contains=" + DEFAULT_ADDRESS_LINE_1);

        // Get all the siretList where addressLine1 contains UPDATED_ADDRESS_LINE_1
        defaultSiretShouldNotBeFound("addressLine1.contains=" + UPDATED_ADDRESS_LINE_1);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine1NotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine1 does not contain DEFAULT_ADDRESS_LINE_1
        defaultSiretShouldNotBeFound("addressLine1.doesNotContain=" + DEFAULT_ADDRESS_LINE_1);

        // Get all the siretList where addressLine1 does not contain UPDATED_ADDRESS_LINE_1
        defaultSiretShouldBeFound("addressLine1.doesNotContain=" + UPDATED_ADDRESS_LINE_1);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine2IsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine2 equals to DEFAULT_ADDRESS_LINE_2
        defaultSiretShouldBeFound("addressLine2.equals=" + DEFAULT_ADDRESS_LINE_2);

        // Get all the siretList where addressLine2 equals to UPDATED_ADDRESS_LINE_2
        defaultSiretShouldNotBeFound("addressLine2.equals=" + UPDATED_ADDRESS_LINE_2);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine2IsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine2 in DEFAULT_ADDRESS_LINE_2 or UPDATED_ADDRESS_LINE_2
        defaultSiretShouldBeFound("addressLine2.in=" + DEFAULT_ADDRESS_LINE_2 + "," + UPDATED_ADDRESS_LINE_2);

        // Get all the siretList where addressLine2 equals to UPDATED_ADDRESS_LINE_2
        defaultSiretShouldNotBeFound("addressLine2.in=" + UPDATED_ADDRESS_LINE_2);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine2IsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine2 is not null
        defaultSiretShouldBeFound("addressLine2.specified=true");

        // Get all the siretList where addressLine2 is null
        defaultSiretShouldNotBeFound("addressLine2.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine2ContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine2 contains DEFAULT_ADDRESS_LINE_2
        defaultSiretShouldBeFound("addressLine2.contains=" + DEFAULT_ADDRESS_LINE_2);

        // Get all the siretList where addressLine2 contains UPDATED_ADDRESS_LINE_2
        defaultSiretShouldNotBeFound("addressLine2.contains=" + UPDATED_ADDRESS_LINE_2);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine2NotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine2 does not contain DEFAULT_ADDRESS_LINE_2
        defaultSiretShouldNotBeFound("addressLine2.doesNotContain=" + DEFAULT_ADDRESS_LINE_2);

        // Get all the siretList where addressLine2 does not contain UPDATED_ADDRESS_LINE_2
        defaultSiretShouldBeFound("addressLine2.doesNotContain=" + UPDATED_ADDRESS_LINE_2);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine3IsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine3 equals to DEFAULT_ADDRESS_LINE_3
        defaultSiretShouldBeFound("addressLine3.equals=" + DEFAULT_ADDRESS_LINE_3);

        // Get all the siretList where addressLine3 equals to UPDATED_ADDRESS_LINE_3
        defaultSiretShouldNotBeFound("addressLine3.equals=" + UPDATED_ADDRESS_LINE_3);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine3IsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine3 in DEFAULT_ADDRESS_LINE_3 or UPDATED_ADDRESS_LINE_3
        defaultSiretShouldBeFound("addressLine3.in=" + DEFAULT_ADDRESS_LINE_3 + "," + UPDATED_ADDRESS_LINE_3);

        // Get all the siretList where addressLine3 equals to UPDATED_ADDRESS_LINE_3
        defaultSiretShouldNotBeFound("addressLine3.in=" + UPDATED_ADDRESS_LINE_3);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine3IsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine3 is not null
        defaultSiretShouldBeFound("addressLine3.specified=true");

        // Get all the siretList where addressLine3 is null
        defaultSiretShouldNotBeFound("addressLine3.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine3ContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine3 contains DEFAULT_ADDRESS_LINE_3
        defaultSiretShouldBeFound("addressLine3.contains=" + DEFAULT_ADDRESS_LINE_3);

        // Get all the siretList where addressLine3 contains UPDATED_ADDRESS_LINE_3
        defaultSiretShouldNotBeFound("addressLine3.contains=" + UPDATED_ADDRESS_LINE_3);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressLine3NotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressLine3 does not contain DEFAULT_ADDRESS_LINE_3
        defaultSiretShouldNotBeFound("addressLine3.doesNotContain=" + DEFAULT_ADDRESS_LINE_3);

        // Get all the siretList where addressLine3 does not contain UPDATED_ADDRESS_LINE_3
        defaultSiretShouldBeFound("addressLine3.doesNotContain=" + UPDATED_ADDRESS_LINE_3);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCityIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCity equals to DEFAULT_ADDRESS_CITY
        defaultSiretShouldBeFound("addressCity.equals=" + DEFAULT_ADDRESS_CITY);

        // Get all the siretList where addressCity equals to UPDATED_ADDRESS_CITY
        defaultSiretShouldNotBeFound("addressCity.equals=" + UPDATED_ADDRESS_CITY);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCityIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCity in DEFAULT_ADDRESS_CITY or UPDATED_ADDRESS_CITY
        defaultSiretShouldBeFound("addressCity.in=" + DEFAULT_ADDRESS_CITY + "," + UPDATED_ADDRESS_CITY);

        // Get all the siretList where addressCity equals to UPDATED_ADDRESS_CITY
        defaultSiretShouldNotBeFound("addressCity.in=" + UPDATED_ADDRESS_CITY);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCityIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCity is not null
        defaultSiretShouldBeFound("addressCity.specified=true");

        // Get all the siretList where addressCity is null
        defaultSiretShouldNotBeFound("addressCity.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCityContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCity contains DEFAULT_ADDRESS_CITY
        defaultSiretShouldBeFound("addressCity.contains=" + DEFAULT_ADDRESS_CITY);

        // Get all the siretList where addressCity contains UPDATED_ADDRESS_CITY
        defaultSiretShouldNotBeFound("addressCity.contains=" + UPDATED_ADDRESS_CITY);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCityNotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCity does not contain DEFAULT_ADDRESS_CITY
        defaultSiretShouldNotBeFound("addressCity.doesNotContain=" + DEFAULT_ADDRESS_CITY);

        // Get all the siretList where addressCity does not contain UPDATED_ADDRESS_CITY
        defaultSiretShouldBeFound("addressCity.doesNotContain=" + UPDATED_ADDRESS_CITY);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountrySubdivisionIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountrySubdivision equals to DEFAULT_ADDRESS_COUNTRY_SUBDIVISION
        defaultSiretShouldBeFound("addressCountrySubdivision.equals=" + DEFAULT_ADDRESS_COUNTRY_SUBDIVISION);

        // Get all the siretList where addressCountrySubdivision equals to UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultSiretShouldNotBeFound("addressCountrySubdivision.equals=" + UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountrySubdivisionIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountrySubdivision in DEFAULT_ADDRESS_COUNTRY_SUBDIVISION or UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultSiretShouldBeFound(
            "addressCountrySubdivision.in=" + DEFAULT_ADDRESS_COUNTRY_SUBDIVISION + "," + UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        );

        // Get all the siretList where addressCountrySubdivision equals to UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultSiretShouldNotBeFound("addressCountrySubdivision.in=" + UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountrySubdivisionIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountrySubdivision is not null
        defaultSiretShouldBeFound("addressCountrySubdivision.specified=true");

        // Get all the siretList where addressCountrySubdivision is null
        defaultSiretShouldNotBeFound("addressCountrySubdivision.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountrySubdivisionContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountrySubdivision contains DEFAULT_ADDRESS_COUNTRY_SUBDIVISION
        defaultSiretShouldBeFound("addressCountrySubdivision.contains=" + DEFAULT_ADDRESS_COUNTRY_SUBDIVISION);

        // Get all the siretList where addressCountrySubdivision contains UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultSiretShouldNotBeFound("addressCountrySubdivision.contains=" + UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountrySubdivisionNotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountrySubdivision does not contain DEFAULT_ADDRESS_COUNTRY_SUBDIVISION
        defaultSiretShouldNotBeFound("addressCountrySubdivision.doesNotContain=" + DEFAULT_ADDRESS_COUNTRY_SUBDIVISION);

        // Get all the siretList where addressCountrySubdivision does not contain UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultSiretShouldBeFound("addressCountrySubdivision.doesNotContain=" + UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryCodeIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryCode equals to DEFAULT_ADDRESS_COUNTRY_CODE
        defaultSiretShouldBeFound("addressCountryCode.equals=" + DEFAULT_ADDRESS_COUNTRY_CODE);

        // Get all the siretList where addressCountryCode equals to UPDATED_ADDRESS_COUNTRY_CODE
        defaultSiretShouldNotBeFound("addressCountryCode.equals=" + UPDATED_ADDRESS_COUNTRY_CODE);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryCodeIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryCode in DEFAULT_ADDRESS_COUNTRY_CODE or UPDATED_ADDRESS_COUNTRY_CODE
        defaultSiretShouldBeFound("addressCountryCode.in=" + DEFAULT_ADDRESS_COUNTRY_CODE + "," + UPDATED_ADDRESS_COUNTRY_CODE);

        // Get all the siretList where addressCountryCode equals to UPDATED_ADDRESS_COUNTRY_CODE
        defaultSiretShouldNotBeFound("addressCountryCode.in=" + UPDATED_ADDRESS_COUNTRY_CODE);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryCodeIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryCode is not null
        defaultSiretShouldBeFound("addressCountryCode.specified=true");

        // Get all the siretList where addressCountryCode is null
        defaultSiretShouldNotBeFound("addressCountryCode.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryCodeContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryCode contains DEFAULT_ADDRESS_COUNTRY_CODE
        defaultSiretShouldBeFound("addressCountryCode.contains=" + DEFAULT_ADDRESS_COUNTRY_CODE);

        // Get all the siretList where addressCountryCode contains UPDATED_ADDRESS_COUNTRY_CODE
        defaultSiretShouldNotBeFound("addressCountryCode.contains=" + UPDATED_ADDRESS_COUNTRY_CODE);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryCodeNotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryCode does not contain DEFAULT_ADDRESS_COUNTRY_CODE
        defaultSiretShouldNotBeFound("addressCountryCode.doesNotContain=" + DEFAULT_ADDRESS_COUNTRY_CODE);

        // Get all the siretList where addressCountryCode does not contain UPDATED_ADDRESS_COUNTRY_CODE
        defaultSiretShouldBeFound("addressCountryCode.doesNotContain=" + UPDATED_ADDRESS_COUNTRY_CODE);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryLabelIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryLabel equals to DEFAULT_ADDRESS_COUNTRY_LABEL
        defaultSiretShouldBeFound("addressCountryLabel.equals=" + DEFAULT_ADDRESS_COUNTRY_LABEL);

        // Get all the siretList where addressCountryLabel equals to UPDATED_ADDRESS_COUNTRY_LABEL
        defaultSiretShouldNotBeFound("addressCountryLabel.equals=" + UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryLabelIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryLabel in DEFAULT_ADDRESS_COUNTRY_LABEL or UPDATED_ADDRESS_COUNTRY_LABEL
        defaultSiretShouldBeFound("addressCountryLabel.in=" + DEFAULT_ADDRESS_COUNTRY_LABEL + "," + UPDATED_ADDRESS_COUNTRY_LABEL);

        // Get all the siretList where addressCountryLabel equals to UPDATED_ADDRESS_COUNTRY_LABEL
        defaultSiretShouldNotBeFound("addressCountryLabel.in=" + UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryLabelIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryLabel is not null
        defaultSiretShouldBeFound("addressCountryLabel.specified=true");

        // Get all the siretList where addressCountryLabel is null
        defaultSiretShouldNotBeFound("addressCountryLabel.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryLabelContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryLabel contains DEFAULT_ADDRESS_COUNTRY_LABEL
        defaultSiretShouldBeFound("addressCountryLabel.contains=" + DEFAULT_ADDRESS_COUNTRY_LABEL);

        // Get all the siretList where addressCountryLabel contains UPDATED_ADDRESS_COUNTRY_LABEL
        defaultSiretShouldNotBeFound("addressCountryLabel.contains=" + UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressCountryLabelNotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressCountryLabel does not contain DEFAULT_ADDRESS_COUNTRY_LABEL
        defaultSiretShouldNotBeFound("addressCountryLabel.doesNotContain=" + DEFAULT_ADDRESS_COUNTRY_LABEL);

        // Get all the siretList where addressCountryLabel does not contain UPDATED_ADDRESS_COUNTRY_LABEL
        defaultSiretShouldBeFound("addressCountryLabel.doesNotContain=" + UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gMoaIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gMoa equals to DEFAULT_B_2_G_MOA
        defaultSiretShouldBeFound("b2gMoa.equals=" + DEFAULT_B_2_G_MOA);

        // Get all the siretList where b2gMoa equals to UPDATED_B_2_G_MOA
        defaultSiretShouldNotBeFound("b2gMoa.equals=" + UPDATED_B_2_G_MOA);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gMoaIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gMoa in DEFAULT_B_2_G_MOA or UPDATED_B_2_G_MOA
        defaultSiretShouldBeFound("b2gMoa.in=" + DEFAULT_B_2_G_MOA + "," + UPDATED_B_2_G_MOA);

        // Get all the siretList where b2gMoa equals to UPDATED_B_2_G_MOA
        defaultSiretShouldNotBeFound("b2gMoa.in=" + UPDATED_B_2_G_MOA);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gMoaIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gMoa is not null
        defaultSiretShouldBeFound("b2gMoa.specified=true");

        // Get all the siretList where b2gMoa is null
        defaultSiretShouldNotBeFound("b2gMoa.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByb2gMoaOnlyIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gMoaOnly equals to DEFAULT_B_2_G_MOA_ONLY
        defaultSiretShouldBeFound("b2gMoaOnly.equals=" + DEFAULT_B_2_G_MOA_ONLY);

        // Get all the siretList where b2gMoaOnly equals to UPDATED_B_2_G_MOA_ONLY
        defaultSiretShouldNotBeFound("b2gMoaOnly.equals=" + UPDATED_B_2_G_MOA_ONLY);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gMoaOnlyIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gMoaOnly in DEFAULT_B_2_G_MOA_ONLY or UPDATED_B_2_G_MOA_ONLY
        defaultSiretShouldBeFound("b2gMoaOnly.in=" + DEFAULT_B_2_G_MOA_ONLY + "," + UPDATED_B_2_G_MOA_ONLY);

        // Get all the siretList where b2gMoaOnly equals to UPDATED_B_2_G_MOA_ONLY
        defaultSiretShouldNotBeFound("b2gMoaOnly.in=" + UPDATED_B_2_G_MOA_ONLY);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gMoaOnlyIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gMoaOnly is not null
        defaultSiretShouldBeFound("b2gMoaOnly.specified=true");

        // Get all the siretList where b2gMoaOnly is null
        defaultSiretShouldNotBeFound("b2gMoaOnly.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByb2gPaymentStatusManagementIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gPaymentStatusManagement equals to DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT
        defaultSiretShouldBeFound("b2gPaymentStatusManagement.equals=" + DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT);

        // Get all the siretList where b2gPaymentStatusManagement equals to UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT
        defaultSiretShouldNotBeFound("b2gPaymentStatusManagement.equals=" + UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gPaymentStatusManagementIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gPaymentStatusManagement in DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT or UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT
        defaultSiretShouldBeFound(
            "b2gPaymentStatusManagement.in=" + DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT + "," + UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT
        );

        // Get all the siretList where b2gPaymentStatusManagement equals to UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT
        defaultSiretShouldNotBeFound("b2gPaymentStatusManagement.in=" + UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gPaymentStatusManagementIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gPaymentStatusManagement is not null
        defaultSiretShouldBeFound("b2gPaymentStatusManagement.specified=true");

        // Get all the siretList where b2gPaymentStatusManagement is null
        defaultSiretShouldNotBeFound("b2gPaymentStatusManagement.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByb2gLegalEngagementManagementIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gLegalEngagementManagement equals to DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT
        defaultSiretShouldBeFound("b2gLegalEngagementManagement.equals=" + DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT);

        // Get all the siretList where b2gLegalEngagementManagement equals to UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT
        defaultSiretShouldNotBeFound("b2gLegalEngagementManagement.equals=" + UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gLegalEngagementManagementIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gLegalEngagementManagement in DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT or UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT
        defaultSiretShouldBeFound(
            "b2gLegalEngagementManagement.in=" + DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT + "," + UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT
        );

        // Get all the siretList where b2gLegalEngagementManagement equals to UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT
        defaultSiretShouldNotBeFound("b2gLegalEngagementManagement.in=" + UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gLegalEngagementManagementIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gLegalEngagementManagement is not null
        defaultSiretShouldBeFound("b2gLegalEngagementManagement.specified=true");

        // Get all the siretList where b2gLegalEngagementManagement is null
        defaultSiretShouldNotBeFound("b2gLegalEngagementManagement.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByb2gLegalOrServiceEngagementManagementIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gLegalOrServiceEngagementManagement equals to DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT
        defaultSiretShouldBeFound("b2gLegalOrServiceEngagementManagement.equals=" + DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT);

        // Get all the siretList where b2gLegalOrServiceEngagementManagement equals to UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT
        defaultSiretShouldNotBeFound(
            "b2gLegalOrServiceEngagementManagement.equals=" + UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT
        );
    }

    @Test
    @Transactional
    void getAllSiretsByb2gLegalOrServiceEngagementManagementIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gLegalOrServiceEngagementManagement in DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT or UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT
        defaultSiretShouldBeFound(
            "b2gLegalOrServiceEngagementManagement.in=" +
            DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT +
            "," +
            UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT
        );

        // Get all the siretList where b2gLegalOrServiceEngagementManagement equals to UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT
        defaultSiretShouldNotBeFound("b2gLegalOrServiceEngagementManagement.in=" + UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gLegalOrServiceEngagementManagementIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gLegalOrServiceEngagementManagement is not null
        defaultSiretShouldBeFound("b2gLegalOrServiceEngagementManagement.specified=true");

        // Get all the siretList where b2gLegalOrServiceEngagementManagement is null
        defaultSiretShouldNotBeFound("b2gLegalOrServiceEngagementManagement.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByb2gServiceCodeManagementIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gServiceCodeManagement equals to DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT
        defaultSiretShouldBeFound("b2gServiceCodeManagement.equals=" + DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT);

        // Get all the siretList where b2gServiceCodeManagement equals to UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT
        defaultSiretShouldNotBeFound("b2gServiceCodeManagement.equals=" + UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gServiceCodeManagementIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gServiceCodeManagement in DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT or UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT
        defaultSiretShouldBeFound(
            "b2gServiceCodeManagement.in=" + DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT + "," + UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT
        );

        // Get all the siretList where b2gServiceCodeManagement equals to UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT
        defaultSiretShouldNotBeFound("b2gServiceCodeManagement.in=" + UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT);
    }

    @Test
    @Transactional
    void getAllSiretsByb2gServiceCodeManagementIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where b2gServiceCodeManagement is not null
        defaultSiretShouldBeFound("b2gServiceCodeManagement.specified=true");

        // Get all the siretList where b2gServiceCodeManagement is null
        defaultSiretShouldNotBeFound("b2gServiceCodeManagement.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByDiffusibleIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where diffusible equals to DEFAULT_DIFFUSIBLE
        defaultSiretShouldBeFound("diffusible.equals=" + DEFAULT_DIFFUSIBLE);

        // Get all the siretList where diffusible equals to UPDATED_DIFFUSIBLE
        defaultSiretShouldNotBeFound("diffusible.equals=" + UPDATED_DIFFUSIBLE);
    }

    @Test
    @Transactional
    void getAllSiretsByDiffusibleIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where diffusible in DEFAULT_DIFFUSIBLE or UPDATED_DIFFUSIBLE
        defaultSiretShouldBeFound("diffusible.in=" + DEFAULT_DIFFUSIBLE + "," + UPDATED_DIFFUSIBLE);

        // Get all the siretList where diffusible equals to UPDATED_DIFFUSIBLE
        defaultSiretShouldNotBeFound("diffusible.in=" + UPDATED_DIFFUSIBLE);
    }

    @Test
    @Transactional
    void getAllSiretsByDiffusibleIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where diffusible is not null
        defaultSiretShouldBeFound("diffusible.specified=true");

        // Get all the siretList where diffusible is null
        defaultSiretShouldNotBeFound("diffusible.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByAddressPostalCodeIsEqualToSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressPostalCode equals to DEFAULT_ADDRESS_POSTAL_CODE
        defaultSiretShouldBeFound("addressPostalCode.equals=" + DEFAULT_ADDRESS_POSTAL_CODE);

        // Get all the siretList where addressPostalCode equals to UPDATED_ADDRESS_POSTAL_CODE
        defaultSiretShouldNotBeFound("addressPostalCode.equals=" + UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressPostalCodeIsInShouldWork() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressPostalCode in DEFAULT_ADDRESS_POSTAL_CODE or UPDATED_ADDRESS_POSTAL_CODE
        defaultSiretShouldBeFound("addressPostalCode.in=" + DEFAULT_ADDRESS_POSTAL_CODE + "," + UPDATED_ADDRESS_POSTAL_CODE);

        // Get all the siretList where addressPostalCode equals to UPDATED_ADDRESS_POSTAL_CODE
        defaultSiretShouldNotBeFound("addressPostalCode.in=" + UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressPostalCodeIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressPostalCode is not null
        defaultSiretShouldBeFound("addressPostalCode.specified=true");

        // Get all the siretList where addressPostalCode is null
        defaultSiretShouldNotBeFound("addressPostalCode.specified=false");
    }

    @Test
    @Transactional
    void getAllSiretsByAddressPostalCodeContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressPostalCode contains DEFAULT_ADDRESS_POSTAL_CODE
        defaultSiretShouldBeFound("addressPostalCode.contains=" + DEFAULT_ADDRESS_POSTAL_CODE);

        // Get all the siretList where addressPostalCode contains UPDATED_ADDRESS_POSTAL_CODE
        defaultSiretShouldNotBeFound("addressPostalCode.contains=" + UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void getAllSiretsByAddressPostalCodeNotContainsSomething() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        // Get all the siretList where addressPostalCode does not contain DEFAULT_ADDRESS_POSTAL_CODE
        defaultSiretShouldNotBeFound("addressPostalCode.doesNotContain=" + DEFAULT_ADDRESS_POSTAL_CODE);

        // Get all the siretList where addressPostalCode does not contain UPDATED_ADDRESS_POSTAL_CODE
        defaultSiretShouldBeFound("addressPostalCode.doesNotContain=" + UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void getAllSiretsByPkRoutingCodeIsEqualToSomething() throws Exception {
        RoutingCode pkRoutingCode;
        if (TestUtil.findAll(this.em, RoutingCode.class).isEmpty()) {
            this.siretRepository.saveAndFlush(this.siret);
            pkRoutingCode = RoutingCodeResourceIT.createEntity();
        } else {
            pkRoutingCode = TestUtil.findAll(this.em, RoutingCode.class).get(0);
        }
        this.em.persist(pkRoutingCode);
        this.em.flush();
        this.siret.addPkRoutingCode(pkRoutingCode);
        this.siretRepository.saveAndFlush(this.siret);
        Long pkRoutingCodeId = pkRoutingCode.getInstanceID();

        // Get all the siretList where pkRoutingCode equals to pkRoutingCodeId
        defaultSiretShouldBeFound("pkRoutingCodeId.equals=" + pkRoutingCodeId);

        // Get all the siretList where pkRoutingCode equals to (pkRoutingCodeId + 1)
        defaultSiretShouldNotBeFound("pkRoutingCodeId.equals=" + (pkRoutingCodeId + 1));
    }

    @Test
    @Transactional
    void getAllSiretsByPkAddressLineIsEqualToSomething() throws Exception {
        AddressLine pkAddressLine;
        if (TestUtil.findAll(this.em, AddressLine.class).isEmpty()) {
            this.siretRepository.saveAndFlush(this.siret);
            pkAddressLine = AddressLineResourceIT.createEntity(this.em);

            Siren siren = SirenResourceIT.createEntity(this.em);
            siren.setSiren("AAAAAAAA1");
            siren.instanceID(101L);
            this.em.persist(siren);
            Siret siret = SiretResourceIT.createEntity(this.em);
            siret.setSiret("AAAAAAAA2");
            siret.instanceID(101L);
            siret.setFkSiretSiren(siren);
            this.em.persist(siret);
            pkAddressLine.setFkAddressLineSiret(siret);
        } else {
            pkAddressLine = TestUtil.findAll(this.em, AddressLine.class).get(0);
        }
        this.em.persist(pkAddressLine);
        this.em.flush();
        this.siret.addPkAddressLine(pkAddressLine);
        this.siretRepository.saveAndFlush(this.siret);
        Long pkAddressLineId = pkAddressLine.getInstanceID();

        // Get all the siretList where pkAddressLine equals to pkAddressLineId
        defaultSiretShouldBeFound("pkAddressLineId.equals=" + pkAddressLineId);

        // Get all the siretList where pkAddressLine equals to (pkAddressLineId + 1)
        defaultSiretShouldNotBeFound("pkAddressLineId.equals=" + (pkAddressLineId + 1));
    }

    @Test
    @Transactional
    void getAllSiretsByFkSiretSirenIsEqualToSomething() throws Exception {
        Siren fkSiretSiren;
        if (TestUtil.findAll(this.em, Siren.class).isEmpty()) {
            this.siretRepository.saveAndFlush(this.siret);
            fkSiretSiren = SirenResourceIT.createEntity(this.em);
        } else {
            fkSiretSiren = TestUtil.findAll(this.em, Siren.class).get(0);
        }
        this.em.persist(fkSiretSiren);
        this.em.flush();
        this.siret.setFkSiretSiren(fkSiretSiren);
        this.siretRepository.saveAndFlush(this.siret);
        Long fkSiretSirenId = fkSiretSiren.getInstanceID();

        // Get all the siretList where fkSiretSiren equals to fkSiretSirenId
        defaultSiretShouldBeFound("fkSiretSirenId.equals=" + fkSiretSirenId);

        // Get all the siretList where fkSiretSiren equals to (fkSiretSirenId + 1)
        defaultSiretShouldNotBeFound("fkSiretSirenId.equals=" + (fkSiretSirenId + 1));
    }

    /**
     * Executes the search, and checks that the default entity is returned.
     */
    private void defaultSiretShouldBeFound(String filter) throws Exception {
        this.restSiretMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.siret.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].siret").value(hasItem(DEFAULT_SIRET)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].establishmentType").value(hasItem(DEFAULT_ESTABLISHMENT_TYPE.toString())))
            .andExpect(jsonPath("$.[*].denomination").value(hasItem(DEFAULT_DENOMINATION)))
            .andExpect(jsonPath("$.[*].addressLine1").value(hasItem(DEFAULT_ADDRESS_LINE_1)))
            .andExpect(jsonPath("$.[*].addressLine2").value(hasItem(DEFAULT_ADDRESS_LINE_2)))
            .andExpect(jsonPath("$.[*].addressLine3").value(hasItem(DEFAULT_ADDRESS_LINE_3)))
            .andExpect(jsonPath("$.[*].addressCity").value(hasItem(DEFAULT_ADDRESS_CITY)))
            .andExpect(jsonPath("$.[*].addressCountrySubdivision").value(hasItem(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION)))
            .andExpect(jsonPath("$.[*].addressCountryCode").value(hasItem(DEFAULT_ADDRESS_COUNTRY_CODE)))
            .andExpect(jsonPath("$.[*].addressCountryLabel").value(hasItem(DEFAULT_ADDRESS_COUNTRY_LABEL)))
            .andExpect(jsonPath("$.[*].b2gMoa").value(hasItem(DEFAULT_B_2_G_MOA.booleanValue())))
            .andExpect(jsonPath("$.[*].b2gMoaOnly").value(hasItem(DEFAULT_B_2_G_MOA_ONLY.booleanValue())))
            .andExpect(jsonPath("$.[*].b2gPaymentStatusManagement").value(hasItem(DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT.booleanValue())))
            .andExpect(
                jsonPath("$.[*].b2gLegalEngagementManagement").value(hasItem(DEFAULT_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT.booleanValue()))
            )
            .andExpect(
                jsonPath("$.[*].b2gLegalOrServiceEngagementManagement")
                    .value(hasItem(DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT.booleanValue()))
            )
            .andExpect(jsonPath("$.[*].b2gServiceCodeManagement").value(hasItem(DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT.booleanValue())))
            .andExpect(jsonPath("$.[*].diffusible").value(hasItem(DEFAULT_DIFFUSIBLE.booleanValue())))
            .andExpect(jsonPath("$.[*].addressPostalCode").value(hasItem(DEFAULT_ADDRESS_POSTAL_CODE)));

        // Check, that the count call also returns 1
        this.restSiretMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("1"));
    }

    /**
     * Executes the search, and checks that the default entity is not returned.
     */
    private void defaultSiretShouldNotBeFound(String filter) throws Exception {
        this.restSiretMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$").isEmpty());

        // Check, that the count call also returns 0
        this.restSiretMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("0"));
    }

    @Test
    @Transactional
    void getNonExistingSiret() throws Exception {
        // Get the siret
        this.restSiretMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingSiret() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        int databaseSizeBeforeUpdate = this.siretRepository.findAll().size();

        // Update the siret
        Siret updatedSiret = this.siretRepository.findById(this.siret.getInstanceID()).get();
        // Disconnect from session so that the updates on updatedSiret are not directly saved in db
        this.em.detach(updatedSiret);
        updatedSiret
            .siret(UPDATED_SIRET)
            .status(UPDATED_STATUS)
            .establishmentType(UPDATED_ESTABLISHMENT_TYPE)
            .denomination(UPDATED_DENOMINATION)
            .addressLine1(UPDATED_ADDRESS_LINE_1)
            .addressLine2(UPDATED_ADDRESS_LINE_2)
            .addressLine3(UPDATED_ADDRESS_LINE_3)
            .addressCity(UPDATED_ADDRESS_CITY)
            .addressCountrySubdivision(UPDATED_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryCode(UPDATED_ADDRESS_COUNTRY_CODE)
            .addressCountryLabel(UPDATED_ADDRESS_COUNTRY_LABEL)
            .b2gMoa(UPDATED_B_2_G_MOA)
            .b2gMoaOnly(UPDATED_B_2_G_MOA_ONLY)
            .b2gPaymentStatusManagement(UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT)
            .b2gLegalEngagementManagement(UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT)
            .b2gLegalOrServiceEngagementManagement(UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT)
            .b2gServiceCodeManagement(UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT)
            .diffusible(UPDATED_DIFFUSIBLE)
            .addressPostalCode(UPDATED_ADDRESS_POSTAL_CODE);
        SiretDTO siretDTO = this.siretMapper.toDto(updatedSiret);

        this.restSiretMockMvc.perform(
                put(ENTITY_API_URL_ID, siretDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isOk());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeUpdate);
        Siret testSiret = siretList.get(siretList.size() - 1);
        assertThat(testSiret.getSiret()).isEqualTo(UPDATED_SIRET);
        assertThat(testSiret.getStatus()).isEqualTo(UPDATED_STATUS);
        assertThat(testSiret.getEstablishmentType()).isEqualTo(UPDATED_ESTABLISHMENT_TYPE);
        assertThat(testSiret.getDenomination()).isEqualTo(UPDATED_DENOMINATION);
        assertThat(testSiret.getAddressLine1()).isEqualTo(UPDATED_ADDRESS_LINE_1);
        assertThat(testSiret.getAddressLine2()).isEqualTo(UPDATED_ADDRESS_LINE_2);
        assertThat(testSiret.getAddressLine3()).isEqualTo(UPDATED_ADDRESS_LINE_3);
        assertThat(testSiret.getAddressCity()).isEqualTo(UPDATED_ADDRESS_CITY);
        assertThat(testSiret.getAddressCountrySubdivision()).isEqualTo(UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
        assertThat(testSiret.getAddressCountryCode()).isEqualTo(UPDATED_ADDRESS_COUNTRY_CODE);
        assertThat(testSiret.getAddressCountryLabel()).isEqualTo(UPDATED_ADDRESS_COUNTRY_LABEL);
        assertThat(testSiret.getB2gMoa()).isEqualTo(UPDATED_B_2_G_MOA);
        assertThat(testSiret.getB2gMoaOnly()).isEqualTo(UPDATED_B_2_G_MOA_ONLY);
        assertThat(testSiret.getB2gPaymentStatusManagement()).isEqualTo(UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT);
        assertThat(testSiret.getB2gLegalEngagementManagement()).isEqualTo(UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT);
        assertThat(testSiret.getB2gLegalOrServiceEngagementManagement()).isEqualTo(UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT);
        assertThat(testSiret.getB2gServiceCodeManagement()).isEqualTo(UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT);
        assertThat(testSiret.getDiffusible()).isEqualTo(UPDATED_DIFFUSIBLE);
        assertThat(testSiret.getAddressPostalCode()).isEqualTo(UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void putNonExistingSiret() throws Exception {
        int databaseSizeBeforeUpdate = this.siretRepository.findAll().size();
        this.siret.setInstanceID(count.incrementAndGet());

        // Create the Siret
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restSiretMockMvc.perform(
                put(ENTITY_API_URL_ID, siretDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchSiret() throws Exception {
        int databaseSizeBeforeUpdate = this.siretRepository.findAll().size();
        this.siret.setInstanceID(count.incrementAndGet());

        // Create the Siret
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restSiretMockMvc.perform(
                put(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamSiret() throws Exception {
        int databaseSizeBeforeUpdate = this.siretRepository.findAll().size();
        this.siret.setInstanceID(count.incrementAndGet());

        // Create the Siret
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restSiretMockMvc.perform(
                put(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateSiretWithPatch() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        int databaseSizeBeforeUpdate = this.siretRepository.findAll().size();

        // Update the siret using partial update
        Siret partialUpdatedSiret = new Siret();
        partialUpdatedSiret.setInstanceID(this.siret.getInstanceID());

        partialUpdatedSiret
            .siret(UPDATED_SIRET)
            .establishmentType(UPDATED_ESTABLISHMENT_TYPE)
            .addressLine2(UPDATED_ADDRESS_LINE_2)
            .addressCountryCode(UPDATED_ADDRESS_COUNTRY_CODE)
            .b2gMoaOnly(UPDATED_B_2_G_MOA_ONLY)
            .b2gLegalEngagementManagement(UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT)
            .diffusible(UPDATED_DIFFUSIBLE);

        this.restSiretMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedSiret.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedSiret))
            )
            .andExpect(status().isOk());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeUpdate);
        Siret testSiret = siretList.get(siretList.size() - 1);
        assertThat(testSiret.getSiret()).isEqualTo(UPDATED_SIRET);
        assertThat(testSiret.getStatus()).isEqualTo(DEFAULT_STATUS);
        assertThat(testSiret.getEstablishmentType()).isEqualTo(UPDATED_ESTABLISHMENT_TYPE);
        assertThat(testSiret.getDenomination()).isEqualTo(DEFAULT_DENOMINATION);
        assertThat(testSiret.getAddressLine1()).isEqualTo(DEFAULT_ADDRESS_LINE_1);
        assertThat(testSiret.getAddressLine2()).isEqualTo(UPDATED_ADDRESS_LINE_2);
        assertThat(testSiret.getAddressLine3()).isEqualTo(DEFAULT_ADDRESS_LINE_3);
        assertThat(testSiret.getAddressCity()).isEqualTo(DEFAULT_ADDRESS_CITY);
        assertThat(testSiret.getAddressCountrySubdivision()).isEqualTo(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION);
        assertThat(testSiret.getAddressCountryCode()).isEqualTo(UPDATED_ADDRESS_COUNTRY_CODE);
        assertThat(testSiret.getAddressCountryLabel()).isEqualTo(DEFAULT_ADDRESS_COUNTRY_LABEL);
        assertThat(testSiret.getB2gMoa()).isEqualTo(DEFAULT_B_2_G_MOA);
        assertThat(testSiret.getB2gMoaOnly()).isEqualTo(UPDATED_B_2_G_MOA_ONLY);
        assertThat(testSiret.getB2gPaymentStatusManagement()).isEqualTo(DEFAULT_B_2_G_PAYMENT_STATUS_MANAGEMENT);
        assertThat(testSiret.getB2gLegalEngagementManagement()).isEqualTo(UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT);
        assertThat(testSiret.getB2gLegalOrServiceEngagementManagement()).isEqualTo(DEFAULT_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT);
        assertThat(testSiret.getB2gServiceCodeManagement()).isEqualTo(DEFAULT_B_2_G_SERVICE_CODE_MANAGEMENT);
        assertThat(testSiret.getDiffusible()).isEqualTo(UPDATED_DIFFUSIBLE);
        assertThat(testSiret.getAddressPostalCode()).isEqualTo(DEFAULT_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void fullUpdateSiretWithPatch() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        int databaseSizeBeforeUpdate = this.siretRepository.findAll().size();

        // Update the siret using partial update
        Siret partialUpdatedSiret = new Siret();
        partialUpdatedSiret.setInstanceID(this.siret.getInstanceID());

        partialUpdatedSiret
            .siret(UPDATED_SIRET)
            .status(UPDATED_STATUS)
            .establishmentType(UPDATED_ESTABLISHMENT_TYPE)
            .denomination(UPDATED_DENOMINATION)
            .addressLine1(UPDATED_ADDRESS_LINE_1)
            .addressLine2(UPDATED_ADDRESS_LINE_2)
            .addressLine3(UPDATED_ADDRESS_LINE_3)
            .addressCity(UPDATED_ADDRESS_CITY)
            .addressCountrySubdivision(UPDATED_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryCode(UPDATED_ADDRESS_COUNTRY_CODE)
            .addressCountryLabel(UPDATED_ADDRESS_COUNTRY_LABEL)
            .b2gMoa(UPDATED_B_2_G_MOA)
            .b2gMoaOnly(UPDATED_B_2_G_MOA_ONLY)
            .b2gPaymentStatusManagement(UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT)
            .b2gLegalEngagementManagement(UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT)
            .b2gLegalOrServiceEngagementManagement(UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT)
            .b2gServiceCodeManagement(UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT)
            .diffusible(UPDATED_DIFFUSIBLE)
            .addressPostalCode(UPDATED_ADDRESS_POSTAL_CODE);

        this.restSiretMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedSiret.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedSiret))
            )
            .andExpect(status().isOk());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeUpdate);
        Siret testSiret = siretList.get(siretList.size() - 1);
        assertThat(testSiret.getSiret()).isEqualTo(UPDATED_SIRET);
        assertThat(testSiret.getStatus()).isEqualTo(UPDATED_STATUS);
        assertThat(testSiret.getEstablishmentType()).isEqualTo(UPDATED_ESTABLISHMENT_TYPE);
        assertThat(testSiret.getDenomination()).isEqualTo(UPDATED_DENOMINATION);
        assertThat(testSiret.getAddressLine1()).isEqualTo(UPDATED_ADDRESS_LINE_1);
        assertThat(testSiret.getAddressLine2()).isEqualTo(UPDATED_ADDRESS_LINE_2);
        assertThat(testSiret.getAddressLine3()).isEqualTo(UPDATED_ADDRESS_LINE_3);
        assertThat(testSiret.getAddressCity()).isEqualTo(UPDATED_ADDRESS_CITY);
        assertThat(testSiret.getAddressCountrySubdivision()).isEqualTo(UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
        assertThat(testSiret.getAddressCountryCode()).isEqualTo(UPDATED_ADDRESS_COUNTRY_CODE);
        assertThat(testSiret.getAddressCountryLabel()).isEqualTo(UPDATED_ADDRESS_COUNTRY_LABEL);
        assertThat(testSiret.getB2gMoa()).isEqualTo(UPDATED_B_2_G_MOA);
        assertThat(testSiret.getB2gMoaOnly()).isEqualTo(UPDATED_B_2_G_MOA_ONLY);
        assertThat(testSiret.getB2gPaymentStatusManagement()).isEqualTo(UPDATED_B_2_G_PAYMENT_STATUS_MANAGEMENT);
        assertThat(testSiret.getB2gLegalEngagementManagement()).isEqualTo(UPDATED_B_2_G_LEGAL_ENGAGEMENT_MANAGEMENT);
        assertThat(testSiret.getB2gLegalOrServiceEngagementManagement()).isEqualTo(UPDATED_B_2_G_LEGAL_OR_SERVICE_ENGAGEMENT_MANAGEMENT);
        assertThat(testSiret.getB2gServiceCodeManagement()).isEqualTo(UPDATED_B_2_G_SERVICE_CODE_MANAGEMENT);
        assertThat(testSiret.getDiffusible()).isEqualTo(UPDATED_DIFFUSIBLE);
        assertThat(testSiret.getAddressPostalCode()).isEqualTo(UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void patchNonExistingSiret() throws Exception {
        int databaseSizeBeforeUpdate = this.siretRepository.findAll().size();
        this.siret.setInstanceID(count.incrementAndGet());

        // Create the Siret
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restSiretMockMvc.perform(
                patch(ENTITY_API_URL_ID, siretDTO.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchSiret() throws Exception {
        int databaseSizeBeforeUpdate = this.siretRepository.findAll().size();
        this.siret.setInstanceID(count.incrementAndGet());

        // Create the Siret
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restSiretMockMvc.perform(
                patch(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamSiret() throws Exception {
        int databaseSizeBeforeUpdate = this.siretRepository.findAll().size();
        this.siret.setInstanceID(count.incrementAndGet());

        // Create the Siret
        SiretDTO siretDTO = this.siretMapper.toDto(this.siret);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restSiretMockMvc.perform(
                patch(ENTITY_API_URL)
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(siretDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the Siret in the database
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteSiret() throws Exception {
        // Initialize the database
        this.siretRepository.saveAndFlush(this.siret);

        int databaseSizeBeforeDelete = this.siretRepository.findAll().size();

        // Delete the siret
        this.restSiretMockMvc.perform(delete(ENTITY_API_URL_ID, this.siret.getInstanceID()).with(csrf()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        List<Siret> siretList = this.siretRepository.findAll();
        assertThat(siretList).hasSize(databaseSizeBeforeDelete - 1);
    }
}
