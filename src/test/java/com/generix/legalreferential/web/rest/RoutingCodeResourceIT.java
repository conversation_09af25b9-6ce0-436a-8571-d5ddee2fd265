package com.generix.legalreferential.web.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.AddressLine;
import com.generix.legalreferential.domain.RoutingCode;
import com.generix.legalreferential.domain.Siren;
import com.generix.legalreferential.domain.Siret;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.repository.RoutingCodeRepository;
import com.generix.legalreferential.security.SecurityUtils;
import com.generix.legalreferential.service.dto.RoutingCodeDTO;
import com.generix.legalreferential.service.mapper.RoutingCodeMapper;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link RoutingCodeResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_ADMIN" })
public class RoutingCodeResourceIT {

    private static final String DEFAULT_ROUTING_CODE_ID = "AAAAAAAAAA";
    private static final String UPDATED_ROUTING_CODE_ID = "BBBBBBBBBB";

    private static final Statut DEFAULT_STATUS = Statut.A;
    private static final Statut UPDATED_STATUS = Statut.I;

    private static final String DEFAULT_ROUTING_CODE_LABEL = "AAAAAAAAAA";
    private static final String UPDATED_ROUTING_CODE_LABEL = "BBBBBBBBBB";

    private static final String DEFAULT_ROUTING_CODE_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_ROUTING_CODE_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_ADMINISTRATIVE_STATUS = "AAAAAAAAAA";
    private static final String UPDATED_ADMINISTRATIVE_STATUS = "BBBBBBBBBB";

    private static final Boolean DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT = false;
    private static final Boolean UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT = true;

    private static final String DEFAULT_ADDRESS_LINE_1 = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_LINE_1 = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_LINE_2 = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_LINE_2 = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_LINE_3 = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_LINE_3 = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_POSTAL_CODE = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_POSTAL_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_CITY = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_CITY = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_COUNTRY_SUBDIVISION = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_COUNTRY_SUBDIVISION = "BBBBBBBBBB";

    private static final String DEFAULT_ISSUER = "https://auth.staging.apps.generix.biz/auth/realms/legalreferential2";
    private static final String DEFAULT_CLIENTID = "legalref_client";
    private static final String DEFAULT_OWNER = "legalref_admin";

    private static final String DEFAULT_ADDRESS_COUNTRY_CODE = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_COUNTRY_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_COUNTRY_LABEL = "AA";
    private static final String UPDATED_ADDRESS_COUNTRY_LABEL = "BB";

    private static final String ENTITY_API_URL = "/api/routing-codes";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong count = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private RoutingCodeRepository routingCodeRepository;

    @Autowired
    private RoutingCodeMapper routingCodeMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restRoutingCodeMockMvc;

    private RoutingCode routingCode;

    public static MockedStatic<SecurityUtils> mockedStatic;

    /**
     * Create an entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it, if they test an entity
     * which requires the current entity.
     */
    public static RoutingCode createEntity() {
        RoutingCode routingCode = new RoutingCode()
            .routingCodeID(DEFAULT_ROUTING_CODE_ID)
            .status(DEFAULT_STATUS)
            .routingCodeLabel(DEFAULT_ROUTING_CODE_LABEL)
            .routingCodeType(DEFAULT_ROUTING_CODE_TYPE)
            .administrativeStatus(DEFAULT_ADMINISTRATIVE_STATUS)
            .legalEngagementManagement(DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT)
            .addressLine1(DEFAULT_ADDRESS_LINE_1)
            .addressLine2(DEFAULT_ADDRESS_LINE_2)
            .addressLine3(DEFAULT_ADDRESS_LINE_3)
            .addressPostalCode(DEFAULT_ADDRESS_POSTAL_CODE)
            .addressCity(DEFAULT_ADDRESS_CITY)
            .addressCountrySubdivision(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryCode(DEFAULT_ADDRESS_COUNTRY_CODE)
            .addressCountryLabel(DEFAULT_ADDRESS_COUNTRY_LABEL);
        routingCode.setIssuer(RoutingCodeResourceIT.DEFAULT_ISSUER);
        routingCode.setClientID(RoutingCodeResourceIT.DEFAULT_CLIENTID);
        routingCode.setOwner(RoutingCodeResourceIT.DEFAULT_OWNER);
        routingCode.setInstanceID(100L);
        return routingCode;
    }

    /**
     * Create an updated entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it, if they test an entity
     * which requires the current entity.
     */
    public static RoutingCode createUpdatedEntity(EntityManager em) {
        RoutingCode routingCode = new RoutingCode()
            .routingCodeID(UPDATED_ROUTING_CODE_ID)
            .status(UPDATED_STATUS)
            .routingCodeLabel(UPDATED_ROUTING_CODE_LABEL)
            .routingCodeType(UPDATED_ROUTING_CODE_TYPE)
            .administrativeStatus(UPDATED_ADMINISTRATIVE_STATUS)
            .legalEngagementManagement(UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT)
            .addressLine1(UPDATED_ADDRESS_LINE_1)
            .addressLine2(UPDATED_ADDRESS_LINE_2)
            .addressLine3(UPDATED_ADDRESS_LINE_3)
            .addressPostalCode(UPDATED_ADDRESS_POSTAL_CODE)
            .addressCity(UPDATED_ADDRESS_CITY)
            .addressCountrySubdivision(UPDATED_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryCode(UPDATED_ADDRESS_COUNTRY_CODE)
            .addressCountryLabel(UPDATED_ADDRESS_COUNTRY_LABEL);
        return routingCode;
    }

    @AfterAll
    public static void teardown() {
        mockedStatic.close();
    }

    @BeforeAll
    public static void initForAllTest() {
        initStaticSecurityUtils();
    }

    private static void initStaticSecurityUtils() {
        mockedStatic = Mockito.mockStatic(SecurityUtils.class);

        mockedStatic
            .when(() -> SecurityUtils.getIssuerCurrentUser())
            .thenReturn("https://auth.staging.apps.generix.biz/auth/realms/legalreferential2");
        mockedStatic.when(() -> SecurityUtils.getClientIDCurrentUser()).thenReturn("client_app");
        mockedStatic.when(() -> SecurityUtils.getPreferredUsernameCurrentUser()).thenReturn("admin");
    }

    @BeforeEach
    public void initTest() {
        this.routingCode = createEntity();
    }

    @Test
    @Transactional
    void createRoutingCode() throws Exception {
        int databaseSizeBeforeCreate = this.routingCodeRepository.findAll().size();
        // Create the RoutingCode
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);
        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isCreated());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeCreate + 1);
        RoutingCode testRoutingCode = routingCodeList.get(routingCodeList.size() - 1);
        assertThat(testRoutingCode.getRoutingCodeID()).isEqualTo(DEFAULT_ROUTING_CODE_ID);
        assertThat(testRoutingCode.getStatus()).isEqualTo(DEFAULT_STATUS);
        assertThat(testRoutingCode.getRoutingCodeLabel()).isEqualTo(DEFAULT_ROUTING_CODE_LABEL);
        assertThat(testRoutingCode.getRoutingCodeType()).isEqualTo(DEFAULT_ROUTING_CODE_TYPE);
        assertThat(testRoutingCode.getAdministrativeStatus()).isEqualTo(DEFAULT_ADMINISTRATIVE_STATUS);
        assertThat(testRoutingCode.getLegalEngagementManagement()).isEqualTo(DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT);
        assertThat(testRoutingCode.getAddressLine1()).isEqualTo(DEFAULT_ADDRESS_LINE_1);
        assertThat(testRoutingCode.getAddressLine2()).isEqualTo(DEFAULT_ADDRESS_LINE_2);
        assertThat(testRoutingCode.getAddressLine3()).isEqualTo(DEFAULT_ADDRESS_LINE_3);
        assertThat(testRoutingCode.getAddressPostalCode()).isEqualTo(DEFAULT_ADDRESS_POSTAL_CODE);
        assertThat(testRoutingCode.getAddressCity()).isEqualTo(DEFAULT_ADDRESS_CITY);
        assertThat(testRoutingCode.getAddressCountrySubdivision()).isEqualTo(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION);
        assertThat(testRoutingCode.getAddressCountryCode()).isEqualTo(DEFAULT_ADDRESS_COUNTRY_CODE);
        assertThat(testRoutingCode.getAddressCountryLabel()).isEqualTo(DEFAULT_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void createRoutingCodeWithExistingId() throws Exception {
        // Create the RoutingCode with an existing ID
        this.routingCode.setInstanceID(null);
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        int databaseSizeBeforeCreate = this.routingCodeRepository.findAll().size();

        // An entity with an existing ID cannot be created, so this API call must fail
        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkRoutingCodeIDIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setRoutingCodeID(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setStatus(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRoutingCodeLabelIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setRoutingCodeLabel(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRoutingCodeTypeIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setRoutingCodeType(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAdministrativeStatusIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setAdministrativeStatus(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkLegalEngagementManagementIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setLegalEngagementManagement(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressLine1IsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setAddressLine1(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressLine2IsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setAddressLine2(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressLine3IsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setAddressLine3(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressPostalCodeIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setAddressPostalCode(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressCityIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setAddressCity(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressCountrySubdivisionIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setAddressCountrySubdivision(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAddressCountryCodeIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.routingCodeRepository.findAll().size();
        // set the field null
        this.routingCode.setAddressCountryCode(null);

        // Create the RoutingCode, which fails.
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        this.restRoutingCodeMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllRoutingCodes() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList
        this.restRoutingCodeMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.routingCode.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].routingCodeID").value(hasItem(DEFAULT_ROUTING_CODE_ID)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].routingCodeLabel").value(hasItem(DEFAULT_ROUTING_CODE_LABEL)))
            .andExpect(jsonPath("$.[*].routingCodeType").value(hasItem(DEFAULT_ROUTING_CODE_TYPE)))
            .andExpect(jsonPath("$.[*].administrativeStatus").value(hasItem(DEFAULT_ADMINISTRATIVE_STATUS)))
            .andExpect(jsonPath("$.[*].legalEngagementManagement").value(hasItem(DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT.booleanValue())))
            .andExpect(jsonPath("$.[*].addressLine1").value(hasItem(DEFAULT_ADDRESS_LINE_1)))
            .andExpect(jsonPath("$.[*].addressLine2").value(hasItem(DEFAULT_ADDRESS_LINE_2)))
            .andExpect(jsonPath("$.[*].addressLine3").value(hasItem(DEFAULT_ADDRESS_LINE_3)))
            .andExpect(jsonPath("$.[*].addressPostalCode").value(hasItem(DEFAULT_ADDRESS_POSTAL_CODE)))
            .andExpect(jsonPath("$.[*].addressCity").value(hasItem(DEFAULT_ADDRESS_CITY)))
            .andExpect(jsonPath("$.[*].addressCountrySubdivision").value(hasItem(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION)))
            .andExpect(jsonPath("$.[*].addressCountryCode").value(hasItem(DEFAULT_ADDRESS_COUNTRY_CODE)))
            .andExpect(jsonPath("$.[*].addressCountryLabel").value(hasItem(DEFAULT_ADDRESS_COUNTRY_LABEL)));
    }

    @Test
    @Transactional
    void getRoutingCode() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get the routingCode
        this.restRoutingCodeMockMvc.perform(get(ENTITY_API_URL_ID, this.routingCode.getInstanceID()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.instanceID").value(this.routingCode.getInstanceID().intValue()))
            .andExpect(jsonPath("$.routingCodeID").value(DEFAULT_ROUTING_CODE_ID))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.routingCodeLabel").value(DEFAULT_ROUTING_CODE_LABEL))
            .andExpect(jsonPath("$.routingCodeType").value(DEFAULT_ROUTING_CODE_TYPE))
            .andExpect(jsonPath("$.administrativeStatus").value(DEFAULT_ADMINISTRATIVE_STATUS))
            .andExpect(jsonPath("$.legalEngagementManagement").value(DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT.booleanValue()))
            .andExpect(jsonPath("$.addressLine1").value(DEFAULT_ADDRESS_LINE_1))
            .andExpect(jsonPath("$.addressLine2").value(DEFAULT_ADDRESS_LINE_2))
            .andExpect(jsonPath("$.addressLine3").value(DEFAULT_ADDRESS_LINE_3))
            .andExpect(jsonPath("$.addressPostalCode").value(DEFAULT_ADDRESS_POSTAL_CODE))
            .andExpect(jsonPath("$.addressCity").value(DEFAULT_ADDRESS_CITY))
            .andExpect(jsonPath("$.addressCountrySubdivision").value(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION))
            .andExpect(jsonPath("$.addressCountryCode").value(DEFAULT_ADDRESS_COUNTRY_CODE))
            .andExpect(jsonPath("$.addressCountryLabel").value(DEFAULT_ADDRESS_COUNTRY_LABEL));
    }

    @Test
    @Transactional
    void getRoutingCodesByIdFiltering() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        Long id = this.routingCode.getInstanceID();

        defaultRoutingCodeShouldBeFound("instanceID.equals=" + id);
        defaultRoutingCodeShouldNotBeFound("instanceID.notEquals=" + id);

        defaultRoutingCodeShouldBeFound("instanceID.greaterThanOrEqual=" + id);
        defaultRoutingCodeShouldNotBeFound("instanceID.greaterThan=" + id);

        defaultRoutingCodeShouldBeFound("instanceID.lessThanOrEqual=" + id);
        defaultRoutingCodeShouldNotBeFound("instanceID.lessThan=" + id);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeIDIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeID equals to DEFAULT_ROUTING_CODE_ID
        defaultRoutingCodeShouldBeFound("routingCodeID.equals=" + DEFAULT_ROUTING_CODE_ID);

        // Get all the routingCodeList where routingCodeID equals to UPDATED_ROUTING_CODE_ID
        defaultRoutingCodeShouldNotBeFound("routingCodeID.equals=" + UPDATED_ROUTING_CODE_ID);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeIDIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeID in DEFAULT_ROUTING_CODE_ID or UPDATED_ROUTING_CODE_ID
        defaultRoutingCodeShouldBeFound("routingCodeID.in=" + DEFAULT_ROUTING_CODE_ID + "," + UPDATED_ROUTING_CODE_ID);

        // Get all the routingCodeList where routingCodeID equals to UPDATED_ROUTING_CODE_ID
        defaultRoutingCodeShouldNotBeFound("routingCodeID.in=" + UPDATED_ROUTING_CODE_ID);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeIDIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeID is not null
        defaultRoutingCodeShouldBeFound("routingCodeID.specified=true");

        // Get all the routingCodeList where routingCodeID is null
        defaultRoutingCodeShouldNotBeFound("routingCodeID.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeIDContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeID contains DEFAULT_ROUTING_CODE_ID
        defaultRoutingCodeShouldBeFound("routingCodeID.contains=" + DEFAULT_ROUTING_CODE_ID);

        // Get all the routingCodeList where routingCodeID contains UPDATED_ROUTING_CODE_ID
        defaultRoutingCodeShouldNotBeFound("routingCodeID.contains=" + UPDATED_ROUTING_CODE_ID);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeIDNotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeID does not contain DEFAULT_ROUTING_CODE_ID
        defaultRoutingCodeShouldNotBeFound("routingCodeID.doesNotContain=" + DEFAULT_ROUTING_CODE_ID);

        // Get all the routingCodeList where routingCodeID does not contain UPDATED_ROUTING_CODE_ID
        defaultRoutingCodeShouldBeFound("routingCodeID.doesNotContain=" + UPDATED_ROUTING_CODE_ID);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByStatusIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where status equals to DEFAULT_STATUS
        defaultRoutingCodeShouldBeFound("status.equals=" + DEFAULT_STATUS);

        // Get all the routingCodeList where status equals to UPDATED_STATUS
        defaultRoutingCodeShouldNotBeFound("status.equals=" + UPDATED_STATUS);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByStatusIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where status in DEFAULT_STATUS or UPDATED_STATUS
        defaultRoutingCodeShouldBeFound("status.in=" + DEFAULT_STATUS + "," + UPDATED_STATUS);

        // Get all the routingCodeList where status equals to UPDATED_STATUS
        defaultRoutingCodeShouldNotBeFound("status.in=" + UPDATED_STATUS);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByStatusIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where status is not null
        defaultRoutingCodeShouldBeFound("status.specified=true");

        // Get all the routingCodeList where status is null
        defaultRoutingCodeShouldNotBeFound("status.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeLabelIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeLabel equals to DEFAULT_ROUTING_CODE_LABEL
        defaultRoutingCodeShouldBeFound("routingCodeLabel.equals=" + DEFAULT_ROUTING_CODE_LABEL);

        // Get all the routingCodeList where routingCodeLabel equals to UPDATED_ROUTING_CODE_LABEL
        defaultRoutingCodeShouldNotBeFound("routingCodeLabel.equals=" + UPDATED_ROUTING_CODE_LABEL);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeLabelIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeLabel in DEFAULT_ROUTING_CODE_LABEL or UPDATED_ROUTING_CODE_LABEL
        defaultRoutingCodeShouldBeFound("routingCodeLabel.in=" + DEFAULT_ROUTING_CODE_LABEL + "," + UPDATED_ROUTING_CODE_LABEL);

        // Get all the routingCodeList where routingCodeLabel equals to UPDATED_ROUTING_CODE_LABEL
        defaultRoutingCodeShouldNotBeFound("routingCodeLabel.in=" + UPDATED_ROUTING_CODE_LABEL);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeLabelIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeLabel is not null
        defaultRoutingCodeShouldBeFound("routingCodeLabel.specified=true");

        // Get all the routingCodeList where routingCodeLabel is null
        defaultRoutingCodeShouldNotBeFound("routingCodeLabel.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeLabelContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeLabel contains DEFAULT_ROUTING_CODE_LABEL
        defaultRoutingCodeShouldBeFound("routingCodeLabel.contains=" + DEFAULT_ROUTING_CODE_LABEL);

        // Get all the routingCodeList where routingCodeLabel contains UPDATED_ROUTING_CODE_LABEL
        defaultRoutingCodeShouldNotBeFound("routingCodeLabel.contains=" + UPDATED_ROUTING_CODE_LABEL);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeLabelNotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeLabel does not contain DEFAULT_ROUTING_CODE_LABEL
        defaultRoutingCodeShouldNotBeFound("routingCodeLabel.doesNotContain=" + DEFAULT_ROUTING_CODE_LABEL);

        // Get all the routingCodeList where routingCodeLabel does not contain UPDATED_ROUTING_CODE_LABEL
        defaultRoutingCodeShouldBeFound("routingCodeLabel.doesNotContain=" + UPDATED_ROUTING_CODE_LABEL);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeTypeIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeType equals to DEFAULT_ROUTING_CODE_TYPE
        defaultRoutingCodeShouldBeFound("routingCodeType.equals=" + DEFAULT_ROUTING_CODE_TYPE);

        // Get all the routingCodeList where routingCodeType equals to UPDATED_ROUTING_CODE_TYPE
        defaultRoutingCodeShouldNotBeFound("routingCodeType.equals=" + UPDATED_ROUTING_CODE_TYPE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeTypeIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeType in DEFAULT_ROUTING_CODE_TYPE or UPDATED_ROUTING_CODE_TYPE
        defaultRoutingCodeShouldBeFound("routingCodeType.in=" + DEFAULT_ROUTING_CODE_TYPE + "," + UPDATED_ROUTING_CODE_TYPE);

        // Get all the routingCodeList where routingCodeType equals to UPDATED_ROUTING_CODE_TYPE
        defaultRoutingCodeShouldNotBeFound("routingCodeType.in=" + UPDATED_ROUTING_CODE_TYPE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeTypeIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeType is not null
        defaultRoutingCodeShouldBeFound("routingCodeType.specified=true");

        // Get all the routingCodeList where routingCodeType is null
        defaultRoutingCodeShouldNotBeFound("routingCodeType.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeTypeContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeType contains DEFAULT_ROUTING_CODE_TYPE
        defaultRoutingCodeShouldBeFound("routingCodeType.contains=" + DEFAULT_ROUTING_CODE_TYPE);

        // Get all the routingCodeList where routingCodeType contains UPDATED_ROUTING_CODE_TYPE
        defaultRoutingCodeShouldNotBeFound("routingCodeType.contains=" + UPDATED_ROUTING_CODE_TYPE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByRoutingCodeTypeNotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where routingCodeType does not contain DEFAULT_ROUTING_CODE_TYPE
        defaultRoutingCodeShouldNotBeFound("routingCodeType.doesNotContain=" + DEFAULT_ROUTING_CODE_TYPE);

        // Get all the routingCodeList where routingCodeType does not contain UPDATED_ROUTING_CODE_TYPE
        defaultRoutingCodeShouldBeFound("routingCodeType.doesNotContain=" + UPDATED_ROUTING_CODE_TYPE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAdministrativeStatusIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where administrativeStatus equals to DEFAULT_ADMINISTRATIVE_STATUS
        defaultRoutingCodeShouldBeFound("administrativeStatus.equals=" + DEFAULT_ADMINISTRATIVE_STATUS);

        // Get all the routingCodeList where administrativeStatus equals to UPDATED_ADMINISTRATIVE_STATUS
        defaultRoutingCodeShouldNotBeFound("administrativeStatus.equals=" + UPDATED_ADMINISTRATIVE_STATUS);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAdministrativeStatusIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where administrativeStatus in DEFAULT_ADMINISTRATIVE_STATUS or UPDATED_ADMINISTRATIVE_STATUS
        defaultRoutingCodeShouldBeFound("administrativeStatus.in=" + DEFAULT_ADMINISTRATIVE_STATUS + "," + UPDATED_ADMINISTRATIVE_STATUS);

        // Get all the routingCodeList where administrativeStatus equals to UPDATED_ADMINISTRATIVE_STATUS
        defaultRoutingCodeShouldNotBeFound("administrativeStatus.in=" + UPDATED_ADMINISTRATIVE_STATUS);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAdministrativeStatusIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where administrativeStatus is not null
        defaultRoutingCodeShouldBeFound("administrativeStatus.specified=true");

        // Get all the routingCodeList where administrativeStatus is null
        defaultRoutingCodeShouldNotBeFound("administrativeStatus.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAdministrativeStatusContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where administrativeStatus contains DEFAULT_ADMINISTRATIVE_STATUS
        defaultRoutingCodeShouldBeFound("administrativeStatus.contains=" + DEFAULT_ADMINISTRATIVE_STATUS);

        // Get all the routingCodeList where administrativeStatus contains UPDATED_ADMINISTRATIVE_STATUS
        defaultRoutingCodeShouldNotBeFound("administrativeStatus.contains=" + UPDATED_ADMINISTRATIVE_STATUS);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAdministrativeStatusNotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where administrativeStatus does not contain DEFAULT_ADMINISTRATIVE_STATUS
        defaultRoutingCodeShouldNotBeFound("administrativeStatus.doesNotContain=" + DEFAULT_ADMINISTRATIVE_STATUS);

        // Get all the routingCodeList where administrativeStatus does not contain UPDATED_ADMINISTRATIVE_STATUS
        defaultRoutingCodeShouldBeFound("administrativeStatus.doesNotContain=" + UPDATED_ADMINISTRATIVE_STATUS);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByLegalEngagementManagementIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where legalEngagementManagement equals to DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT
        defaultRoutingCodeShouldBeFound("legalEngagementManagement.equals=" + DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT);

        // Get all the routingCodeList where legalEngagementManagement equals to UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT
        defaultRoutingCodeShouldNotBeFound("legalEngagementManagement.equals=" + UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByLegalEngagementManagementIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where legalEngagementManagement in DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT or UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT
        defaultRoutingCodeShouldBeFound(
            "legalEngagementManagement.in=" + DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT + "," + UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT
        );

        // Get all the routingCodeList where legalEngagementManagement equals to UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT
        defaultRoutingCodeShouldNotBeFound("legalEngagementManagement.in=" + UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByLegalEngagementManagementIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where legalEngagementManagement is not null
        defaultRoutingCodeShouldBeFound("legalEngagementManagement.specified=true");

        // Get all the routingCodeList where legalEngagementManagement is null
        defaultRoutingCodeShouldNotBeFound("legalEngagementManagement.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine1IsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine1 equals to DEFAULT_ADDRESS_LINE_1
        defaultRoutingCodeShouldBeFound("addressLine1.equals=" + DEFAULT_ADDRESS_LINE_1);

        // Get all the routingCodeList where addressLine1 equals to UPDATED_ADDRESS_LINE_1
        defaultRoutingCodeShouldNotBeFound("addressLine1.equals=" + UPDATED_ADDRESS_LINE_1);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine1IsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine1 in DEFAULT_ADDRESS_LINE_1 or UPDATED_ADDRESS_LINE_1
        defaultRoutingCodeShouldBeFound("addressLine1.in=" + DEFAULT_ADDRESS_LINE_1 + "," + UPDATED_ADDRESS_LINE_1);

        // Get all the routingCodeList where addressLine1 equals to UPDATED_ADDRESS_LINE_1
        defaultRoutingCodeShouldNotBeFound("addressLine1.in=" + UPDATED_ADDRESS_LINE_1);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine1IsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine1 is not null
        defaultRoutingCodeShouldBeFound("addressLine1.specified=true");

        // Get all the routingCodeList where addressLine1 is null
        defaultRoutingCodeShouldNotBeFound("addressLine1.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine1ContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine1 contains DEFAULT_ADDRESS_LINE_1
        defaultRoutingCodeShouldBeFound("addressLine1.contains=" + DEFAULT_ADDRESS_LINE_1);

        // Get all the routingCodeList where addressLine1 contains UPDATED_ADDRESS_LINE_1
        defaultRoutingCodeShouldNotBeFound("addressLine1.contains=" + UPDATED_ADDRESS_LINE_1);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine1NotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine1 does not contain DEFAULT_ADDRESS_LINE_1
        defaultRoutingCodeShouldNotBeFound("addressLine1.doesNotContain=" + DEFAULT_ADDRESS_LINE_1);

        // Get all the routingCodeList where addressLine1 does not contain UPDATED_ADDRESS_LINE_1
        defaultRoutingCodeShouldBeFound("addressLine1.doesNotContain=" + UPDATED_ADDRESS_LINE_1);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine2IsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine2 equals to DEFAULT_ADDRESS_LINE_2
        defaultRoutingCodeShouldBeFound("addressLine2.equals=" + DEFAULT_ADDRESS_LINE_2);

        // Get all the routingCodeList where addressLine2 equals to UPDATED_ADDRESS_LINE_2
        defaultRoutingCodeShouldNotBeFound("addressLine2.equals=" + UPDATED_ADDRESS_LINE_2);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine2IsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine2 in DEFAULT_ADDRESS_LINE_2 or UPDATED_ADDRESS_LINE_2
        defaultRoutingCodeShouldBeFound("addressLine2.in=" + DEFAULT_ADDRESS_LINE_2 + "," + UPDATED_ADDRESS_LINE_2);

        // Get all the routingCodeList where addressLine2 equals to UPDATED_ADDRESS_LINE_2
        defaultRoutingCodeShouldNotBeFound("addressLine2.in=" + UPDATED_ADDRESS_LINE_2);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine2IsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine2 is not null
        defaultRoutingCodeShouldBeFound("addressLine2.specified=true");

        // Get all the routingCodeList where addressLine2 is null
        defaultRoutingCodeShouldNotBeFound("addressLine2.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine2ContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine2 contains DEFAULT_ADDRESS_LINE_2
        defaultRoutingCodeShouldBeFound("addressLine2.contains=" + DEFAULT_ADDRESS_LINE_2);

        // Get all the routingCodeList where addressLine2 contains UPDATED_ADDRESS_LINE_2
        defaultRoutingCodeShouldNotBeFound("addressLine2.contains=" + UPDATED_ADDRESS_LINE_2);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine2NotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine2 does not contain DEFAULT_ADDRESS_LINE_2
        defaultRoutingCodeShouldNotBeFound("addressLine2.doesNotContain=" + DEFAULT_ADDRESS_LINE_2);

        // Get all the routingCodeList where addressLine2 does not contain UPDATED_ADDRESS_LINE_2
        defaultRoutingCodeShouldBeFound("addressLine2.doesNotContain=" + UPDATED_ADDRESS_LINE_2);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine3IsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine3 equals to DEFAULT_ADDRESS_LINE_3
        defaultRoutingCodeShouldBeFound("addressLine3.equals=" + DEFAULT_ADDRESS_LINE_3);

        // Get all the routingCodeList where addressLine3 equals to UPDATED_ADDRESS_LINE_3
        defaultRoutingCodeShouldNotBeFound("addressLine3.equals=" + UPDATED_ADDRESS_LINE_3);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine3IsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine3 in DEFAULT_ADDRESS_LINE_3 or UPDATED_ADDRESS_LINE_3
        defaultRoutingCodeShouldBeFound("addressLine3.in=" + DEFAULT_ADDRESS_LINE_3 + "," + UPDATED_ADDRESS_LINE_3);

        // Get all the routingCodeList where addressLine3 equals to UPDATED_ADDRESS_LINE_3
        defaultRoutingCodeShouldNotBeFound("addressLine3.in=" + UPDATED_ADDRESS_LINE_3);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine3IsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine3 is not null
        defaultRoutingCodeShouldBeFound("addressLine3.specified=true");

        // Get all the routingCodeList where addressLine3 is null
        defaultRoutingCodeShouldNotBeFound("addressLine3.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine3ContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine3 contains DEFAULT_ADDRESS_LINE_3
        defaultRoutingCodeShouldBeFound("addressLine3.contains=" + DEFAULT_ADDRESS_LINE_3);

        // Get all the routingCodeList where addressLine3 contains UPDATED_ADDRESS_LINE_3
        defaultRoutingCodeShouldNotBeFound("addressLine3.contains=" + UPDATED_ADDRESS_LINE_3);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressLine3NotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressLine3 does not contain DEFAULT_ADDRESS_LINE_3
        defaultRoutingCodeShouldNotBeFound("addressLine3.doesNotContain=" + DEFAULT_ADDRESS_LINE_3);

        // Get all the routingCodeList where addressLine3 does not contain UPDATED_ADDRESS_LINE_3
        defaultRoutingCodeShouldBeFound("addressLine3.doesNotContain=" + UPDATED_ADDRESS_LINE_3);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressPostalCodeIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressPostalCode equals to DEFAULT_ADDRESS_POSTAL_CODE
        defaultRoutingCodeShouldBeFound("addressPostalCode.equals=" + DEFAULT_ADDRESS_POSTAL_CODE);

        // Get all the routingCodeList where addressPostalCode equals to UPDATED_ADDRESS_POSTAL_CODE
        defaultRoutingCodeShouldNotBeFound("addressPostalCode.equals=" + UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressPostalCodeIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressPostalCode in DEFAULT_ADDRESS_POSTAL_CODE or UPDATED_ADDRESS_POSTAL_CODE
        defaultRoutingCodeShouldBeFound("addressPostalCode.in=" + DEFAULT_ADDRESS_POSTAL_CODE + "," + UPDATED_ADDRESS_POSTAL_CODE);

        // Get all the routingCodeList where addressPostalCode equals to UPDATED_ADDRESS_POSTAL_CODE
        defaultRoutingCodeShouldNotBeFound("addressPostalCode.in=" + UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressPostalCodeIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressPostalCode is not null
        defaultRoutingCodeShouldBeFound("addressPostalCode.specified=true");

        // Get all the routingCodeList where addressPostalCode is null
        defaultRoutingCodeShouldNotBeFound("addressPostalCode.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressPostalCodeContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressPostalCode contains DEFAULT_ADDRESS_POSTAL_CODE
        defaultRoutingCodeShouldBeFound("addressPostalCode.contains=" + DEFAULT_ADDRESS_POSTAL_CODE);

        // Get all the routingCodeList where addressPostalCode contains UPDATED_ADDRESS_POSTAL_CODE
        defaultRoutingCodeShouldNotBeFound("addressPostalCode.contains=" + UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressPostalCodeNotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressPostalCode does not contain DEFAULT_ADDRESS_POSTAL_CODE
        defaultRoutingCodeShouldNotBeFound("addressPostalCode.doesNotContain=" + DEFAULT_ADDRESS_POSTAL_CODE);

        // Get all the routingCodeList where addressPostalCode does not contain UPDATED_ADDRESS_POSTAL_CODE
        defaultRoutingCodeShouldBeFound("addressPostalCode.doesNotContain=" + UPDATED_ADDRESS_POSTAL_CODE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCityIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCity equals to DEFAULT_ADDRESS_CITY
        defaultRoutingCodeShouldBeFound("addressCity.equals=" + DEFAULT_ADDRESS_CITY);

        // Get all the routingCodeList where addressCity equals to UPDATED_ADDRESS_CITY
        defaultRoutingCodeShouldNotBeFound("addressCity.equals=" + UPDATED_ADDRESS_CITY);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCityIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCity in DEFAULT_ADDRESS_CITY or UPDATED_ADDRESS_CITY
        defaultRoutingCodeShouldBeFound("addressCity.in=" + DEFAULT_ADDRESS_CITY + "," + UPDATED_ADDRESS_CITY);

        // Get all the routingCodeList where addressCity equals to UPDATED_ADDRESS_CITY
        defaultRoutingCodeShouldNotBeFound("addressCity.in=" + UPDATED_ADDRESS_CITY);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCityIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCity is not null
        defaultRoutingCodeShouldBeFound("addressCity.specified=true");

        // Get all the routingCodeList where addressCity is null
        defaultRoutingCodeShouldNotBeFound("addressCity.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCityContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCity contains DEFAULT_ADDRESS_CITY
        defaultRoutingCodeShouldBeFound("addressCity.contains=" + DEFAULT_ADDRESS_CITY);

        // Get all the routingCodeList where addressCity contains UPDATED_ADDRESS_CITY
        defaultRoutingCodeShouldNotBeFound("addressCity.contains=" + UPDATED_ADDRESS_CITY);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCityNotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCity does not contain DEFAULT_ADDRESS_CITY
        defaultRoutingCodeShouldNotBeFound("addressCity.doesNotContain=" + DEFAULT_ADDRESS_CITY);

        // Get all the routingCodeList where addressCity does not contain UPDATED_ADDRESS_CITY
        defaultRoutingCodeShouldBeFound("addressCity.doesNotContain=" + UPDATED_ADDRESS_CITY);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountrySubdivisionIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountrySubdivision equals to DEFAULT_ADDRESS_COUNTRY_SUBDIVISION
        defaultRoutingCodeShouldBeFound("addressCountrySubdivision.equals=" + DEFAULT_ADDRESS_COUNTRY_SUBDIVISION);

        // Get all the routingCodeList where addressCountrySubdivision equals to UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultRoutingCodeShouldNotBeFound("addressCountrySubdivision.equals=" + UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountrySubdivisionIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountrySubdivision in DEFAULT_ADDRESS_COUNTRY_SUBDIVISION or UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultRoutingCodeShouldBeFound(
            "addressCountrySubdivision.in=" + DEFAULT_ADDRESS_COUNTRY_SUBDIVISION + "," + UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        );

        // Get all the routingCodeList where addressCountrySubdivision equals to UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultRoutingCodeShouldNotBeFound("addressCountrySubdivision.in=" + UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountrySubdivisionIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountrySubdivision is not null
        defaultRoutingCodeShouldBeFound("addressCountrySubdivision.specified=true");

        // Get all the routingCodeList where addressCountrySubdivision is null
        defaultRoutingCodeShouldNotBeFound("addressCountrySubdivision.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountrySubdivisionContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountrySubdivision contains DEFAULT_ADDRESS_COUNTRY_SUBDIVISION
        defaultRoutingCodeShouldBeFound("addressCountrySubdivision.contains=" + DEFAULT_ADDRESS_COUNTRY_SUBDIVISION);

        // Get all the routingCodeList where addressCountrySubdivision contains UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultRoutingCodeShouldNotBeFound("addressCountrySubdivision.contains=" + UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountrySubdivisionNotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountrySubdivision does not contain DEFAULT_ADDRESS_COUNTRY_SUBDIVISION
        defaultRoutingCodeShouldNotBeFound("addressCountrySubdivision.doesNotContain=" + DEFAULT_ADDRESS_COUNTRY_SUBDIVISION);

        // Get all the routingCodeList where addressCountrySubdivision does not contain UPDATED_ADDRESS_COUNTRY_SUBDIVISION
        defaultRoutingCodeShouldBeFound("addressCountrySubdivision.doesNotContain=" + UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryCodeIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryCode equals to DEFAULT_ADDRESS_COUNTRY_CODE
        defaultRoutingCodeShouldBeFound("addressCountryCode.equals=" + DEFAULT_ADDRESS_COUNTRY_CODE);

        // Get all the routingCodeList where addressCountryCode equals to UPDATED_ADDRESS_COUNTRY_CODE
        defaultRoutingCodeShouldNotBeFound("addressCountryCode.equals=" + UPDATED_ADDRESS_COUNTRY_CODE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryCodeIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryCode in DEFAULT_ADDRESS_COUNTRY_CODE or UPDATED_ADDRESS_COUNTRY_CODE
        defaultRoutingCodeShouldBeFound("addressCountryCode.in=" + DEFAULT_ADDRESS_COUNTRY_CODE + "," + UPDATED_ADDRESS_COUNTRY_CODE);

        // Get all the routingCodeList where addressCountryCode equals to UPDATED_ADDRESS_COUNTRY_CODE
        defaultRoutingCodeShouldNotBeFound("addressCountryCode.in=" + UPDATED_ADDRESS_COUNTRY_CODE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryCodeIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryCode is not null
        defaultRoutingCodeShouldBeFound("addressCountryCode.specified=true");

        // Get all the routingCodeList where addressCountryCode is null
        defaultRoutingCodeShouldNotBeFound("addressCountryCode.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryCodeContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryCode contains DEFAULT_ADDRESS_COUNTRY_CODE
        defaultRoutingCodeShouldBeFound("addressCountryCode.contains=" + DEFAULT_ADDRESS_COUNTRY_CODE);

        // Get all the routingCodeList where addressCountryCode contains UPDATED_ADDRESS_COUNTRY_CODE
        defaultRoutingCodeShouldNotBeFound("addressCountryCode.contains=" + UPDATED_ADDRESS_COUNTRY_CODE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryCodeNotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryCode does not contain DEFAULT_ADDRESS_COUNTRY_CODE
        defaultRoutingCodeShouldNotBeFound("addressCountryCode.doesNotContain=" + DEFAULT_ADDRESS_COUNTRY_CODE);

        // Get all the routingCodeList where addressCountryCode does not contain UPDATED_ADDRESS_COUNTRY_CODE
        defaultRoutingCodeShouldBeFound("addressCountryCode.doesNotContain=" + UPDATED_ADDRESS_COUNTRY_CODE);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryLabelIsEqualToSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryLabel equals to DEFAULT_ADDRESS_COUNTRY_LABEL
        defaultRoutingCodeShouldBeFound("addressCountryLabel.equals=" + DEFAULT_ADDRESS_COUNTRY_LABEL);

        // Get all the routingCodeList where addressCountryLabel equals to UPDATED_ADDRESS_COUNTRY_LABEL
        defaultRoutingCodeShouldNotBeFound("addressCountryLabel.equals=" + UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryLabelIsInShouldWork() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryLabel in DEFAULT_ADDRESS_COUNTRY_LABEL or UPDATED_ADDRESS_COUNTRY_LABEL
        defaultRoutingCodeShouldBeFound("addressCountryLabel.in=" + DEFAULT_ADDRESS_COUNTRY_LABEL + "," + UPDATED_ADDRESS_COUNTRY_LABEL);

        // Get all the routingCodeList where addressCountryLabel equals to UPDATED_ADDRESS_COUNTRY_LABEL
        defaultRoutingCodeShouldNotBeFound("addressCountryLabel.in=" + UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryLabelIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryLabel is not null
        defaultRoutingCodeShouldBeFound("addressCountryLabel.specified=true");

        // Get all the routingCodeList where addressCountryLabel is null
        defaultRoutingCodeShouldNotBeFound("addressCountryLabel.specified=false");
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryLabelContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryLabel contains DEFAULT_ADDRESS_COUNTRY_LABEL
        defaultRoutingCodeShouldBeFound("addressCountryLabel.contains=" + DEFAULT_ADDRESS_COUNTRY_LABEL);

        // Get all the routingCodeList where addressCountryLabel contains UPDATED_ADDRESS_COUNTRY_LABEL
        defaultRoutingCodeShouldNotBeFound("addressCountryLabel.contains=" + UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByAddressCountryLabelNotContainsSomething() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        // Get all the routingCodeList where addressCountryLabel does not contain DEFAULT_ADDRESS_COUNTRY_LABEL
        defaultRoutingCodeShouldNotBeFound("addressCountryLabel.doesNotContain=" + DEFAULT_ADDRESS_COUNTRY_LABEL);

        // Get all the routingCodeList where addressCountryLabel does not contain UPDATED_ADDRESS_COUNTRY_LABEL
        defaultRoutingCodeShouldBeFound("addressCountryLabel.doesNotContain=" + UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void getAllRoutingCodesByPkAddressLineIsEqualToSomething() throws Exception {
        AddressLine pkAddressLine;
        if (TestUtil.findAll(this.em, AddressLine.class).isEmpty()) {
            this.routingCodeRepository.saveAndFlush(this.routingCode);
            pkAddressLine = AddressLineResourceIT.createEntity(this.em);
            Siren siren = SirenResourceIT.createEntity(this.em);
            siren.setSiren("AAAAAAAA1");
            siren.setInstanceID(101L);
            this.em.persist(siren);
            Siret siret = SiretResourceIT.createEntity(this.em);
            siret.setSiret("AAAAAAAA2");
            siret.setInstanceID(101L);
            siret.setFkSiretSiren(siren);
            this.em.persist(siret);
            pkAddressLine.setFkAddressLineSiret(siret);
        } else {
            pkAddressLine = TestUtil.findAll(this.em, AddressLine.class).get(0);
        }
        this.em.persist(pkAddressLine);
        this.em.flush();
        pkAddressLine.setFkAddressLineSiret(null);
        this.routingCode.addPkAddressLine(pkAddressLine);
        this.routingCodeRepository.saveAndFlush(this.routingCode);
        Long pkAddressLineId = pkAddressLine.getInstanceID();

        // Get all the routingCodeList where pkAddressLine equals to pkAddressLineId
        defaultRoutingCodeShouldBeFound("pkAddressLineId.equals=" + pkAddressLineId);

        // Get all the routingCodeList where pkAddressLine equals to (pkAddressLineId + 1)
        defaultRoutingCodeShouldNotBeFound("pkAddressLineId.equals=" + (pkAddressLineId + 1));
    }

    @Test
    @Transactional
    void getAllRoutingCodesByFkRoutingCodeSiretIsEqualToSomething() throws Exception {
        Siret fkRoutingCodeSiret;
        if (TestUtil.findAll(this.em, Siret.class).isEmpty()) {
            this.routingCodeRepository.saveAndFlush(this.routingCode);
            fkRoutingCodeSiret = SiretResourceIT.createEntity(this.em);
        } else {
            fkRoutingCodeSiret = TestUtil.findAll(this.em, Siret.class).get(0);
        }
        this.em.persist(fkRoutingCodeSiret);
        this.em.flush();
        this.routingCode.setFkRoutingCodeSiret(fkRoutingCodeSiret);
        this.routingCodeRepository.saveAndFlush(this.routingCode);
        Long fkRoutingCodeSiretId = fkRoutingCodeSiret.getInstanceID();

        // Get all the routingCodeList where fkRoutingCodeSiret equals to fkRoutingCodeSiretId
        defaultRoutingCodeShouldBeFound("fkRoutingCodeSiretId.equals=" + fkRoutingCodeSiretId);

        // Get all the routingCodeList where fkRoutingCodeSiret equals to (fkRoutingCodeSiretId + 1)
        defaultRoutingCodeShouldNotBeFound("fkRoutingCodeSiretId.equals=" + (fkRoutingCodeSiretId + 1));
    }

    /**
     * Executes the search, and checks that the default entity is returned.
     */
    private void defaultRoutingCodeShouldBeFound(String filter) throws Exception {
        this.restRoutingCodeMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.routingCode.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].routingCodeID").value(hasItem(DEFAULT_ROUTING_CODE_ID)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].routingCodeLabel").value(hasItem(DEFAULT_ROUTING_CODE_LABEL)))
            .andExpect(jsonPath("$.[*].routingCodeType").value(hasItem(DEFAULT_ROUTING_CODE_TYPE)))
            .andExpect(jsonPath("$.[*].administrativeStatus").value(hasItem(DEFAULT_ADMINISTRATIVE_STATUS)))
            .andExpect(jsonPath("$.[*].legalEngagementManagement").value(hasItem(DEFAULT_LEGAL_ENGAGEMENT_MANAGEMENT.booleanValue())))
            .andExpect(jsonPath("$.[*].addressLine1").value(hasItem(DEFAULT_ADDRESS_LINE_1)))
            .andExpect(jsonPath("$.[*].addressLine2").value(hasItem(DEFAULT_ADDRESS_LINE_2)))
            .andExpect(jsonPath("$.[*].addressLine3").value(hasItem(DEFAULT_ADDRESS_LINE_3)))
            .andExpect(jsonPath("$.[*].addressPostalCode").value(hasItem(DEFAULT_ADDRESS_POSTAL_CODE)))
            .andExpect(jsonPath("$.[*].addressCity").value(hasItem(DEFAULT_ADDRESS_CITY)))
            .andExpect(jsonPath("$.[*].addressCountrySubdivision").value(hasItem(DEFAULT_ADDRESS_COUNTRY_SUBDIVISION)))
            .andExpect(jsonPath("$.[*].addressCountryCode").value(hasItem(DEFAULT_ADDRESS_COUNTRY_CODE)))
            .andExpect(jsonPath("$.[*].addressCountryLabel").value(hasItem(DEFAULT_ADDRESS_COUNTRY_LABEL)));

        // Check, that the count call also returns 1
        this.restRoutingCodeMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("1"));
    }

    /**
     * Executes the search, and checks that the default entity is not returned.
     */
    private void defaultRoutingCodeShouldNotBeFound(String filter) throws Exception {
        this.restRoutingCodeMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$").isEmpty());

        // Check, that the count call also returns 0
        this.restRoutingCodeMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("0"));
    }

    @Test
    @Transactional
    void getNonExistingRoutingCode() throws Exception {
        // Get the routingCode
        this.restRoutingCodeMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingRoutingCode() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        int databaseSizeBeforeUpdate = this.routingCodeRepository.findAll().size();

        // Update the routingCode
        RoutingCode updatedRoutingCode = this.routingCodeRepository.findById(this.routingCode.getInstanceID()).get();
        // Disconnect from session so that the updates on updatedRoutingCode are not directly saved in db
        this.em.detach(updatedRoutingCode);
        updatedRoutingCode
            .routingCodeID(UPDATED_ROUTING_CODE_ID)
            .status(UPDATED_STATUS)
            .routingCodeLabel(UPDATED_ROUTING_CODE_LABEL)
            .routingCodeType(UPDATED_ROUTING_CODE_TYPE)
            .administrativeStatus(UPDATED_ADMINISTRATIVE_STATUS)
            .legalEngagementManagement(UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT)
            .addressLine1(UPDATED_ADDRESS_LINE_1)
            .addressLine2(UPDATED_ADDRESS_LINE_2)
            .addressLine3(UPDATED_ADDRESS_LINE_3)
            .addressPostalCode(UPDATED_ADDRESS_POSTAL_CODE)
            .addressCity(UPDATED_ADDRESS_CITY)
            .addressCountrySubdivision(UPDATED_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryCode(UPDATED_ADDRESS_COUNTRY_CODE)
            .addressCountryLabel(UPDATED_ADDRESS_COUNTRY_LABEL);
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(updatedRoutingCode);

        this.restRoutingCodeMockMvc.perform(
                put(ENTITY_API_URL_ID, routingCodeDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isOk());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeUpdate);
        RoutingCode testRoutingCode = routingCodeList.get(routingCodeList.size() - 1);
        assertThat(testRoutingCode.getRoutingCodeID()).isEqualTo(UPDATED_ROUTING_CODE_ID);
        assertThat(testRoutingCode.getStatus()).isEqualTo(UPDATED_STATUS);
        assertThat(testRoutingCode.getRoutingCodeLabel()).isEqualTo(UPDATED_ROUTING_CODE_LABEL);
        assertThat(testRoutingCode.getRoutingCodeType()).isEqualTo(UPDATED_ROUTING_CODE_TYPE);
        assertThat(testRoutingCode.getAdministrativeStatus()).isEqualTo(UPDATED_ADMINISTRATIVE_STATUS);
        assertThat(testRoutingCode.getLegalEngagementManagement()).isEqualTo(UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT);
        assertThat(testRoutingCode.getAddressLine1()).isEqualTo(UPDATED_ADDRESS_LINE_1);
        assertThat(testRoutingCode.getAddressLine2()).isEqualTo(UPDATED_ADDRESS_LINE_2);
        assertThat(testRoutingCode.getAddressLine3()).isEqualTo(UPDATED_ADDRESS_LINE_3);
        assertThat(testRoutingCode.getAddressPostalCode()).isEqualTo(UPDATED_ADDRESS_POSTAL_CODE);
        assertThat(testRoutingCode.getAddressCity()).isEqualTo(UPDATED_ADDRESS_CITY);
        assertThat(testRoutingCode.getAddressCountrySubdivision()).isEqualTo(UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
        assertThat(testRoutingCode.getAddressCountryCode()).isEqualTo(UPDATED_ADDRESS_COUNTRY_CODE);
        assertThat(testRoutingCode.getAddressCountryLabel()).isEqualTo(UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void putNonExistingRoutingCode() throws Exception {
        int databaseSizeBeforeUpdate = this.routingCodeRepository.findAll().size();
        this.routingCode.setInstanceID(count.incrementAndGet());

        // Create the RoutingCode
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restRoutingCodeMockMvc.perform(
                put(ENTITY_API_URL_ID, routingCodeDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchRoutingCode() throws Exception {
        int databaseSizeBeforeUpdate = this.routingCodeRepository.findAll().size();
        this.routingCode.setInstanceID(count.incrementAndGet());

        // Create the RoutingCode
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restRoutingCodeMockMvc.perform(
                put(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamRoutingCode() throws Exception {
        int databaseSizeBeforeUpdate = this.routingCodeRepository.findAll().size();
        this.routingCode.setInstanceID(count.incrementAndGet());

        // Create the RoutingCode
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restRoutingCodeMockMvc.perform(
                put(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateRoutingCodeWithPatch() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        int databaseSizeBeforeUpdate = this.routingCodeRepository.findAll().size();

        // Update the routingCode using partial update
        RoutingCode partialUpdatedRoutingCode = new RoutingCode();
        partialUpdatedRoutingCode.setInstanceID(this.routingCode.getInstanceID());

        partialUpdatedRoutingCode
            .routingCodeID(UPDATED_ROUTING_CODE_ID)
            .routingCodeType(UPDATED_ROUTING_CODE_TYPE)
            .legalEngagementManagement(UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT)
            .addressLine1(UPDATED_ADDRESS_LINE_1)
            .addressLine2(UPDATED_ADDRESS_LINE_2)
            .addressPostalCode(UPDATED_ADDRESS_POSTAL_CODE)
            .addressCountrySubdivision(UPDATED_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryLabel(UPDATED_ADDRESS_COUNTRY_LABEL);

        this.restRoutingCodeMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRoutingCode.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedRoutingCode))
            )
            .andExpect(status().isOk());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeUpdate);
        RoutingCode testRoutingCode = routingCodeList.get(routingCodeList.size() - 1);
        assertThat(testRoutingCode.getRoutingCodeID()).isEqualTo(UPDATED_ROUTING_CODE_ID);
        assertThat(testRoutingCode.getStatus()).isEqualTo(DEFAULT_STATUS);
        assertThat(testRoutingCode.getRoutingCodeLabel()).isEqualTo(DEFAULT_ROUTING_CODE_LABEL);
        assertThat(testRoutingCode.getRoutingCodeType()).isEqualTo(UPDATED_ROUTING_CODE_TYPE);
        assertThat(testRoutingCode.getAdministrativeStatus()).isEqualTo(DEFAULT_ADMINISTRATIVE_STATUS);
        assertThat(testRoutingCode.getLegalEngagementManagement()).isEqualTo(UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT);
        assertThat(testRoutingCode.getAddressLine1()).isEqualTo(UPDATED_ADDRESS_LINE_1);
        assertThat(testRoutingCode.getAddressLine2()).isEqualTo(UPDATED_ADDRESS_LINE_2);
        assertThat(testRoutingCode.getAddressLine3()).isEqualTo(DEFAULT_ADDRESS_LINE_3);
        assertThat(testRoutingCode.getAddressPostalCode()).isEqualTo(UPDATED_ADDRESS_POSTAL_CODE);
        assertThat(testRoutingCode.getAddressCity()).isEqualTo(DEFAULT_ADDRESS_CITY);
        assertThat(testRoutingCode.getAddressCountrySubdivision()).isEqualTo(UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
        assertThat(testRoutingCode.getAddressCountryCode()).isEqualTo(DEFAULT_ADDRESS_COUNTRY_CODE);
        assertThat(testRoutingCode.getAddressCountryLabel()).isEqualTo(UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void fullUpdateRoutingCodeWithPatch() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        int databaseSizeBeforeUpdate = this.routingCodeRepository.findAll().size();

        // Update the routingCode using partial update
        RoutingCode partialUpdatedRoutingCode = new RoutingCode();
        partialUpdatedRoutingCode.setInstanceID(this.routingCode.getInstanceID());

        partialUpdatedRoutingCode
            .routingCodeID(UPDATED_ROUTING_CODE_ID)
            .status(UPDATED_STATUS)
            .routingCodeLabel(UPDATED_ROUTING_CODE_LABEL)
            .routingCodeType(UPDATED_ROUTING_CODE_TYPE)
            .administrativeStatus(UPDATED_ADMINISTRATIVE_STATUS)
            .legalEngagementManagement(UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT)
            .addressLine1(UPDATED_ADDRESS_LINE_1)
            .addressLine2(UPDATED_ADDRESS_LINE_2)
            .addressLine3(UPDATED_ADDRESS_LINE_3)
            .addressPostalCode(UPDATED_ADDRESS_POSTAL_CODE)
            .addressCity(UPDATED_ADDRESS_CITY)
            .addressCountrySubdivision(UPDATED_ADDRESS_COUNTRY_SUBDIVISION)
            .addressCountryCode(UPDATED_ADDRESS_COUNTRY_CODE)
            .addressCountryLabel(UPDATED_ADDRESS_COUNTRY_LABEL);

        this.restRoutingCodeMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRoutingCode.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedRoutingCode))
            )
            .andExpect(status().isOk());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeUpdate);
        RoutingCode testRoutingCode = routingCodeList.get(routingCodeList.size() - 1);
        assertThat(testRoutingCode.getRoutingCodeID()).isEqualTo(UPDATED_ROUTING_CODE_ID);
        assertThat(testRoutingCode.getStatus()).isEqualTo(UPDATED_STATUS);
        assertThat(testRoutingCode.getRoutingCodeLabel()).isEqualTo(UPDATED_ROUTING_CODE_LABEL);
        assertThat(testRoutingCode.getRoutingCodeType()).isEqualTo(UPDATED_ROUTING_CODE_TYPE);
        assertThat(testRoutingCode.getAdministrativeStatus()).isEqualTo(UPDATED_ADMINISTRATIVE_STATUS);
        assertThat(testRoutingCode.getLegalEngagementManagement()).isEqualTo(UPDATED_LEGAL_ENGAGEMENT_MANAGEMENT);
        assertThat(testRoutingCode.getAddressLine1()).isEqualTo(UPDATED_ADDRESS_LINE_1);
        assertThat(testRoutingCode.getAddressLine2()).isEqualTo(UPDATED_ADDRESS_LINE_2);
        assertThat(testRoutingCode.getAddressLine3()).isEqualTo(UPDATED_ADDRESS_LINE_3);
        assertThat(testRoutingCode.getAddressPostalCode()).isEqualTo(UPDATED_ADDRESS_POSTAL_CODE);
        assertThat(testRoutingCode.getAddressCity()).isEqualTo(UPDATED_ADDRESS_CITY);
        assertThat(testRoutingCode.getAddressCountrySubdivision()).isEqualTo(UPDATED_ADDRESS_COUNTRY_SUBDIVISION);
        assertThat(testRoutingCode.getAddressCountryCode()).isEqualTo(UPDATED_ADDRESS_COUNTRY_CODE);
        assertThat(testRoutingCode.getAddressCountryLabel()).isEqualTo(UPDATED_ADDRESS_COUNTRY_LABEL);
    }

    @Test
    @Transactional
    void patchNonExistingRoutingCode() throws Exception {
        int databaseSizeBeforeUpdate = this.routingCodeRepository.findAll().size();
        this.routingCode.setInstanceID(count.incrementAndGet());

        // Create the RoutingCode
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restRoutingCodeMockMvc.perform(
                patch(ENTITY_API_URL_ID, routingCodeDTO.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchRoutingCode() throws Exception {
        int databaseSizeBeforeUpdate = this.routingCodeRepository.findAll().size();
        this.routingCode.setInstanceID(count.incrementAndGet());

        // Create the RoutingCode
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restRoutingCodeMockMvc.perform(
                patch(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamRoutingCode() throws Exception {
        int databaseSizeBeforeUpdate = this.routingCodeRepository.findAll().size();
        this.routingCode.setInstanceID(count.incrementAndGet());

        // Create the RoutingCode
        RoutingCodeDTO routingCodeDTO = this.routingCodeMapper.toDto(this.routingCode);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restRoutingCodeMockMvc.perform(
                patch(ENTITY_API_URL)
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(routingCodeDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the RoutingCode in the database
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteRoutingCode() throws Exception {
        // Initialize the database
        this.routingCodeRepository.saveAndFlush(this.routingCode);

        int databaseSizeBeforeDelete = this.routingCodeRepository.findAll().size();

        // Delete the routingCode
        this.restRoutingCodeMockMvc.perform(
                delete(ENTITY_API_URL_ID, this.routingCode.getInstanceID()).with(csrf()).accept(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        List<RoutingCode> routingCodeList = this.routingCodeRepository.findAll();
        assertThat(routingCodeList).hasSize(databaseSizeBeforeDelete - 1);
    }
}
