package com.generix.legalreferential.web.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.AddressLine;
import com.generix.legalreferential.domain.Platform;
import com.generix.legalreferential.domain.RoutingCode;
import com.generix.legalreferential.domain.Siren;
import com.generix.legalreferential.domain.Siret;
import com.generix.legalreferential.domain.enumeration.Nature;
import com.generix.legalreferential.repository.AddressLineRepository;
import com.generix.legalreferential.repository.SirenRepository;
import com.generix.legalreferential.security.SecurityUtils;
import com.generix.legalreferential.service.dto.AddressLineDTO;
import com.generix.legalreferential.service.mapper.AddressLineMapper;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link AddressLineResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_ADMIN" })
class AddressLineResourceIT {

    private static final String DEFAULT_ADDRESS_LINE_ID = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_LINE_ID = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS_SUFFIX = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS_SUFFIX = "BBBBBBBBBB";

    private static final LocalDate DEFAULT_EFFECT_BEGIN_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_EFFECT_BEGIN_DATE = LocalDate.now(ZoneId.systemDefault());
    private static final LocalDate SMALLER_EFFECT_BEGIN_DATE = LocalDate.ofEpochDay(-1L);

    private static final LocalDate DEFAULT_EFFECT_END_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_EFFECT_END_DATE = LocalDate.now(ZoneId.systemDefault());
    private static final LocalDate SMALLER_EFFECT_END_DATE = LocalDate.ofEpochDay(-1L);

    private static final LocalDate DEFAULT_CREATION_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_CREATION_DATE = LocalDate.now(ZoneId.systemDefault());
    private static final LocalDate SMALLER_CREATION_DATE = LocalDate.ofEpochDay(-1L);

    private static final String DEFAULT_CREATION_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATION_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_LAST_MODIFICATION_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_LAST_MODIFICATION_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_MODIFIED_BY = "AAAAAAAAAA";
    private static final String UPDATED_MODIFIED_BY = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/address-lines";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static final String DEFAULT_ISSUER = "https://auth.staging.apps.generix.biz/auth/realms/legalreferential2";
    private static final String DEFAULT_CLIENTID = "legalref_client";
    private static final String DEFAULT_OWNER = "legalref_admin";

    private static Random random = new Random();
    private static AtomicLong count = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private AddressLineRepository addressLineRepository;

    @Autowired
    private SirenRepository sirenRepository;

    @Autowired
    private AddressLineMapper addressLineMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restAddressLineMockMvc;

    private AddressLine addressLine;

    public static MockedStatic<SecurityUtils> mockedStatic;

    /**
     * Create an entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it, if they test an entity
     * which requires the current entity.
     */
    public static AddressLine createEntity(EntityManager em) {
        AddressLine addressLine = new AddressLine()
            .addressLineID(DEFAULT_ADDRESS_LINE_ID)
            .addressSuffix(DEFAULT_ADDRESS_SUFFIX)
            .effectBeginDate(DEFAULT_EFFECT_BEGIN_DATE)
            .effectEndDate(DEFAULT_EFFECT_END_DATE)
            .creationDate(DEFAULT_CREATION_DATE)
            .creationBy(DEFAULT_CREATION_BY)
            .lastModificationDate(DEFAULT_LAST_MODIFICATION_DATE)
            .modifiedBy(DEFAULT_MODIFIED_BY);
        addressLine.setInstanceID(100L);
        addressLine.setNature(Nature.D);
        addressLine.setIssuer(AddressLineResourceIT.DEFAULT_ISSUER);
        addressLine.setClientID(AddressLineResourceIT.DEFAULT_CLIENTID);
        addressLine.setNature(Nature.D);
        addressLine.setOwner(AddressLineResourceIT.DEFAULT_OWNER);
        return addressLine;
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static AddressLine createUpdatedEntity(EntityManager em) {
        AddressLine addressLine = new AddressLine()
            .addressLineID(UPDATED_ADDRESS_LINE_ID)
            .addressSuffix(UPDATED_ADDRESS_SUFFIX)
            .effectBeginDate(UPDATED_EFFECT_BEGIN_DATE)
            .effectEndDate(UPDATED_EFFECT_END_DATE)
            .creationDate(UPDATED_CREATION_DATE)
            .creationBy(UPDATED_CREATION_BY)
            .lastModificationDate(UPDATED_LAST_MODIFICATION_DATE)
            .modifiedBy(UPDATED_MODIFIED_BY);
        return addressLine;
    }

    @AfterAll
    public static void teardown() {
        mockedStatic.close();
    }

    @BeforeAll
    public static void initForAllTest() {
        initStaticSecurityUtils();
    }

    private static void initStaticSecurityUtils() {
        mockedStatic = Mockito.mockStatic(SecurityUtils.class);

        mockedStatic
            .when(() -> SecurityUtils.getIssuerCurrentUser())
            .thenReturn("https://auth.staging.apps.generix.biz/auth/realms/legalreferential2");
        mockedStatic.when(() -> SecurityUtils.getClientIDCurrentUser()).thenReturn("client_app");
        mockedStatic.when(() -> SecurityUtils.getPreferredUsernameCurrentUser()).thenReturn("admin");
    }

    @BeforeEach
    public void initTest() {
        this.addressLine = createEntity(this.em);
        Siren siren = this.sirenRepository.saveAndFlush(SirenResourceIT.createEntity(this.em));
        this.addressLine.setFkAddressLineSiren(siren);
    }

    @Test
    @Transactional
    void createAddressLine() throws Exception {
        int databaseSizeBeforeCreate = this.addressLineRepository.findAll().size();
        // Create the AddressLine
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);
        this.restAddressLineMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isCreated());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeCreate + 1);
        AddressLine testAddressLine = addressLineList.get(addressLineList.size() - 1);
        assertThat(testAddressLine.getAddressLineID()).isEqualTo(DEFAULT_ADDRESS_LINE_ID);
        assertThat(testAddressLine.getAddressSuffix()).isEqualTo(DEFAULT_ADDRESS_SUFFIX);
        assertThat(testAddressLine.getEffectBeginDate()).isEqualTo(DEFAULT_EFFECT_BEGIN_DATE);
        assertThat(testAddressLine.getEffectEndDate()).isEqualTo(DEFAULT_EFFECT_END_DATE);
        assertThat(testAddressLine.getCreationDate()).isEqualTo(DEFAULT_CREATION_DATE);
        assertThat(testAddressLine.getCreationBy()).isEqualTo(DEFAULT_CREATION_BY);
        assertThat(testAddressLine.getLastModificationDate()).isEqualTo(DEFAULT_LAST_MODIFICATION_DATE);
        assertThat(testAddressLine.getModifiedBy()).isEqualTo(DEFAULT_MODIFIED_BY);
    }

    @Test
    @Transactional
    void createAddressLineWithExistingId() throws Exception {
        // Create the AddressLine with an existing ID
        this.addressLine.setInstanceID(null);
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        int databaseSizeBeforeCreate = this.addressLineRepository.findAll().size();

        // An entity with an existing ID cannot be created, so this API call must fail
        this.restAddressLineMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkAddressLineIDIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.addressLineRepository.findAll().size();
        // set the field null
        this.addressLine.setAddressLineID(null);

        // Create the AddressLine, which fails.
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        this.restAddressLineMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEffectBeginDateIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.addressLineRepository.findAll().size();
        // set the field null
        this.addressLine.setEffectBeginDate(null);

        // Create the AddressLine, which fails.
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        this.restAddressLineMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreationDateIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.addressLineRepository.findAll().size();
        // set the field null
        this.addressLine.setCreationDate(null);

        // Create the AddressLine, which fails.
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        this.restAddressLineMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreationByIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.addressLineRepository.findAll().size();
        // set the field null
        this.addressLine.setCreationBy(null);

        // Create the AddressLine, which fails.
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        this.restAddressLineMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkLastModificationDateIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.addressLineRepository.findAll().size();
        // set the field null
        this.addressLine.setLastModificationDate(null);

        // Create the AddressLine, which fails.
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        this.restAddressLineMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkModifiedByIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.addressLineRepository.findAll().size();
        // set the field null
        this.addressLine.setModifiedBy(null);

        // Create the AddressLine, which fails.
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        this.restAddressLineMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllAddressLines() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList
        this.restAddressLineMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.addressLine.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].addressLineID").value(hasItem(DEFAULT_ADDRESS_LINE_ID)))
            .andExpect(jsonPath("$.[*].addressSuffix").value(hasItem(DEFAULT_ADDRESS_SUFFIX)))
            .andExpect(jsonPath("$.[*].effectBeginDate").value(hasItem(DEFAULT_EFFECT_BEGIN_DATE.toString())))
            .andExpect(jsonPath("$.[*].effectEndDate").value(hasItem(DEFAULT_EFFECT_END_DATE.toString())))
            .andExpect(jsonPath("$.[*].creationDate").value(hasItem(DEFAULT_CREATION_DATE.toString())))
            .andExpect(jsonPath("$.[*].creationBy").value(hasItem(DEFAULT_CREATION_BY)))
            .andExpect(jsonPath("$.[*].lastModificationDate").value(hasItem(DEFAULT_LAST_MODIFICATION_DATE.toString())))
            .andExpect(jsonPath("$.[*].modifiedBy").value(hasItem(DEFAULT_MODIFIED_BY)));
    }

    @Test
    @Transactional
    void getAddressLine() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get the addressLine
        this.restAddressLineMockMvc.perform(get(ENTITY_API_URL_ID, this.addressLine.getInstanceID()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.instanceID").value(this.addressLine.getInstanceID().intValue()))
            .andExpect(jsonPath("$.addressLineID").value(DEFAULT_ADDRESS_LINE_ID))
            .andExpect(jsonPath("$.addressSuffix").value(DEFAULT_ADDRESS_SUFFIX))
            .andExpect(jsonPath("$.effectBeginDate").value(DEFAULT_EFFECT_BEGIN_DATE.toString()))
            .andExpect(jsonPath("$.effectEndDate").value(DEFAULT_EFFECT_END_DATE.toString()))
            .andExpect(jsonPath("$.creationDate").value(DEFAULT_CREATION_DATE.toString()))
            .andExpect(jsonPath("$.creationBy").value(DEFAULT_CREATION_BY))
            .andExpect(jsonPath("$.lastModificationDate").value(DEFAULT_LAST_MODIFICATION_DATE.toString()))
            .andExpect(jsonPath("$.modifiedBy").value(DEFAULT_MODIFIED_BY));
    }

    @Test
    @Transactional
    void getAddressLinesByIdFiltering() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        Long id = this.addressLine.getInstanceID();

        defaultAddressLineShouldBeFound("instanceID.equals=" + id);
        defaultAddressLineShouldNotBeFound("instanceID.notEquals=" + id);

        defaultAddressLineShouldBeFound("instanceID.greaterThanOrEqual=" + id);
        defaultAddressLineShouldNotBeFound("instanceID.greaterThan=" + id);

        defaultAddressLineShouldBeFound("instanceID.lessThanOrEqual=" + id);
        defaultAddressLineShouldNotBeFound("instanceID.lessThan=" + id);
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressLineIDIsEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressLineID equals to DEFAULT_ADDRESS_LINE_ID
        defaultAddressLineShouldBeFound("addressLineID.equals=" + DEFAULT_ADDRESS_LINE_ID);

        // Get all the addressLineList where addressLineID equals to UPDATED_ADDRESS_LINE_ID
        defaultAddressLineShouldNotBeFound("addressLineID.equals=" + UPDATED_ADDRESS_LINE_ID);
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressLineIDIsInShouldWork() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressLineID in DEFAULT_ADDRESS_LINE_ID or UPDATED_ADDRESS_LINE_ID
        defaultAddressLineShouldBeFound("addressLineID.in=" + DEFAULT_ADDRESS_LINE_ID + "," + UPDATED_ADDRESS_LINE_ID);

        // Get all the addressLineList where addressLineID equals to UPDATED_ADDRESS_LINE_ID
        defaultAddressLineShouldNotBeFound("addressLineID.in=" + UPDATED_ADDRESS_LINE_ID);
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressLineIDIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressLineID is not null
        defaultAddressLineShouldBeFound("addressLineID.specified=true");

        // Get all the addressLineList where addressLineID is null
        defaultAddressLineShouldNotBeFound("addressLineID.specified=false");
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressLineIDContainsSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressLineID contains DEFAULT_ADDRESS_LINE_ID
        defaultAddressLineShouldBeFound("addressLineID.contains=" + DEFAULT_ADDRESS_LINE_ID);

        // Get all the addressLineList where addressLineID contains UPDATED_ADDRESS_LINE_ID
        defaultAddressLineShouldNotBeFound("addressLineID.contains=" + UPDATED_ADDRESS_LINE_ID);
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressLineIDNotContainsSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressLineID does not contain DEFAULT_ADDRESS_LINE_ID
        defaultAddressLineShouldNotBeFound("addressLineID.doesNotContain=" + DEFAULT_ADDRESS_LINE_ID);

        // Get all the addressLineList where addressLineID does not contain UPDATED_ADDRESS_LINE_ID
        defaultAddressLineShouldBeFound("addressLineID.doesNotContain=" + UPDATED_ADDRESS_LINE_ID);
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressSuffixIsEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressSuffix equals to DEFAULT_ADDRESS_SUFFIX
        defaultAddressLineShouldBeFound("addressSuffix.equals=" + DEFAULT_ADDRESS_SUFFIX);

        // Get all the addressLineList where addressSuffix equals to UPDATED_ADDRESS_SUFFIX
        defaultAddressLineShouldNotBeFound("addressSuffix.equals=" + UPDATED_ADDRESS_SUFFIX);
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressSuffixIsInShouldWork() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressSuffix in DEFAULT_ADDRESS_SUFFIX or UPDATED_ADDRESS_SUFFIX
        defaultAddressLineShouldBeFound("addressSuffix.in=" + DEFAULT_ADDRESS_SUFFIX + "," + UPDATED_ADDRESS_SUFFIX);

        // Get all the addressLineList where addressSuffix equals to UPDATED_ADDRESS_SUFFIX
        defaultAddressLineShouldNotBeFound("addressSuffix.in=" + UPDATED_ADDRESS_SUFFIX);
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressSuffixIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressSuffix is not null
        defaultAddressLineShouldBeFound("addressSuffix.specified=true");

        // Get all the addressLineList where addressSuffix is null
        defaultAddressLineShouldNotBeFound("addressSuffix.specified=false");
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressSuffixContainsSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressSuffix contains DEFAULT_ADDRESS_SUFFIX
        defaultAddressLineShouldBeFound("addressSuffix.contains=" + DEFAULT_ADDRESS_SUFFIX);

        // Get all the addressLineList where addressSuffix contains UPDATED_ADDRESS_SUFFIX
        defaultAddressLineShouldNotBeFound("addressSuffix.contains=" + UPDATED_ADDRESS_SUFFIX);
    }

    @Test
    @Transactional
    void getAllAddressLinesByAddressSuffixNotContainsSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where addressSuffix does not contain DEFAULT_ADDRESS_SUFFIX
        defaultAddressLineShouldNotBeFound("addressSuffix.doesNotContain=" + DEFAULT_ADDRESS_SUFFIX);

        // Get all the addressLineList where addressSuffix does not contain UPDATED_ADDRESS_SUFFIX
        defaultAddressLineShouldBeFound("addressSuffix.doesNotContain=" + UPDATED_ADDRESS_SUFFIX);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectBeginDateIsEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectBeginDate equals to DEFAULT_EFFECT_BEGIN_DATE
        defaultAddressLineShouldBeFound("effectBeginDate.equals=" + DEFAULT_EFFECT_BEGIN_DATE);

        // Get all the addressLineList where effectBeginDate equals to UPDATED_EFFECT_BEGIN_DATE
        defaultAddressLineShouldNotBeFound("effectBeginDate.equals=" + UPDATED_EFFECT_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectBeginDateIsInShouldWork() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectBeginDate in DEFAULT_EFFECT_BEGIN_DATE or UPDATED_EFFECT_BEGIN_DATE
        defaultAddressLineShouldBeFound("effectBeginDate.in=" + DEFAULT_EFFECT_BEGIN_DATE + "," + UPDATED_EFFECT_BEGIN_DATE);

        // Get all the addressLineList where effectBeginDate equals to UPDATED_EFFECT_BEGIN_DATE
        defaultAddressLineShouldNotBeFound("effectBeginDate.in=" + UPDATED_EFFECT_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectBeginDateIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectBeginDate is not null
        defaultAddressLineShouldBeFound("effectBeginDate.specified=true");

        // Get all the addressLineList where effectBeginDate is null
        defaultAddressLineShouldNotBeFound("effectBeginDate.specified=false");
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectBeginDateIsGreaterThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectBeginDate is greater than or equal to DEFAULT_EFFECT_BEGIN_DATE
        defaultAddressLineShouldBeFound("effectBeginDate.greaterThanOrEqual=" + DEFAULT_EFFECT_BEGIN_DATE);

        // Get all the addressLineList where effectBeginDate is greater than or equal to UPDATED_EFFECT_BEGIN_DATE
        defaultAddressLineShouldNotBeFound("effectBeginDate.greaterThanOrEqual=" + UPDATED_EFFECT_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectBeginDateIsLessThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectBeginDate is less than or equal to DEFAULT_EFFECT_BEGIN_DATE
        defaultAddressLineShouldBeFound("effectBeginDate.lessThanOrEqual=" + DEFAULT_EFFECT_BEGIN_DATE);

        // Get all the addressLineList where effectBeginDate is less than or equal to SMALLER_EFFECT_BEGIN_DATE
        defaultAddressLineShouldNotBeFound("effectBeginDate.lessThanOrEqual=" + SMALLER_EFFECT_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectBeginDateIsLessThanSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectBeginDate is less than DEFAULT_EFFECT_BEGIN_DATE
        defaultAddressLineShouldNotBeFound("effectBeginDate.lessThan=" + DEFAULT_EFFECT_BEGIN_DATE);

        // Get all the addressLineList where effectBeginDate is less than UPDATED_EFFECT_BEGIN_DATE
        defaultAddressLineShouldBeFound("effectBeginDate.lessThan=" + UPDATED_EFFECT_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectBeginDateIsGreaterThanSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectBeginDate is greater than DEFAULT_EFFECT_BEGIN_DATE
        defaultAddressLineShouldNotBeFound("effectBeginDate.greaterThan=" + DEFAULT_EFFECT_BEGIN_DATE);

        // Get all the addressLineList where effectBeginDate is greater than SMALLER_EFFECT_BEGIN_DATE
        defaultAddressLineShouldBeFound("effectBeginDate.greaterThan=" + SMALLER_EFFECT_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectEndDateIsEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectEndDate equals to DEFAULT_EFFECT_END_DATE
        defaultAddressLineShouldBeFound("effectEndDate.equals=" + DEFAULT_EFFECT_END_DATE);

        // Get all the addressLineList where effectEndDate equals to UPDATED_EFFECT_END_DATE
        defaultAddressLineShouldNotBeFound("effectEndDate.equals=" + UPDATED_EFFECT_END_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectEndDateIsInShouldWork() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectEndDate in DEFAULT_EFFECT_END_DATE or UPDATED_EFFECT_END_DATE
        defaultAddressLineShouldBeFound("effectEndDate.in=" + DEFAULT_EFFECT_END_DATE + "," + UPDATED_EFFECT_END_DATE);

        // Get all the addressLineList where effectEndDate equals to UPDATED_EFFECT_END_DATE
        defaultAddressLineShouldNotBeFound("effectEndDate.in=" + UPDATED_EFFECT_END_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectEndDateIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectEndDate is not null
        defaultAddressLineShouldBeFound("effectEndDate.specified=true");

        // Get all the addressLineList where effectEndDate is null
        defaultAddressLineShouldNotBeFound("effectEndDate.specified=false");
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectEndDateIsGreaterThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectEndDate is greater than or equal to DEFAULT_EFFECT_END_DATE
        defaultAddressLineShouldBeFound("effectEndDate.greaterThanOrEqual=" + DEFAULT_EFFECT_END_DATE);

        // Get all the addressLineList where effectEndDate is greater than or equal to UPDATED_EFFECT_END_DATE
        defaultAddressLineShouldNotBeFound("effectEndDate.greaterThanOrEqual=" + UPDATED_EFFECT_END_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectEndDateIsLessThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectEndDate is less than or equal to DEFAULT_EFFECT_END_DATE
        defaultAddressLineShouldBeFound("effectEndDate.lessThanOrEqual=" + DEFAULT_EFFECT_END_DATE);

        // Get all the addressLineList where effectEndDate is less than or equal to SMALLER_EFFECT_END_DATE
        defaultAddressLineShouldNotBeFound("effectEndDate.lessThanOrEqual=" + SMALLER_EFFECT_END_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectEndDateIsLessThanSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectEndDate is less than DEFAULT_EFFECT_END_DATE
        defaultAddressLineShouldNotBeFound("effectEndDate.lessThan=" + DEFAULT_EFFECT_END_DATE);

        // Get all the addressLineList where effectEndDate is less than UPDATED_EFFECT_END_DATE
        defaultAddressLineShouldBeFound("effectEndDate.lessThan=" + UPDATED_EFFECT_END_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByEffectEndDateIsGreaterThanSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where effectEndDate is greater than DEFAULT_EFFECT_END_DATE
        defaultAddressLineShouldNotBeFound("effectEndDate.greaterThan=" + DEFAULT_EFFECT_END_DATE);

        // Get all the addressLineList where effectEndDate is greater than SMALLER_EFFECT_END_DATE
        defaultAddressLineShouldBeFound("effectEndDate.greaterThan=" + SMALLER_EFFECT_END_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationDateIsEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationDate equals to DEFAULT_CREATION_DATE
        defaultAddressLineShouldBeFound("creationDate.equals=" + DEFAULT_CREATION_DATE);

        // Get all the addressLineList where creationDate equals to UPDATED_CREATION_DATE
        defaultAddressLineShouldNotBeFound("creationDate.equals=" + UPDATED_CREATION_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationDateIsInShouldWork() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationDate in DEFAULT_CREATION_DATE or UPDATED_CREATION_DATE
        defaultAddressLineShouldBeFound("creationDate.in=" + DEFAULT_CREATION_DATE + "," + UPDATED_CREATION_DATE);

        // Get all the addressLineList where creationDate equals to UPDATED_CREATION_DATE
        defaultAddressLineShouldNotBeFound("creationDate.in=" + UPDATED_CREATION_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationDateIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationDate is not null
        defaultAddressLineShouldBeFound("creationDate.specified=true");

        // Get all the addressLineList where creationDate is null
        defaultAddressLineShouldNotBeFound("creationDate.specified=false");
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationDateIsGreaterThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationDate is greater than or equal to DEFAULT_CREATION_DATE
        defaultAddressLineShouldBeFound("creationDate.greaterThanOrEqual=" + DEFAULT_CREATION_DATE);

        // Get all the addressLineList where creationDate is greater than or equal to UPDATED_CREATION_DATE
        defaultAddressLineShouldNotBeFound("creationDate.greaterThanOrEqual=" + UPDATED_CREATION_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationDateIsLessThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationDate is less than or equal to DEFAULT_CREATION_DATE
        defaultAddressLineShouldBeFound("creationDate.lessThanOrEqual=" + DEFAULT_CREATION_DATE);

        // Get all the addressLineList where creationDate is less than or equal to SMALLER_CREATION_DATE
        defaultAddressLineShouldNotBeFound("creationDate.lessThanOrEqual=" + SMALLER_CREATION_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationDateIsLessThanSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationDate is less than DEFAULT_CREATION_DATE
        defaultAddressLineShouldNotBeFound("creationDate.lessThan=" + DEFAULT_CREATION_DATE);

        // Get all the addressLineList where creationDate is less than UPDATED_CREATION_DATE
        defaultAddressLineShouldBeFound("creationDate.lessThan=" + UPDATED_CREATION_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationDateIsGreaterThanSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationDate is greater than DEFAULT_CREATION_DATE
        defaultAddressLineShouldNotBeFound("creationDate.greaterThan=" + DEFAULT_CREATION_DATE);

        // Get all the addressLineList where creationDate is greater than SMALLER_CREATION_DATE
        defaultAddressLineShouldBeFound("creationDate.greaterThan=" + SMALLER_CREATION_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationByIsEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationBy equals to DEFAULT_CREATION_BY
        defaultAddressLineShouldBeFound("creationBy.equals=" + DEFAULT_CREATION_BY);

        // Get all the addressLineList where creationBy equals to UPDATED_CREATION_BY
        defaultAddressLineShouldNotBeFound("creationBy.equals=" + UPDATED_CREATION_BY);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationByIsInShouldWork() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationBy in DEFAULT_CREATION_BY or UPDATED_CREATION_BY
        defaultAddressLineShouldBeFound("creationBy.in=" + DEFAULT_CREATION_BY + "," + UPDATED_CREATION_BY);

        // Get all the addressLineList where creationBy equals to UPDATED_CREATION_BY
        defaultAddressLineShouldNotBeFound("creationBy.in=" + UPDATED_CREATION_BY);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationByIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationBy is not null
        defaultAddressLineShouldBeFound("creationBy.specified=true");

        // Get all the addressLineList where creationBy is null
        defaultAddressLineShouldNotBeFound("creationBy.specified=false");
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationByContainsSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationBy contains DEFAULT_CREATION_BY
        defaultAddressLineShouldBeFound("creationBy.contains=" + DEFAULT_CREATION_BY);

        // Get all the addressLineList where creationBy contains UPDATED_CREATION_BY
        defaultAddressLineShouldNotBeFound("creationBy.contains=" + UPDATED_CREATION_BY);
    }

    @Test
    @Transactional
    void getAllAddressLinesByCreationByNotContainsSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where creationBy does not contain DEFAULT_CREATION_BY
        defaultAddressLineShouldNotBeFound("creationBy.doesNotContain=" + DEFAULT_CREATION_BY);

        // Get all the addressLineList where creationBy does not contain UPDATED_CREATION_BY
        defaultAddressLineShouldBeFound("creationBy.doesNotContain=" + UPDATED_CREATION_BY);
    }

    @Test
    @Transactional
    void getAllAddressLinesByLastModificationDateIsEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where lastModificationDate equals to DEFAULT_LAST_MODIFICATION_DATE
        defaultAddressLineShouldBeFound("lastModificationDate.equals=" + DEFAULT_LAST_MODIFICATION_DATE);

        // Get all the addressLineList where lastModificationDate equals to UPDATED_LAST_MODIFICATION_DATE
        defaultAddressLineShouldNotBeFound("lastModificationDate.equals=" + UPDATED_LAST_MODIFICATION_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByLastModificationDateIsInShouldWork() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where lastModificationDate in DEFAULT_LAST_MODIFICATION_DATE or UPDATED_LAST_MODIFICATION_DATE
        defaultAddressLineShouldBeFound("lastModificationDate.in=" + DEFAULT_LAST_MODIFICATION_DATE + "," + UPDATED_LAST_MODIFICATION_DATE);

        // Get all the addressLineList where lastModificationDate equals to UPDATED_LAST_MODIFICATION_DATE
        defaultAddressLineShouldNotBeFound("lastModificationDate.in=" + UPDATED_LAST_MODIFICATION_DATE);
    }

    @Test
    @Transactional
    void getAllAddressLinesByLastModificationDateIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where lastModificationDate is not null
        defaultAddressLineShouldBeFound("lastModificationDate.specified=true");

        // Get all the addressLineList where lastModificationDate is null
        defaultAddressLineShouldNotBeFound("lastModificationDate.specified=false");
    }

    @Test
    @Transactional
    void getAllAddressLinesByModifiedByIsEqualToSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where modifiedBy equals to DEFAULT_MODIFIED_BY
        defaultAddressLineShouldBeFound("modifiedBy.equals=" + DEFAULT_MODIFIED_BY);

        // Get all the addressLineList where modifiedBy equals to UPDATED_MODIFIED_BY
        defaultAddressLineShouldNotBeFound("modifiedBy.equals=" + UPDATED_MODIFIED_BY);
    }

    @Test
    @Transactional
    void getAllAddressLinesByModifiedByIsInShouldWork() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where modifiedBy in DEFAULT_MODIFIED_BY or UPDATED_MODIFIED_BY
        defaultAddressLineShouldBeFound("modifiedBy.in=" + DEFAULT_MODIFIED_BY + "," + UPDATED_MODIFIED_BY);

        // Get all the addressLineList where modifiedBy equals to UPDATED_MODIFIED_BY
        defaultAddressLineShouldNotBeFound("modifiedBy.in=" + UPDATED_MODIFIED_BY);
    }

    @Test
    @Transactional
    void getAllAddressLinesByModifiedByIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where modifiedBy is not null
        defaultAddressLineShouldBeFound("modifiedBy.specified=true");

        // Get all the addressLineList where modifiedBy is null
        defaultAddressLineShouldNotBeFound("modifiedBy.specified=false");
    }

    @Test
    @Transactional
    void getAllAddressLinesByModifiedByContainsSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where modifiedBy contains DEFAULT_MODIFIED_BY
        defaultAddressLineShouldBeFound("modifiedBy.contains=" + DEFAULT_MODIFIED_BY);

        // Get all the addressLineList where modifiedBy contains UPDATED_MODIFIED_BY
        defaultAddressLineShouldNotBeFound("modifiedBy.contains=" + UPDATED_MODIFIED_BY);
    }

    @Test
    @Transactional
    void getAllAddressLinesByModifiedByNotContainsSomething() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        // Get all the addressLineList where modifiedBy does not contain DEFAULT_MODIFIED_BY
        defaultAddressLineShouldNotBeFound("modifiedBy.doesNotContain=" + DEFAULT_MODIFIED_BY);

        // Get all the addressLineList where modifiedBy does not contain UPDATED_MODIFIED_BY
        defaultAddressLineShouldBeFound("modifiedBy.doesNotContain=" + UPDATED_MODIFIED_BY);
    }

    @Test
    @Transactional
    void getAllAddressLinesByFkAddressLineRoutingCodeIsEqualToSomething() throws Exception {
        RoutingCode fkAddressLineRoutingCode;
        if (TestUtil.findAll(this.em, RoutingCode.class).isEmpty()) {
            this.addressLineRepository.saveAndFlush(this.addressLine);
            fkAddressLineRoutingCode = RoutingCodeResourceIT.createEntity();
        } else {
            fkAddressLineRoutingCode = TestUtil.findAll(this.em, RoutingCode.class).get(0);
        }
        this.em.persist(fkAddressLineRoutingCode);
        this.em.flush();
        this.addressLine.setFkAddressLineRoutingCode(fkAddressLineRoutingCode);
        this.addressLine.setFkAddressLineSiren(null);
        this.addressLineRepository.saveAndFlush(this.addressLine);
        Long fkAddressLineRoutingCodeId = fkAddressLineRoutingCode.getInstanceID();

        // Get all the addressLineList where fkAddressLineRoutingCode equals to fkAddressLineRoutingCodeId
        defaultAddressLineShouldBeFound("fkAddressLineRoutingCodeId.equals=" + fkAddressLineRoutingCodeId);

        // Get all the addressLineList where fkAddressLineRoutingCode equals to (fkAddressLineRoutingCodeId + 1)
        defaultAddressLineShouldNotBeFound("fkAddressLineRoutingCodeId.equals=" + (fkAddressLineRoutingCodeId + 1));
    }

    @Test
    @Transactional
    void getAllAddressLinesByFkAddressLineSirenIsEqualToSomething() throws Exception {
        Siren fkAddressLineSiren;
        if (TestUtil.findAll(this.em, Siren.class).isEmpty()) {
            this.addressLineRepository.saveAndFlush(this.addressLine);
            fkAddressLineSiren = SirenResourceIT.createEntity(this.em);
        } else {
            fkAddressLineSiren = TestUtil.findAll(this.em, Siren.class).get(0);
        }
        this.em.persist(fkAddressLineSiren);
        this.em.flush();
        this.addressLine.setFkAddressLineSiren(fkAddressLineSiren);
        this.addressLineRepository.saveAndFlush(this.addressLine);
        Long fkAddressLineSirenId = fkAddressLineSiren.getInstanceID();

        // Get all the addressLineList where fkAddressLineSiren equals to fkAddressLineSirenId
        defaultAddressLineShouldBeFound("fkAddressLineSirenId.equals=" + fkAddressLineSirenId);

        // Get all the addressLineList where fkAddressLineSiren equals to (fkAddressLineSirenId + 1)
        defaultAddressLineShouldNotBeFound("fkAddressLineSirenId.equals=" + (fkAddressLineSirenId + 1));
    }

    @Test
    @Transactional
    void getAllAddressLinesByFkAddressLineSiretIsEqualToSomething() throws Exception {
        Siret fkAddressLineSiret;
        if (TestUtil.findAll(this.em, Siret.class).isEmpty()) {
            this.addressLineRepository.saveAndFlush(this.addressLine);
            fkAddressLineSiret = SiretResourceIT.createEntity(this.em);
        } else {
            fkAddressLineSiret = TestUtil.findAll(this.em, Siret.class).get(0);
        }
        this.em.persist(fkAddressLineSiret);
        this.em.flush();
        this.addressLine.setFkAddressLineSiret(fkAddressLineSiret);
        this.addressLine.setFkAddressLineSiren(null);
        this.addressLineRepository.saveAndFlush(this.addressLine);
        Long fkAddressLineSiretId = fkAddressLineSiret.getInstanceID();

        // Get all the addressLineList where fkAddressLineSiret equals to fkAddressLineSiretId
        defaultAddressLineShouldBeFound("fkAddressLineSiretId.equals=" + fkAddressLineSiretId);

        // Get all the addressLineList where fkAddressLineSiret equals to (fkAddressLineSiretId + 1)
        defaultAddressLineShouldNotBeFound("fkAddressLineSiretId.equals=" + (fkAddressLineSiretId + 1));
    }

    @Test
    @Transactional
    void getAllAddressLinesByFkAddressLinePlatformIsEqualToSomething() throws Exception {
        Platform fkAddressLinePlatform;
        if (TestUtil.findAll(this.em, Platform.class).isEmpty()) {
            this.addressLineRepository.saveAndFlush(this.addressLine);
            fkAddressLinePlatform = PlatformResourceIT.createEntity();
        } else {
            fkAddressLinePlatform = TestUtil.findAll(this.em, Platform.class).get(0);
        }
        this.em.persist(fkAddressLinePlatform);
        this.em.flush();
        this.addressLine.setFkAddressLinePlatform(fkAddressLinePlatform);
        this.addressLineRepository.saveAndFlush(this.addressLine);
        Long fkAddressLinePlatformId = fkAddressLinePlatform.getInstanceID();

        // Get all the addressLineList where fkAddressLinePlatform equals to fkAddressLinePlatformId
        defaultAddressLineShouldBeFound("fkAddressLinePlatformId.equals=" + fkAddressLinePlatformId);

        // Get all the addressLineList where fkAddressLinePlatform equals to (fkAddressLinePlatformId + 1)
        defaultAddressLineShouldNotBeFound("fkAddressLinePlatformId.equals=" + (fkAddressLinePlatformId + 1));
    }

    /**
     * Executes the search, and checks that the default entity is returned.
     */
    private void defaultAddressLineShouldBeFound(String filter) throws Exception {
        this.restAddressLineMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.addressLine.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].addressLineID").value(hasItem(DEFAULT_ADDRESS_LINE_ID)))
            .andExpect(jsonPath("$.[*].addressSuffix").value(hasItem(DEFAULT_ADDRESS_SUFFIX)))
            .andExpect(jsonPath("$.[*].effectBeginDate").value(hasItem(DEFAULT_EFFECT_BEGIN_DATE.toString())))
            .andExpect(jsonPath("$.[*].effectEndDate").value(hasItem(DEFAULT_EFFECT_END_DATE.toString())))
            .andExpect(jsonPath("$.[*].creationDate").value(hasItem(DEFAULT_CREATION_DATE.toString())))
            .andExpect(jsonPath("$.[*].creationBy").value(hasItem(DEFAULT_CREATION_BY)))
            .andExpect(jsonPath("$.[*].lastModificationDate").value(hasItem(DEFAULT_LAST_MODIFICATION_DATE.toString())))
            .andExpect(jsonPath("$.[*].modifiedBy").value(hasItem(DEFAULT_MODIFIED_BY)));

        // Check, that the count call also returns 1
        this.restAddressLineMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("1"));
    }

    /**
     * Executes the search, and checks that the default entity is not returned.
     */
    private void defaultAddressLineShouldNotBeFound(String filter) throws Exception {
        this.restAddressLineMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$").isEmpty());

        // Check, that the count call also returns 0
        this.restAddressLineMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("0"));
    }

    @Test
    @Transactional
    void getNonExistingAddressLine() throws Exception {
        // Get the addressLine
        this.restAddressLineMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAddressLine() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        int databaseSizeBeforeUpdate = this.addressLineRepository.findAll().size();

        // Update the addressLine
        AddressLine updatedAddressLine = this.addressLineRepository.findById(this.addressLine.getInstanceID()).get();
        // Disconnect from session so that the updates on updatedAddressLine are not directly saved in db
        this.em.detach(updatedAddressLine);
        updatedAddressLine
            .addressLineID(UPDATED_ADDRESS_LINE_ID)
            .addressSuffix(UPDATED_ADDRESS_SUFFIX)
            .effectBeginDate(UPDATED_EFFECT_BEGIN_DATE)
            .effectEndDate(UPDATED_EFFECT_END_DATE)
            .creationDate(UPDATED_CREATION_DATE)
            .creationBy(UPDATED_CREATION_BY)
            .lastModificationDate(UPDATED_LAST_MODIFICATION_DATE)
            .modifiedBy(UPDATED_MODIFIED_BY);
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(updatedAddressLine);

        this.restAddressLineMockMvc.perform(
                put(ENTITY_API_URL_ID, addressLineDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isOk());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeUpdate);
        AddressLine testAddressLine = addressLineList.get(addressLineList.size() - 1);
        assertThat(testAddressLine.getAddressLineID()).isEqualTo(UPDATED_ADDRESS_LINE_ID);
        assertThat(testAddressLine.getAddressSuffix()).isEqualTo(UPDATED_ADDRESS_SUFFIX);
        assertThat(testAddressLine.getEffectBeginDate()).isEqualTo(UPDATED_EFFECT_BEGIN_DATE);
        assertThat(testAddressLine.getEffectEndDate()).isEqualTo(UPDATED_EFFECT_END_DATE);
        assertThat(testAddressLine.getCreationDate()).isEqualTo(UPDATED_CREATION_DATE);
        assertThat(testAddressLine.getCreationBy()).isEqualTo(UPDATED_CREATION_BY);
        assertThat(testAddressLine.getLastModificationDate()).isEqualTo(UPDATED_LAST_MODIFICATION_DATE);
        assertThat(testAddressLine.getModifiedBy()).isEqualTo(UPDATED_MODIFIED_BY);
    }

    @Test
    @Transactional
    void putNonExistingAddressLine() throws Exception {
        int databaseSizeBeforeUpdate = this.addressLineRepository.findAll().size();
        this.addressLine.setInstanceID(count.incrementAndGet());

        // Create the AddressLine
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restAddressLineMockMvc.perform(
                put(ENTITY_API_URL_ID, addressLineDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAddressLine() throws Exception {
        int databaseSizeBeforeUpdate = this.addressLineRepository.findAll().size();
        this.addressLine.setInstanceID(count.incrementAndGet());

        // Create the AddressLine
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restAddressLineMockMvc.perform(
                put(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAddressLine() throws Exception {
        int databaseSizeBeforeUpdate = this.addressLineRepository.findAll().size();
        this.addressLine.setInstanceID(count.incrementAndGet());

        // Create the AddressLine
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restAddressLineMockMvc.perform(
                put(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateAddressLineWithPatch() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        int databaseSizeBeforeUpdate = this.addressLineRepository.findAll().size();

        // Update the addressLine using partial update
        AddressLine partialUpdatedAddressLine = new AddressLine();
        partialUpdatedAddressLine.setInstanceID(this.addressLine.getInstanceID());

        partialUpdatedAddressLine
            .addressLineID(UPDATED_ADDRESS_LINE_ID)
            .effectBeginDate(UPDATED_EFFECT_BEGIN_DATE)
            .lastModificationDate(UPDATED_LAST_MODIFICATION_DATE)
            .modifiedBy(UPDATED_MODIFIED_BY);

        this.restAddressLineMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAddressLine.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedAddressLine))
            )
            .andExpect(status().isOk());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeUpdate);
        AddressLine testAddressLine = addressLineList.get(addressLineList.size() - 1);
        assertThat(testAddressLine.getAddressLineID()).isEqualTo(UPDATED_ADDRESS_LINE_ID);
        assertThat(testAddressLine.getAddressSuffix()).isEqualTo(DEFAULT_ADDRESS_SUFFIX);
        assertThat(testAddressLine.getEffectBeginDate()).isEqualTo(UPDATED_EFFECT_BEGIN_DATE);
        assertThat(testAddressLine.getEffectEndDate()).isEqualTo(DEFAULT_EFFECT_END_DATE);
        assertThat(testAddressLine.getCreationDate()).isEqualTo(DEFAULT_CREATION_DATE);
        assertThat(testAddressLine.getCreationBy()).isEqualTo(DEFAULT_CREATION_BY);
        assertThat(testAddressLine.getLastModificationDate()).isEqualTo(UPDATED_LAST_MODIFICATION_DATE);
        assertThat(testAddressLine.getModifiedBy()).isEqualTo(UPDATED_MODIFIED_BY);
    }

    @Test
    @Transactional
    void fullUpdateAddressLineWithPatch() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        int databaseSizeBeforeUpdate = this.addressLineRepository.findAll().size();

        // Update the addressLine using partial update
        AddressLine partialUpdatedAddressLine = new AddressLine();
        partialUpdatedAddressLine.setInstanceID(this.addressLine.getInstanceID());

        partialUpdatedAddressLine
            .addressLineID(UPDATED_ADDRESS_LINE_ID)
            .addressSuffix(UPDATED_ADDRESS_SUFFIX)
            .effectBeginDate(UPDATED_EFFECT_BEGIN_DATE)
            .effectEndDate(UPDATED_EFFECT_END_DATE)
            .creationDate(UPDATED_CREATION_DATE)
            .creationBy(UPDATED_CREATION_BY)
            .lastModificationDate(UPDATED_LAST_MODIFICATION_DATE)
            .modifiedBy(UPDATED_MODIFIED_BY);

        this.restAddressLineMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAddressLine.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedAddressLine))
            )
            .andExpect(status().isOk());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeUpdate);
        AddressLine testAddressLine = addressLineList.get(addressLineList.size() - 1);
        assertThat(testAddressLine.getAddressLineID()).isEqualTo(UPDATED_ADDRESS_LINE_ID);
        assertThat(testAddressLine.getAddressSuffix()).isEqualTo(UPDATED_ADDRESS_SUFFIX);
        assertThat(testAddressLine.getEffectBeginDate()).isEqualTo(UPDATED_EFFECT_BEGIN_DATE);
        assertThat(testAddressLine.getEffectEndDate()).isEqualTo(UPDATED_EFFECT_END_DATE);
        assertThat(testAddressLine.getCreationDate()).isEqualTo(UPDATED_CREATION_DATE);
        assertThat(testAddressLine.getCreationBy()).isEqualTo(UPDATED_CREATION_BY);
        assertThat(testAddressLine.getLastModificationDate()).isEqualTo(UPDATED_LAST_MODIFICATION_DATE);
        assertThat(testAddressLine.getModifiedBy()).isEqualTo(UPDATED_MODIFIED_BY);
    }

    @Test
    @Transactional
    void patchNonExistingAddressLine() throws Exception {
        int databaseSizeBeforeUpdate = this.addressLineRepository.findAll().size();
        this.addressLine.setInstanceID(count.incrementAndGet());

        // Create the AddressLine
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restAddressLineMockMvc.perform(
                patch(ENTITY_API_URL_ID, addressLineDTO.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAddressLine() throws Exception {
        int databaseSizeBeforeUpdate = this.addressLineRepository.findAll().size();
        this.addressLine.setInstanceID(count.incrementAndGet());

        // Create the AddressLine
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restAddressLineMockMvc.perform(
                patch(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAddressLine() throws Exception {
        int databaseSizeBeforeUpdate = this.addressLineRepository.findAll().size();
        this.addressLine.setInstanceID(count.incrementAndGet());

        // Create the AddressLine
        AddressLineDTO addressLineDTO = this.addressLineMapper.toDto(this.addressLine);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restAddressLineMockMvc.perform(
                patch(ENTITY_API_URL)
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(addressLineDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the AddressLine in the database
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAddressLine() throws Exception {
        // Initialize the database
        this.addressLineRepository.saveAndFlush(this.addressLine);

        int databaseSizeBeforeDelete = this.addressLineRepository.findAll().size();

        // Delete the addressLine
        this.restAddressLineMockMvc.perform(
                delete(ENTITY_API_URL_ID, this.addressLine.getInstanceID()).with(csrf()).accept(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        List<AddressLine> addressLineList = this.addressLineRepository.findAll();
        assertThat(addressLineList).hasSize(databaseSizeBeforeDelete - 1);
    }
}
