package com.generix.legalreferential.web.rest.flux14;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.Platform;
import com.generix.legalreferential.domain.enumeration.Motif;
import com.generix.legalreferential.repository.PlatformRepository;
import com.generix.legalreferential.security.SecurityUtils;
import com.generix.legalreferential.service.dto.custom.flux14.RequestFlux14PlatformDTO;
import com.generix.legalreferential.service.mapper.flux14.RequestPlatformMapper;
import com.generix.legalreferential.web.rest.PlatformResourceIT;
import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link PlatformeFlux14Ressource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_SYNCHRONIZER" })
class PlatformeFlux14RessourceIT {

    private static final String ENTITY_API_URL = "/v1/plateforme";

    private static final String DEFAULT_PLATFORM_ID = "AAAAAAAAAA";
    private static final String UPDATED_PLATFORM_ID = "BBBBBBBBBB";

    private static final Motif DEFAULT_MOTIF = Motif.C;
    private static final Motif UPDATED_MOTIF = Motif.P;
    private static final Motif DELETE_MOTIF = Motif.S;
    public static final String SIREN_PLATEFORME = "Raison Sociale2";
    public static final long DEFAULT_ID_INSTANCE = 100L;

    @Autowired
    private PlatformRepository platformRepository;

    @Autowired
    private RequestPlatformMapper requestPlatformMapper;

    @Autowired
    private MockMvc restPlatformMockMvc;

    private Platform platform;

    private RequestFlux14PlatformDTO requestFlux14PlatformDTO;

    public static MockedStatic<SecurityUtils> mockedStatic;

    @BeforeAll
    public static void initForAllTest() {
        PlatformeFlux14RessourceIT.initStaticSecurityUtils();
    }

    private static void initStaticSecurityUtils() {
        if (mockedStatic == null) { // Prevent re-registering
            mockedStatic = Mockito.mockStatic(SecurityUtils.class);

            mockedStatic
                .when(() -> SecurityUtils.getIssuerCurrentUser())
                .thenReturn("https://auth.staging.apps.generix.biz/auth/realms/legalreferential2");
            mockedStatic.when(() -> SecurityUtils.getClientIDCurrentUser()).thenReturn("client_app");
            mockedStatic.when(() -> SecurityUtils.getPreferredUsernameCurrentUser()).thenReturn("admin");
        }
    }

    /**
     * Create an entity for this test.
     */
    public static Platform createEntity() {
        Platform platform1 = PlatformResourceIT.createEntity();
        return platform1;
    }

    /**
     * Create a RequestFlux14PlatformDTO for this test.
     */
    public static RequestFlux14PlatformDTO createDTO() {
        RequestFlux14PlatformDTO dto = new RequestFlux14PlatformDTO();
        dto.setIdInstance(DEFAULT_ID_INSTANCE);
        //dto.setPlatformID(DEFAULT_PLATFORM_ID);
        dto.setMotif(DEFAULT_MOTIF);
        return dto;
    }

    /**
     * Initialize the test.
     */
    @BeforeEach
    public void initTest() {
        platform = createEntity();
        requestFlux14PlatformDTO = createDTO();
    }

    @Test
    @Transactional
    void updatePlatformFromPpf() throws Exception {
        // Initialize the database
        platform = platformRepository.saveAndFlush(platform);

        int databaseSizeBeforeUpdate = platformRepository.findAll().size();

        requestFlux14PlatformDTO = requestPlatformMapper.toPlatformDTO(platform);
        // Update the platform
        requestFlux14PlatformDTO.setMotif(UPDATED_MOTIF);
        requestFlux14PlatformDTO.setIdInstance(DEFAULT_ID_INSTANCE);
        requestFlux14PlatformDTO.setSirenPlateforme(SIREN_PLATEFORME);

        restPlatformMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestFlux14PlatformDTO))
            )
            .andExpect(status().isOk());

        // Validate the Platform in the database
        Platform testPlatform = platformRepository.findById(platform.getInstanceID()).get();
        assertThat(testPlatform.getPlatformSiren()).isEqualTo(SIREN_PLATEFORME);
        assertThat(platformRepository.findAll()).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void updatePlatformFromPpfWithInvalidId() throws Exception {
        int databaseSizeBeforeUpdate = platformRepository.findAll().size();

        // Set an invalid ID
        requestFlux14PlatformDTO.setIdInstance(null);

        restPlatformMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestFlux14PlatformDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate that no new record was created
        assertThat(platformRepository.findAll()).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deletePlatformFromPpf_NotFound() throws Exception {
        // Initialize the database
        platformRepository.saveAndFlush(platform);

        int databaseSizeBeforeDelete = platformRepository.findAll().size();

        // Set the motif to 'S' (for delete)
        requestFlux14PlatformDTO.setMotif(Motif.S);
        requestFlux14PlatformDTO.setIdInstance(1L);
        restPlatformMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestFlux14PlatformDTO))
            )
            .andExpect(status().isNotFound());

        // Validate the database no longer contains the platform
        assertThat(platformRepository.findAll()).hasSize(databaseSizeBeforeDelete);
    }

    @Test
    @Transactional
    void deletePlatformFromPpf_Success() throws Exception {
        // Initialize the database
        platformRepository.saveAndFlush(platform);
        int databaseSizeBeforeDelete = platformRepository.findAll().size();
        // Set the motif to 'S' (for delete)
        requestFlux14PlatformDTO.setMotif(Motif.S);
        restPlatformMockMvc
            .perform(
                put(ENTITY_API_URL)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestFlux14PlatformDTO))
            )
            .andExpect(status().isOk());

        // Validate the database no longer contains the platform
        assertThat(platformRepository.findAll()).hasSize(databaseSizeBeforeDelete - 1);
    }

    @Test
    @Transactional
    void updatePlatformFromPpfWithInvalidMotif() throws Exception {
        // Set an invalid motif
        String invalidJsonPayload = "{" + "\"idInstance\": 1," + "\"platformID\": \"AAAAAAAAAA\"," + "\"motif\": \"INVALID\"" + "}";
        restPlatformMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(invalidJsonPayload))
            .andExpect(status().isBadRequest());
    }

    @AfterAll
    public static void tearDown() {
        if (PlatformeFlux14RessourceIT.mockedStatic != null) {
            PlatformeFlux14RessourceIT.mockedStatic.close();
            PlatformeFlux14RessourceIT.mockedStatic = null;
        }
    }
}
