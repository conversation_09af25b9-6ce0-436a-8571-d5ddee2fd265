package com.generix.legalreferential.web.rest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.generix.legalreferential.domain.AddressLine;
import com.generix.legalreferential.repository.AddressLineRepository;
import com.generix.legalreferential.service.*;
import com.generix.legalreferential.service.dto.*;
import com.generix.legalreferential.service.dto.custom.annuaire.InformationAdressageDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.PeriodeEffetDTO;
import com.generix.legalreferential.service.dto.custom.flux14.Flux14InformationAdressageDTO;
import com.generix.legalreferential.service.dto.custom.flux14.Flux14PeriodeEffetDTO;
import com.generix.legalreferential.service.dto.custom.flux14.RequestFlux14AdresseLigneDTO;
import com.generix.legalreferential.service.error.AnnuaireException;
import com.generix.legalreferential.service.impl.AddressLineServiceImpl;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import com.generix.legalreferential.service.mapper.AddressLineMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.persistence.EntityNotFoundException;

public class AnnuaireAddressLineResourceIT {

    @InjectMocks
    private AddressLineServiceImpl addressLineServiceImpl;

    @Mock
    private PlatformService platformService;

    @Mock
    private SirenService sirenService;

    @Mock
    private SiretService siretService;

    @Mock
    private RoutingCodeService routingCodeService;
    @Mock
    private AddressLineMapper addressLineMapper;
    @Mock
    private AddressLineRepository addressLineRepository;
    private static final String DATE_PATTERN = "yyyy-MM-dd";

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN);

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void whenSirenIsMissing() {
        var exception = assertThrows(
            AnnuaireException.class,
            () ->
                addressLineServiceImpl.validateRequestForAdresseLine(
                    null,
                    "**************",
                    "R123",
                    "P123",
                    null,
                    "2024-01-01",
                    "2024-12-31",
                    true
                )
        );
        assertEquals("sirenIsMandatory", exception.getTitle());
    }

    @Test
    void whenMatriculePlateformeIsMissing() {
        var exception = assertThrows(
            AnnuaireException.class,
            () ->
                addressLineServiceImpl.validateRequestForAdresseLine(
                    "*********",
                    "**************",
                    "R123",
                    null,
                    "Suffixe",
                    "2024-01-01",
                    "2024-12-31",
                    true
                )
        );
        assertEquals("matriculePlateformeIsMandatory", exception.getTitle());
    }

    @Test
    void whenSirenNotFound() {
        PlatformDTO mockPlatform = new PlatformDTO();
        when(sirenService.findLatestActiveSiren("*********")).thenReturn(Optional.empty());
        when(platformService.findByMatricule("MP123")).thenReturn(Optional.of(mockPlatform));
        var exception = assertThrows(
            AnnuaireException.class,
            () ->
                addressLineServiceImpl.validateRequestForAdresseLine(
                    "*********",
                    "**************",
                    "R123",
                    "MP123",
                    null,
                    "2024-01-01",
                    "2024-12-31",
                    true
                )
        );
        assertEquals("sirenNotFound", exception.getTitle());
    }

    @Test
    void whenSuffixeAndSiretAreProvided() {
        PlatformDTO mockPlatform = new PlatformDTO();
        SirenDTO mockSirenDTO = new SirenDTO();
        when(sirenService.findLatestActiveSiren("*********")).thenReturn(Optional.of(mockSirenDTO));
        when(platformService.findByMatricule("MP123")).thenReturn(Optional.of(mockPlatform));

        var exception = assertThrows(
            AnnuaireException.class,
            () ->
                addressLineServiceImpl.validateRequestForAdresseLine(
                    "*********",
                    "**************",
                    "R123",
                    "MP123",
                    "Suffixe",
                    "2039-01-01",
                    "2040-12-31",
                    true
                )
        );
        assertEquals("suffixeSiretExclusivity", exception.getTitle());
    }

    @Test
    void whenRoutingCodeIsPresentWithoutSiret() {
        PlatformDTO mockPlatform = new PlatformDTO();
        SirenDTO mockSirenDTO = new SirenDTO();
        mockSirenDTO.setSiren("*********");
        mockSirenDTO.setInstanceID((long) 1);
        SiretDTO mocksiretDTO = new SiretDTO();
        mocksiretDTO.setFkSiretSiren(mockSirenDTO);
        mocksiretDTO.setInstanceID((long) 1);
        RoutingCodeDTO mockroutingCodeDTO = new RoutingCodeDTO();
        mockroutingCodeDTO.setFkRoutingCodeSiret(mocksiretDTO);

        when(sirenService.findLatestActiveSiren("*********")).thenReturn(Optional.of(mockSirenDTO));
        when(platformService.findByMatricule("MP123")).thenReturn(Optional.of(mockPlatform));
        when(siretService.findLatestActiveSiret("**************")).thenReturn(Optional.of(mocksiretDTO));
        when(routingCodeService.findByRoutingCodeID("R123")).thenReturn(Optional.of(mockroutingCodeDTO));

        var exception = assertThrows(
            AnnuaireException.class,
            () ->
                addressLineServiceImpl.validateRequestForAdresseLine(
                    "*********",
                    null,
                    "R123",
                    "MP123",
                    null,
                    "2024-01-01",
                    "2024-12-31",
                    true
                )
        );
        assertEquals("routingCodeWithoutSiret", exception.getTitle());
    }

    @Test
    void whenBeginDateIsAfterEndDate() {
        PlatformDTO mockPlatform = new PlatformDTO();
        SirenDTO mockSirenDTO = new SirenDTO();
        mockSirenDTO.setSiren("*********");
        mockSirenDTO.setInstanceID((long) 1);
        SiretDTO mocksiretDTO = new SiretDTO();
        mocksiretDTO.setFkSiretSiren(mockSirenDTO);
        mocksiretDTO.setInstanceID((long) 1);
        RoutingCodeDTO mockroutingCodeDTO = new RoutingCodeDTO();
        mockroutingCodeDTO.setFkRoutingCodeSiret(mocksiretDTO);

        when(sirenService.findLatestActiveSiren("*********")).thenReturn(Optional.of(mockSirenDTO));
        when(platformService.findByMatricule("MP123")).thenReturn(Optional.of(mockPlatform));
        when(siretService.findLatestActiveSiret("**************")).thenReturn(Optional.of(mocksiretDTO));
        when(routingCodeService.findByRoutingCodeID("R123")).thenReturn(Optional.of(mockroutingCodeDTO));
        var exception = assertThrows(
            AnnuaireException.class,
            () ->
                addressLineServiceImpl.validateRequestForAdresseLine(
                    "*********",
                    "**************",
                    "R123",
                    "MP123",
                    null,
                    "2024-12-31",
                    "2024-01-01",
                    true
                )
        );
        assertEquals("dateDebutGreaterorEqualsThenToday", exception.getTitle());
    }

    @Test
    void whenAddressLineIsNotUniqueForPeriod() {
        // Mock objects
        PlatformDTO mockPlatform = new PlatformDTO();
        SirenDTO mockSirenDTO = new SirenDTO();
        mockSirenDTO.setSiren("*********");
        mockSirenDTO.setInstanceID(1L);

        SiretDTO mockSiretDTO = new SiretDTO();
        mockSiretDTO.setFkSiretSiren(mockSirenDTO);
        mockSiretDTO.setInstanceID(1L);

        RoutingCodeDTO mockRoutingCodeDTO = new RoutingCodeDTO();
        mockRoutingCodeDTO.setFkRoutingCodeSiret(mockSiretDTO);

        // Mocked services behavior
        when(sirenService.findLatestActiveSiren("*********")).thenReturn(Optional.of(mockSirenDTO));
        when(platformService.findByMatricule("MP123")).thenReturn(Optional.of(mockPlatform));
        when(siretService.findLatestActiveSiret("**************")).thenReturn(Optional.of(mockSiretDTO));
        when(routingCodeService.findByRoutingCodeID("R123")).thenReturn(Optional.of(mockRoutingCodeDTO));
        List<Long> mockInstanceIds = Arrays.asList(1L, 2L, 3L);
        when(
            addressLineRepository.findMatchingInstanceIds(
                eq("*********"),
                isNull(),
                eq(null),
                eq(null),
                eq(LocalDate.of(2024, 1, 1)),
                eq(LocalDate.of(2024, 12, 31)),
                eq("MP123")
            )
        )
            .thenReturn(mockInstanceIds);

        assertFalse(
            addressLineServiceImpl.isUniqueForPeriod(
                "*********",
                null,
                null,
                null,
                LocalDate.of(2024, 1, 1),
                LocalDate.of(2024, 12, 31),
                "MP123"
            )
        );

        var exception = assertThrows(
            AnnuaireException.class,
            () ->
                addressLineServiceImpl.validateRequestForAdresseLine(
                    "*********",
                    null,
                    null,
                    "MP123",
                    null,
                    LocalDate.of(2024, 1, 1).toString(),
                    LocalDate.of(2024, 12, 31).toString(),
                    true
                )
        );
        assertEquals("notUniqueAddressLine", exception.getTitle());
    }

    @Test
    void whenDeletingExistingAddressLine_thenSuccess() {
        Long idInstance = 1L;
        AddressLine mockAddressLine = new AddressLine();
        mockAddressLine.setInstanceID(idInstance);
        mockAddressLine.setEffectBeginDate(LocalDate.now().plusDays(1)); // Future date allows deletion

        when(addressLineRepository.findById(idInstance)).thenReturn(Optional.of(mockAddressLine));

        assertDoesNotThrow(() -> addressLineServiceImpl.deleteAnnuaireAddresseLine("FR", idInstance));

        verify(addressLineRepository).delete(mockAddressLine);
    }

    @Test
    void whenDeletingNonExistingAddressLine() {
        Long idInstance = 2L;

        when(addressLineRepository.findById(idInstance)).thenReturn(Optional.empty());

        var exception = assertThrows(
            EntityNotFoundException.class,  // Expecting EntityNotFoundException
            () -> addressLineServiceImpl.deleteAnnuaireAddresseLine("FR", idInstance)
        );

        assertEquals("Annuaire Adresse Line not found for id-instance: " + idInstance, exception.getMessage());
    }



    @Test
    void whenDeletingAddressLineWithPastOrTodayEffectDate() {
        Long idInstance = 3L;
        AddressLine mockAddressLine = new AddressLine();
        mockAddressLine.setInstanceID(idInstance);
        mockAddressLine.setEffectBeginDate(LocalDate.now()); // Today, so it shouldn't be deleted

        when(addressLineRepository.findById(idInstance)).thenReturn(Optional.of(mockAddressLine));

        var exception = assertThrows(
            AnnuaireException.class,
            () -> addressLineServiceImpl.deleteAnnuaireAddresseLine("FR", idInstance)
        );

        assertEquals(
            "Suppression_impossible: Impossible de supprimer une ligne d'adresse déjà commencée",
            exception.getMessage()
        );
    }
}
