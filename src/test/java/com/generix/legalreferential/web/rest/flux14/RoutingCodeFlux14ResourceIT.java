package com.generix.legalreferential.web.rest.flux14;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.RoutingCode;
import com.generix.legalreferential.domain.Siret;
import com.generix.legalreferential.domain.enumeration.Motif;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.repository.RoutingCodeRepository;
import com.generix.legalreferential.repository.SiretRepository;
import com.generix.legalreferential.security.SecurityUtils;
import com.generix.legalreferential.service.dto.custom.flux14.RequestFlux14RoutingCodeDTO;
import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link RoutingCodeFlux14Resource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_SYNCHRONIZER" })
class RoutingCodeFlux14ResourceIT {

    private static final Long DEFAULT_ID_INSTANCE = 100L;
    private static final String DEFAULT_ROUTING_CODE_LABEL = "Routing Label";
    private static final String DEFAULT_ROUTING_CODE_ID = "AAAAAAAAAA";
    private static final Statut DEFAULT_STATUS = Statut.A;
    private static final Motif DEFAULT_MOTIF = Motif.C;

    private static final String DEFAULT_ISSUER = "https://auth.staging.apps.generix.biz/auth/realms/legalreferential2";
    private static final String DEFAULT_CLIENTID = "legalref_client";
    private static final String DEFAULT_OWNER = "legalref_admin";
    public static final String API_FLUX14_ROUTING_CODE = "/v1/code-routage";

    @Autowired
    private MockMvc restMockMvc;

    @Autowired
    private RoutingCodeRepository routingCodeRepository;

    @Autowired
    private SiretRepository siretRepository;

    private static MockedStatic<SecurityUtils> mockedStatic;

    @BeforeAll
    public static void initForAllTest() {
        mockedStatic = Mockito.mockStatic(SecurityUtils.class);
        mockedStatic.when(SecurityUtils::getIssuerCurrentUser).thenReturn(DEFAULT_ISSUER);
        mockedStatic.when(SecurityUtils::getClientIDCurrentUser).thenReturn(DEFAULT_CLIENTID);
        mockedStatic.when(SecurityUtils::getPreferredUsernameCurrentUser).thenReturn(DEFAULT_OWNER);
    }

    private RequestFlux14RoutingCodeDTO createValidRequestDTO() {
        RequestFlux14RoutingCodeDTO requestDTO = new RequestFlux14RoutingCodeDTO();
        requestDTO.setIdInstance(DEFAULT_ID_INSTANCE);
        requestDTO.setIdentifiantRoutage(DEFAULT_ROUTING_CODE_ID);
        requestDTO.setLibelleCodeRoutage(DEFAULT_ROUTING_CODE_LABEL);
        requestDTO.setSiret(SiretFlux14ResourceIT.DEFAULT_SIRET);
        requestDTO.setMotif(DEFAULT_MOTIF);
        requestDTO.setStatut(DEFAULT_STATUS);
        return requestDTO;
    }

    private RoutingCode createExistingRoutingCode() {
        return new RoutingCode()
            .instanceID(DEFAULT_ID_INSTANCE)
            .routingCodeID(DEFAULT_ROUTING_CODE_ID)
            .status(DEFAULT_STATUS)
            .routingCodeLabel(DEFAULT_ROUTING_CODE_LABEL)
            .routingCodeType("TYPE")
            .administrativeStatus("ACTIVE")
            .legalEngagementManagement(true)
            .addressLine1("Address Line 1")
            .addressLine2("Address Line 2")
            .addressLine3("Address Line 3")
            .addressPostalCode("75001")
            .addressCity("Paris")
            .addressCountrySubdivision("Ile-de-France")
            .addressCountryCode("FR")
            .addressCountryLabel("FR");
    }

    @Test
    @Transactional
    void updateRoutingCode_Success() throws Exception {
        Siret siretEntity = SiretFlux14ResourceIT.createSiretEntity();
        siretEntity = siretRepository.saveAndFlush(siretEntity);
        RoutingCode existingRoutingCode = createExistingRoutingCode();
        existingRoutingCode.setFkRoutingCodeSiret(siretEntity);
        routingCodeRepository.saveAndFlush(existingRoutingCode);

        RequestFlux14RoutingCodeDTO requestDTO = createValidRequestDTO();
        requestDTO.setLibelleCodeRoutage("Updated Label");

        restMockMvc
            .perform(
                put(API_FLUX14_ROUTING_CODE)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestDTO))
            )
            .andExpect(status().isOk());

        RoutingCode updatedRoutingCode = routingCodeRepository.findByRoutingCodeID(DEFAULT_ROUTING_CODE_ID).orElseThrow();
        assertThat(updatedRoutingCode.getRoutingCodeLabel()).isEqualTo("Updated Label");
        assertThat(updatedRoutingCode.getRoutingCodeID()).isEqualTo(DEFAULT_ROUTING_CODE_ID);
    }

    @Test
    @Transactional
    void updateRoutingCode_BadRequest_MissingIdInstance() throws Exception {
        RequestFlux14RoutingCodeDTO requestDTO = createValidRequestDTO();
        requestDTO.setIdInstance(null);

        restMockMvc
            .perform(
                put(API_FLUX14_ROUTING_CODE)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestDTO))
            )
            .andExpect(status().isBadRequest());
    }

    @Test
    @Transactional
    void updateRoutingCode_NotFound() throws Exception {
        RequestFlux14RoutingCodeDTO requestDTO = createValidRequestDTO();

        restMockMvc
            .perform(
                put(API_FLUX14_ROUTING_CODE)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestDTO))
            )
            .andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void updateRoutingCode_DeleteSuccess() throws Exception {
        Siret siretEntity = SiretFlux14ResourceIT.createSiretEntity();
        siretEntity = siretRepository.saveAndFlush(siretEntity);
        RoutingCode existingRoutingCode = createExistingRoutingCode();
        existingRoutingCode.setFkRoutingCodeSiret(siretEntity);
        routingCodeRepository.saveAndFlush(existingRoutingCode);

        RequestFlux14RoutingCodeDTO requestDTO = createValidRequestDTO();
        requestDTO.setMotif(Motif.S);

        restMockMvc
            .perform(
                put(API_FLUX14_ROUTING_CODE)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestDTO))
            )
            .andExpect(status().isOk());

        assertThat(routingCodeRepository.findByRoutingCodeID(DEFAULT_ROUTING_CODE_ID)).isEmpty();
    }

    @AfterAll
    public static void tearDown() {
        if (RoutingCodeFlux14ResourceIT.mockedStatic != null) {
            RoutingCodeFlux14ResourceIT.mockedStatic.close();
            RoutingCodeFlux14ResourceIT.mockedStatic = null;
        }
    }
}
