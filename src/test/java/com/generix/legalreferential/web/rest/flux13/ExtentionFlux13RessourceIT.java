package com.generix.legalreferential.web.rest.flux13;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.testng.AssertJUnit.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.Tracking;
import com.generix.legalreferential.domain.enumeration.NatureEtablissement;
import com.generix.legalreferential.domain.enumeration.Operation;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.repository.TrackingRepository;
import com.generix.legalreferential.service.ExtentionFlux13Service;
import com.generix.legalreferential.service.dto.TrackingDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.AnnuaireAdresseDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.InformationAdressageDTO;
import com.generix.legalreferential.service.dto.custom.annuaire.PeriodeEffetDTO;
import com.generix.legalreferential.service.dto.custom.flux13.*;
import com.generix.legalreferential.web.rest.TestUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_ADMIN", "LEGALREF_CUSTOMER" })
class ExtentionFlux13RessourceIT {

    private static final String ENTITY_API_URL = "/lignes-annuaire-et-codes-routage";
    private static final String SIREN_DEMANDEUR_HEADER = "Siren-Demandeur";
    private static final String REQUEST_ID_HEADER = "Request-Id";
    private static final String DEFAULT_SIREN_DEMANDEUR = "702042755";
    private static final UUID REQUEST_ID = UUID.randomUUID();
    private static final String INVALID_SIREN_DEMANDEUR = "invalidSIREN";

    @Autowired
    private TrackingRepository trackingRepository;

    @Autowired
    private MockMvc restMockMvc;

    @MockBean
    private ExtentionFlux13Service extentionFlux13Service;

    @Autowired
    private ObjectMapper objectMapper;

    private AnnuaireActualisationRequestDTO annuaireActualisationRequestDTO;

    /**
     * Sets up the validator for validation before each test.
     * This method is executed before each test method to initialize the
     * validator used for constraint validation.
     */
    @NotNull
    private static LigneAnnuaireRequestDTO getLigneAnnuaireRequestDTO() {
        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = new LigneAnnuaireRequestDTO();
        ligneAnnuaireRequestDTO.setOperation(Operation.Creation);
        PeriodeEffetDTO periodeEffetDTO = new PeriodeEffetDTO();
        periodeEffetDTO.setDateDebutEffet("2023-01-01");
        periodeEffetDTO.setDateFinEffet("2023-01-31");
        ligneAnnuaireRequestDTO.setPeriodeEffet(periodeEffetDTO);
        InformationAdressageDTO informationAdressageDTO = new InformationAdressageDTO();
        informationAdressageDTO.setSiren("702042755");
        informationAdressageDTO.setSiret("70204275500240");
        informationAdressageDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
        informationAdressageDTO.setSuffixeAdressage("00000");
        informationAdressageDTO.setMatriculePlateforme("0145");
        ligneAnnuaireRequestDTO.setInformationAdressage(informationAdressageDTO);
        return ligneAnnuaireRequestDTO;
    }

    /**
     * Creates and returns a {@link CodeRoutageRequestDTO} with predefined valid data.
     *
     * @return a valid instance of {@link CodeRoutageRequestDTO}
     */
    @NotNull
    private static CodeRoutageRequestDTO getCodeRoutageRequestDTO() {
        CodeRoutageRequestDTO codeRoutageRequestDTO = new CodeRoutageRequestDTO();
        codeRoutageRequestDTO.setNatureEtablissement(NatureEtablissement.Privée);
        codeRoutageRequestDTO.setIdentifiantRoutage("dcsc456sdcsdcs556");
        codeRoutageRequestDTO.setSiret("70204275500240");
        codeRoutageRequestDTO.setTypeIdentifiantRoutage("0224");
        codeRoutageRequestDTO.setLibelleCodeRoutage("Libellé Code routage");
        codeRoutageRequestDTO.setGestionEngagementJuridique(true);
        codeRoutageRequestDTO.setEtatAdministratif(Statut.A);
        AnnuaireAdresseDTO annuaireAdresseDTO = new AnnuaireAdresseDTO();
        annuaireAdresseDTO.setLigneAdresse1("16 BIS RUE HENRI BARBUSSE");
        annuaireAdresseDTO.setLigneAdresse2("CEDEX 1");
        annuaireAdresseDTO.setLigneAdresse3("Bâtiment le Callipso");
        annuaireAdresseDTO.setCodePostal("38100");
        annuaireAdresseDTO.setSubDivisionPays("Bretagne");
        annuaireAdresseDTO.setLocalite("Grenoble");
        annuaireAdresseDTO.setCodePays("FR");
        annuaireAdresseDTO.setLibellePays("France");
        codeRoutageRequestDTO.setAdresse(annuaireAdresseDTO);
        return codeRoutageRequestDTO;
    }

    private void createValidRequestDTO() {
        annuaireActualisationRequestDTO = new AnnuaireActualisationRequestDTO();

        List<CodeRoutageRequestDTO> validCodesRoutage = new ArrayList<>();
        CodeRoutageRequestDTO codeRoutageRequestDTO = getCodeRoutageRequestDTO();
        validCodesRoutage.add(codeRoutageRequestDTO);

        List<LigneAnnuaireRequestDTO> validLignesAdressage = new ArrayList<>();
        LigneAnnuaireRequestDTO ligneAnnuaireRequestDTO = getLigneAnnuaireRequestDTO();
        validLignesAdressage.add(ligneAnnuaireRequestDTO);

        annuaireActualisationRequestDTO.setCodesRoutage(validCodesRoutage);
        annuaireActualisationRequestDTO.setLignesAdressage(validLignesAdressage);
    }

    @BeforeAll
    public static void setUpAllTest() {
        TestUtil.initStaticSecurityUtils();
    }

    @BeforeEach
    public void setUp() {
        createValidRequestDTO();
    }

    @Test
    void testSuccess() throws Exception {
        // Given
        String validSirenDemandeur = DEFAULT_SIREN_DEMANDEUR;
        AnnuaireActualisationRequestDTO requestDTO = annuaireActualisationRequestDTO;
        int trackingTableSizeBeforeCreate = this.trackingRepository.findAll().size();

        // Mock service call
        TrackingDTO mockTrackingDTO = new TrackingDTO();
        mockTrackingDTO.setRequestId(UUID.randomUUID());

        // When
        MvcResult result = restMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .header(SIREN_DEMANDEUR_HEADER, validSirenDemandeur)
                    .header(REQUEST_ID_HEADER, REQUEST_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestDTO))
            )
            .andExpect(status().isAccepted()) // 202 status
            .andReturn();

        List<Tracking> trackingList = this.trackingRepository.findAll();
        assertTrue(trackingList.size() == trackingTableSizeBeforeCreate + 1);

        String jsonResponse = result.getResponse().getContentAsString();
        ObjectMapper objectMapper = new ObjectMapper();
        AnnuaireActualisationResponseDTO responseDTO = objectMapper.readValue(jsonResponse, AnnuaireActualisationResponseDTO.class);

        Tracking tracking = trackingList.get(trackingList.size() - 1);
        assertTrue(tracking.getRequestId().equals(responseDTO.getIdRequete()));
    }

    @Test
    void testMissingSirenDemandeur() throws Exception {
        // Given
        AnnuaireActualisationRequestDTO requestDTO = annuaireActualisationRequestDTO;
        // When
        MvcResult result = restMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .header(REQUEST_ID_HEADER, REQUEST_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestDTO))
            )
            .andExpect(status().isBadRequest()) // 400 status if Siren-Demandeur is missing
            .andReturn();

        JSONObject jsonObject = new JSONObject(result.getResponse().getContentAsString());
        assertTrue(jsonObject.getInt("statut") == HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void testInvalidSirenDemandeur() throws Exception {
        // Given an invalid SIREN
        String invalidSirenDemandeur = INVALID_SIREN_DEMANDEUR;
        AnnuaireActualisationRequestDTO requestDTO = annuaireActualisationRequestDTO;
        // When
        MvcResult result = restMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .header(SIREN_DEMANDEUR_HEADER, invalidSirenDemandeur)
                    .header(REQUEST_ID_HEADER, REQUEST_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(requestDTO))
            )
            .andExpect(status().isBadRequest())
            .andReturn();

        JSONObject jsonObject = new JSONObject(result.getResponse().getContentAsString());
        assertTrue(jsonObject.getInt("statut") == HttpStatus.BAD_REQUEST.value());
    }

    @Test
    void testInternalServerError() throws Exception {
        // Given
        String validSirenDemandeur = DEFAULT_SIREN_DEMANDEUR;
        AnnuaireActualisationRequestDTO requestDTO = annuaireActualisationRequestDTO;

        when(extentionFlux13Service.actualisationAnnuaire(eq(requestDTO), eq(validSirenDemandeur)))
            .thenThrow(new RuntimeException("Internal server error"));

        // When
        MvcResult result = restMockMvc
            .perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .header(SIREN_DEMANDEUR_HEADER, validSirenDemandeur)
                    .header(REQUEST_ID_HEADER, REQUEST_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(requestDTO))
            )
            .andExpect(status().isInternalServerError()) // 500 status for service failure
            .andReturn();

        JSONObject jsonObject = new JSONObject(result.getResponse().getContentAsString());
        assertTrue(jsonObject.getInt("statut") == HttpStatus.INTERNAL_SERVER_ERROR.value());
    }
}
