package com.generix.legalreferential.web.rest.flux14;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.Siren;
import com.generix.legalreferential.domain.enumeration.Motif;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.domain.enumeration.TypeEntiteDestinataire;
import com.generix.legalreferential.repository.SirenRepository;
import com.generix.legalreferential.security.SecurityUtils;
import com.generix.legalreferential.service.dto.custom.flux14.RequestFlux14SirenDTO;
import com.generix.legalreferential.service.dto.custom.flux14.TypeEntiteDestinataireFlux14DTO;
import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link SirenFlux14Resource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_SYNCHRONIZER" })
class SirenFlux14ResourceIT {

    private static final Long DEFAULT_ID_INSTANCE = 12345L;
    private static final String DEFAULT_SIREN = "*********";
    private static final String DEFAULT_RAISON_SOCIALE = "Company ABC";
    private static final TypeEntiteDestinataire DEFAULT_TYPE_ENTITE = TypeEntiteDestinataire.PUBLIQUE;
    private static final Statut DEFAULT_STATUS = Statut.A;
    private static final Motif DEFAULT_MOTIF = Motif.C;

    @Autowired
    private MockMvc restMockMvc;

    @Autowired
    private SirenRepository sirenRepository;

    @BeforeAll
    public static void initForAllTest() {
        SirenFlux14ResourceIT.initStaticSecurityUtils();
    }

    public static MockedStatic<SecurityUtils> mockedStatic;

    private RequestFlux14SirenDTO createValidRequestDTO() {
        RequestFlux14SirenDTO requestDTO = new RequestFlux14SirenDTO();
        requestDTO.setIdInstance(DEFAULT_ID_INSTANCE);
        requestDTO.setSiren(DEFAULT_SIREN);
        requestDTO.setRaisonSociale(DEFAULT_RAISON_SOCIALE);
        requestDTO.setTypeEntite(TypeEntiteDestinataireFlux14DTO.P);
        requestDTO.setStatut(DEFAULT_STATUS);
        requestDTO.setMotif(DEFAULT_MOTIF);
        return requestDTO;
    }

    private Siren createExistingSiren() {
        Siren siren = new Siren();
        siren.setInstanceID(DEFAULT_ID_INSTANCE);
        siren.setSiren(DEFAULT_SIREN);
        siren.setCompanyName(DEFAULT_RAISON_SOCIALE);
        siren.setEntityType(DEFAULT_TYPE_ENTITE);
        siren.setStatus(DEFAULT_STATUS);
        return siren;
    }

    private static void initStaticSecurityUtils() {
        if (mockedStatic == null) { // Prevent re-registering
            mockedStatic = Mockito.mockStatic(SecurityUtils.class);

            mockedStatic
                .when(() -> SecurityUtils.getIssuerCurrentUser())
                .thenReturn("https://auth.staging.apps.generix.biz/auth/realms/legalreferential2");
            mockedStatic.when(() -> SecurityUtils.getClientIDCurrentUser()).thenReturn("client_app");
            mockedStatic.when(() -> SecurityUtils.getPreferredUsernameCurrentUser()).thenReturn("admin");
        }
    }

    @Test
    @Transactional
    void patchSiren_Success() throws Exception {
        Siren existingSiren = createExistingSiren();
        sirenRepository.saveAndFlush(existingSiren);

        // Create the Request DTO
        RequestFlux14SirenDTO requestDTO = createValidRequestDTO();

        restMockMvc
            .perform(
                put("/v1/siren").with(csrf()).contentType(MediaType.APPLICATION_JSON).content(TestUtil.convertObjectToJsonBytes(requestDTO))
            )
            .andExpect(status().isOk());

        // Validate the Siren in the database
        Siren updatedSiren = sirenRepository.findById(DEFAULT_ID_INSTANCE).orElseThrow();
        assertThat(updatedSiren.getSiren()).isEqualTo(DEFAULT_SIREN);
        assertThat(updatedSiren.getCompanyName()).isEqualTo(DEFAULT_RAISON_SOCIALE);
        assertThat(updatedSiren.getEntityType()).isEqualTo(DEFAULT_TYPE_ENTITE);
        assertThat(updatedSiren.getStatus()).isEqualTo(DEFAULT_STATUS);
    }

    @Test
    @Transactional
    void patchSiren_BadRequest_MissingIdInstance() throws Exception {
        // Create an invalid Request DTO (missing idInstance)
        RequestFlux14SirenDTO requestDTO = createValidRequestDTO();
        requestDTO.setIdInstance(null);
        restMockMvc
            .perform(put("/v1/siren").contentType(MediaType.APPLICATION_JSON).content(TestUtil.convertObjectToJsonBytes(requestDTO)))
            .andExpect(status().isBadRequest());
    }

    @Test
    @Transactional
    void patchSiren_NotFound() throws Exception {
        // Create the Request DTO for a non-existent Siren
        RequestFlux14SirenDTO requestDTO = createValidRequestDTO();
        ResultActions resultActions = restMockMvc.perform(
            put("/v1/siren").contentType(MediaType.APPLICATION_JSON).content(TestUtil.convertObjectToJsonBytes(requestDTO))
        );

        resultActions.andExpect(status().isOk()); // Assuming it creates a deletion alert when the record is not found
    }

    //TODO IT CASE for the case of delete
    //TODO IT CASE for the case of UPDATE

    @AfterAll
    public static void tearDown() {
        if (SirenFlux14ResourceIT.mockedStatic != null) {
            SirenFlux14ResourceIT.mockedStatic.close();
            SirenFlux14ResourceIT.mockedStatic = null;
        }
    }
}
