package com.generix.legalreferential.web.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.AddressLine;
import com.generix.legalreferential.domain.Platform;
import com.generix.legalreferential.domain.Siren;
import com.generix.legalreferential.domain.enumeration.PlatformType;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.repository.PlatformRepository;
import com.generix.legalreferential.repository.SirenRepository;
import com.generix.legalreferential.security.SecurityUtils;
import com.generix.legalreferential.service.dto.PlatformDTO;
import com.generix.legalreferential.service.mapper.PlatformMapper;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link PlatformResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_ADMIN" })
public class PlatformResourceIT {

    private static final String DEFAULT_PLATFORM_ID = "AAAAAAAAAA";
    private static final String UPDATED_PLATFORM_ID = "BBBBBBBBBB";

    private static final PlatformType DEFAULT_PLATFORM_TYPE = PlatformType.PPF;
    private static final PlatformType UPDATED_PLATFORM_TYPE = PlatformType.PDP;

    private static final Statut DEFAULT_PLATFORM_STATUS = Statut.A;
    private static final Statut UPDATED_PLATFORM_STATUS = Statut.I;

    private static final String DEFAULT_PLATFORM_SIREN = "AAAAAAAAAA";
    private static final String UPDATED_PLATFORM_SIREN = "BBBBBBBBBB";

    private static final String DEFAULT_PLATFORM_COMPANY_NAME = "AAAAAAAAAA";
    private static final String UPDATED_PLATFORM_COMPANY_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_PLATFORM_COMMERCIAL_NAME = "AAAAAAAAAA";
    private static final String UPDATED_PLATFORM_COMMERCIAL_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_PLATFORM_CONTACT_OR_URL = "AAAAAAAAAA";
    private static final String UPDATED_PLATFORM_CONTACT_OR_URL = "BBBBBBBBBB";
    private static final String PLATFORM_MATRICULE = "CCCCCCCCCCCC";

    private static final LocalDate DEFAULT_REGISTRATION_BEGIN_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_REGISTRATION_BEGIN_DATE = LocalDate.now(ZoneId.systemDefault());
    private static final LocalDate SMALLER_REGISTRATION_BEGIN_DATE = LocalDate.ofEpochDay(-1L);

    private static final LocalDate DEFAULT_REGISTRATION_END_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_REGISTRATION_END_DATE = LocalDate.now(ZoneId.systemDefault());
    private static final LocalDate SMALLER_REGISTRATION_END_DATE = LocalDate.ofEpochDay(-1L);

    private static final String ENTITY_API_URL = "/api/platforms";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static final String DEFAULT_ISSUER = "https://auth.staging.apps.generix.biz/auth/realms/legalreferential2";
    private static final String DEFAULT_CLIENTID = "legalref_client";
    private static final String DEFAULT_OWNER = "legalref_admin";

    private static Random random = new Random();
    private static AtomicLong count = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private PlatformRepository platformRepository;

    @Autowired
    private PlatformMapper platformMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restPlatformMockMvc;

    @Autowired
    private SirenRepository sirenRepository;

    public static MockedStatic<SecurityUtils> mockedStatic;

    private Platform platform;

    /**
     * Create an entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it, if they test an entity
     * which requires the current entity.
     */
    public static Platform createEntity() {
        Platform platform = new Platform()
            .platformID(DEFAULT_PLATFORM_ID)
            .platformType(DEFAULT_PLATFORM_TYPE)
            .platformStatus(DEFAULT_PLATFORM_STATUS)
            .platformSiren(DEFAULT_PLATFORM_SIREN)
            .platformCompanyName(DEFAULT_PLATFORM_COMPANY_NAME)
            .platformCommercialName(DEFAULT_PLATFORM_COMMERCIAL_NAME)
            .platformContactOrUrl(DEFAULT_PLATFORM_CONTACT_OR_URL)
            .registrationBeginDate(DEFAULT_REGISTRATION_BEGIN_DATE)
            .registrationEndDate(DEFAULT_REGISTRATION_END_DATE);
        platform.setPlatformMatricule(PLATFORM_MATRICULE);
        platform.setIssuer(PlatformResourceIT.DEFAULT_ISSUER);
        platform.setClientID(PlatformResourceIT.DEFAULT_CLIENTID);
        platform.setOwner(PlatformResourceIT.DEFAULT_OWNER);
        platform.setInstanceID(100L);
        return platform;
    }

    /**
     * Create an updated entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it, if they test an entity
     * which requires the current entity.
     */
    public static Platform createUpdatedEntity(EntityManager em) {
        Platform platform = new Platform()
            .platformID(UPDATED_PLATFORM_ID)
            .platformType(UPDATED_PLATFORM_TYPE)
            .platformStatus(UPDATED_PLATFORM_STATUS)
            .platformSiren(UPDATED_PLATFORM_SIREN)
            .platformCompanyName(UPDATED_PLATFORM_COMPANY_NAME)
            .platformCommercialName(UPDATED_PLATFORM_COMMERCIAL_NAME)
            .platformContactOrUrl(UPDATED_PLATFORM_CONTACT_OR_URL)
            .registrationBeginDate(UPDATED_REGISTRATION_BEGIN_DATE)
            .registrationEndDate(UPDATED_REGISTRATION_END_DATE);
        return platform;
    }

    @AfterAll
    public static void teardown() {
        mockedStatic.close();
    }

    @BeforeAll
    public static void initForAllTest() {
        initStaticSecurityUtils();
    }

    private static void initStaticSecurityUtils() {
        mockedStatic = Mockito.mockStatic(SecurityUtils.class);

        mockedStatic
            .when(() -> SecurityUtils.getIssuerCurrentUser())
            .thenReturn("https://auth.staging.apps.generix.biz/auth/realms/legalreferential2");
        mockedStatic.when(() -> SecurityUtils.getClientIDCurrentUser()).thenReturn("client_app");
        mockedStatic.when(() -> SecurityUtils.getPreferredUsernameCurrentUser()).thenReturn("admin");
    }

    @BeforeEach
    public void initTest() {
        this.platform = createEntity();
    }

    @Test
    @Transactional
    void createPlatform() throws Exception {
        int databaseSizeBeforeCreate = this.platformRepository.findAll().size();
        // Create the Platform
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);
        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isCreated());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeCreate + 1);
        Platform testPlatform = platformList.get(platformList.size() - 1);
        assertThat(testPlatform.getPlatformID()).isEqualTo(DEFAULT_PLATFORM_ID);
        assertThat(testPlatform.getPlatformType()).isEqualTo(DEFAULT_PLATFORM_TYPE);
        assertThat(testPlatform.getPlatformStatus()).isEqualTo(DEFAULT_PLATFORM_STATUS);
        assertThat(testPlatform.getPlatformSiren()).isEqualTo(DEFAULT_PLATFORM_SIREN);
        assertThat(testPlatform.getPlatformCompanyName()).isEqualTo(DEFAULT_PLATFORM_COMPANY_NAME);
        assertThat(testPlatform.getPlatformCommercialName()).isEqualTo(DEFAULT_PLATFORM_COMMERCIAL_NAME);
        assertThat(testPlatform.getPlatformContactOrUrl()).isEqualTo(DEFAULT_PLATFORM_CONTACT_OR_URL);
        assertThat(testPlatform.getRegistrationBeginDate()).isEqualTo(DEFAULT_REGISTRATION_BEGIN_DATE);
        assertThat(testPlatform.getRegistrationEndDate()).isEqualTo(DEFAULT_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void createPlatformWithExistingId() throws Exception {
        // Create the Platform with an existing ID
        this.platform.setInstanceID(null);
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        int databaseSizeBeforeCreate = this.platformRepository.findAll().size();

        // An entity with an existing ID cannot be created, so this API call must fail
        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkPlatformTypeIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.platformRepository.findAll().size();
        // set the field null
        this.platform.setPlatformType(null);

        // Create the Platform, which fails.
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPlatformStatusIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.platformRepository.findAll().size();
        // set the field null
        this.platform.setPlatformStatus(null);

        // Create the Platform, which fails.
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPlatformSirenIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.platformRepository.findAll().size();
        // set the field null
        this.platform.setPlatformSiren(null);

        // Create the Platform, which fails.
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPlatformCompanyNameIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.platformRepository.findAll().size();
        // set the field null
        this.platform.setPlatformCompanyName(null);

        // Create the Platform, which fails.
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPlatformCommercialNameIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.platformRepository.findAll().size();
        // set the field null
        this.platform.setPlatformCommercialName(null);

        // Create the Platform, which fails.
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPlatformContactOrUrlIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.platformRepository.findAll().size();
        // set the field null
        this.platform.setPlatformContactOrUrl(null);

        // Create the Platform, which fails.
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRegistrationBeginDateIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.platformRepository.findAll().size();
        // set the field null
        this.platform.setRegistrationBeginDate(null);

        // Create the Platform, which fails.
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRegistrationEndDateIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.platformRepository.findAll().size();
        // set the field null
        this.platform.setRegistrationEndDate(null);

        // Create the Platform, which fails.
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        this.restPlatformMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllPlatforms() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList
        this.restPlatformMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.platform.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].platformID").value(hasItem(DEFAULT_PLATFORM_ID)))
            .andExpect(jsonPath("$.[*].platformType").value(hasItem(DEFAULT_PLATFORM_TYPE.toString())))
            .andExpect(jsonPath("$.[*].platformStatus").value(hasItem(DEFAULT_PLATFORM_STATUS.toString())))
            .andExpect(jsonPath("$.[*].platformSiren").value(hasItem(DEFAULT_PLATFORM_SIREN)))
            .andExpect(jsonPath("$.[*].platformCompanyName").value(hasItem(DEFAULT_PLATFORM_COMPANY_NAME)))
            .andExpect(jsonPath("$.[*].platformCommercialName").value(hasItem(DEFAULT_PLATFORM_COMMERCIAL_NAME)))
            .andExpect(jsonPath("$.[*].platformContactOrUrl").value(hasItem(DEFAULT_PLATFORM_CONTACT_OR_URL)))
            .andExpect(jsonPath("$.[*].registrationBeginDate").value(hasItem(DEFAULT_REGISTRATION_BEGIN_DATE.toString())))
            .andExpect(jsonPath("$.[*].registrationEndDate").value(hasItem(DEFAULT_REGISTRATION_END_DATE.toString())));
    }

    @Test
    @Transactional
    void getPlatform() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get the platform
        this.restPlatformMockMvc.perform(get(ENTITY_API_URL_ID, this.platform.getInstanceID()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.instanceID").value(this.platform.getInstanceID().intValue()))
            .andExpect(jsonPath("$.platformID").value(DEFAULT_PLATFORM_ID))
            .andExpect(jsonPath("$.platformType").value(DEFAULT_PLATFORM_TYPE.toString()))
            .andExpect(jsonPath("$.platformStatus").value(DEFAULT_PLATFORM_STATUS.toString()))
            .andExpect(jsonPath("$.platformSiren").value(DEFAULT_PLATFORM_SIREN))
            .andExpect(jsonPath("$.platformCompanyName").value(DEFAULT_PLATFORM_COMPANY_NAME))
            .andExpect(jsonPath("$.platformCommercialName").value(DEFAULT_PLATFORM_COMMERCIAL_NAME))
            .andExpect(jsonPath("$.platformContactOrUrl").value(DEFAULT_PLATFORM_CONTACT_OR_URL))
            .andExpect(jsonPath("$.registrationBeginDate").value(DEFAULT_REGISTRATION_BEGIN_DATE.toString()))
            .andExpect(jsonPath("$.registrationEndDate").value(DEFAULT_REGISTRATION_END_DATE.toString()));
    }

    @Test
    @Transactional
    void getPlatformsByIdFiltering() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        Long id = this.platform.getInstanceID();

        defaultPlatformShouldBeFound("instanceID.equals=" + id);
        defaultPlatformShouldNotBeFound("instanceID.notEquals=" + id);

        defaultPlatformShouldBeFound("instanceID.greaterThanOrEqual=" + id);
        defaultPlatformShouldNotBeFound("instanceID.greaterThan=" + id);

        defaultPlatformShouldBeFound("instanceID.lessThanOrEqual=" + id);
        defaultPlatformShouldNotBeFound("instanceID.lessThan=" + id);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformIDIsEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformID equals to DEFAULT_PLATFORM_ID
        defaultPlatformShouldBeFound("platformID.equals=" + DEFAULT_PLATFORM_ID);

        // Get all the platformList where platformID equals to UPDATED_PLATFORM_ID
        defaultPlatformShouldNotBeFound("platformID.equals=" + UPDATED_PLATFORM_ID);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformIDIsInShouldWork() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformID in DEFAULT_PLATFORM_ID or UPDATED_PLATFORM_ID
        defaultPlatformShouldBeFound("platformID.in=" + DEFAULT_PLATFORM_ID + "," + UPDATED_PLATFORM_ID);

        // Get all the platformList where platformID equals to UPDATED_PLATFORM_ID
        defaultPlatformShouldNotBeFound("platformID.in=" + UPDATED_PLATFORM_ID);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformIDIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformID is not null
        defaultPlatformShouldBeFound("platformID.specified=true");

        // Get all the platformList where platformID is null
        defaultPlatformShouldNotBeFound("platformID.specified=false");
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformIDContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformID contains DEFAULT_PLATFORM_ID
        defaultPlatformShouldBeFound("platformID.contains=" + DEFAULT_PLATFORM_ID);

        // Get all the platformList where platformID contains UPDATED_PLATFORM_ID
        defaultPlatformShouldNotBeFound("platformID.contains=" + UPDATED_PLATFORM_ID);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformIDNotContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformID does not contain DEFAULT_PLATFORM_ID
        defaultPlatformShouldNotBeFound("platformID.doesNotContain=" + DEFAULT_PLATFORM_ID);

        // Get all the platformList where platformID does not contain UPDATED_PLATFORM_ID
        defaultPlatformShouldBeFound("platformID.doesNotContain=" + UPDATED_PLATFORM_ID);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformTypeIsEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformType equals to DEFAULT_PLATFORM_TYPE
        defaultPlatformShouldBeFound("platformType.equals=" + DEFAULT_PLATFORM_TYPE);

        // Get all the platformList where platformType equals to UPDATED_PLATFORM_TYPE
        defaultPlatformShouldNotBeFound("platformType.equals=" + UPDATED_PLATFORM_TYPE);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformTypeIsInShouldWork() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformType in DEFAULT_PLATFORM_TYPE or UPDATED_PLATFORM_TYPE
        defaultPlatformShouldBeFound("platformType.in=" + DEFAULT_PLATFORM_TYPE + "," + UPDATED_PLATFORM_TYPE);

        // Get all the platformList where platformType equals to UPDATED_PLATFORM_TYPE
        defaultPlatformShouldNotBeFound("platformType.in=" + UPDATED_PLATFORM_TYPE);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformTypeIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformType is not null
        defaultPlatformShouldBeFound("platformType.specified=true");

        // Get all the platformList where platformType is null
        defaultPlatformShouldNotBeFound("platformType.specified=false");
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformStatusIsEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformStatus equals to DEFAULT_PLATFORM_STATUS
        defaultPlatformShouldBeFound("platformStatus.equals=" + DEFAULT_PLATFORM_STATUS);

        // Get all the platformList where platformStatus equals to UPDATED_PLATFORM_STATUS
        defaultPlatformShouldNotBeFound("platformStatus.equals=" + UPDATED_PLATFORM_STATUS);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformStatusIsInShouldWork() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformStatus in DEFAULT_PLATFORM_STATUS or UPDATED_PLATFORM_STATUS
        defaultPlatformShouldBeFound("platformStatus.in=" + DEFAULT_PLATFORM_STATUS + "," + UPDATED_PLATFORM_STATUS);

        // Get all the platformList where platformStatus equals to UPDATED_PLATFORM_STATUS
        defaultPlatformShouldNotBeFound("platformStatus.in=" + UPDATED_PLATFORM_STATUS);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformStatusIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformStatus is not null
        defaultPlatformShouldBeFound("platformStatus.specified=true");

        // Get all the platformList where platformStatus is null
        defaultPlatformShouldNotBeFound("platformStatus.specified=false");
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformStatusContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformStatus contains DEFAULT_PLATFORM_STATUS
        defaultPlatformShouldBeFound("platformStatus.equals=" + DEFAULT_PLATFORM_STATUS);

        // Get all the platformList where platformStatus contains UPDATED_PLATFORM_STATUS
        defaultPlatformShouldNotBeFound("platformStatus.equals=" + UPDATED_PLATFORM_STATUS);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformStatusNotContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformStatus does not contain DEFAULT_PLATFORM_STATUS
        defaultPlatformShouldNotBeFound("platformStatus.equals=" + DEFAULT_PLATFORM_STATUS);

        // Get all the platformList where platformStatus does not contain UPDATED_PLATFORM_STATUS
        defaultPlatformShouldBeFound("platformStatus.equals=" + UPDATED_PLATFORM_STATUS);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformSirenIsEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformSiren equals to DEFAULT_PLATFORM_SIREN
        defaultPlatformShouldBeFound("platformSiren.equals=" + DEFAULT_PLATFORM_SIREN);

        // Get all the platformList where platformSiren equals to UPDATED_PLATFORM_SIREN
        defaultPlatformShouldNotBeFound("platformSiren.equals=" + UPDATED_PLATFORM_SIREN);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformSirenIsInShouldWork() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformSiren in DEFAULT_PLATFORM_SIREN or UPDATED_PLATFORM_SIREN
        defaultPlatformShouldBeFound("platformSiren.in=" + DEFAULT_PLATFORM_SIREN + "," + UPDATED_PLATFORM_SIREN);

        // Get all the platformList where platformSiren equals to UPDATED_PLATFORM_SIREN
        defaultPlatformShouldNotBeFound("platformSiren.in=" + UPDATED_PLATFORM_SIREN);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformSirenIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformSiren is not null
        defaultPlatformShouldBeFound("platformSiren.specified=true");

        // Get all the platformList where platformSiren is null
        defaultPlatformShouldNotBeFound("platformSiren.specified=false");
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformSirenContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformSiren contains DEFAULT_PLATFORM_SIREN
        defaultPlatformShouldBeFound("platformSiren.contains=" + DEFAULT_PLATFORM_SIREN);

        // Get all the platformList where platformSiren contains UPDATED_PLATFORM_SIREN
        defaultPlatformShouldNotBeFound("platformSiren.contains=" + UPDATED_PLATFORM_SIREN);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformSirenNotContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformSiren does not contain DEFAULT_PLATFORM_SIREN
        defaultPlatformShouldNotBeFound("platformSiren.doesNotContain=" + DEFAULT_PLATFORM_SIREN);

        // Get all the platformList where platformSiren does not contain UPDATED_PLATFORM_SIREN
        defaultPlatformShouldBeFound("platformSiren.doesNotContain=" + UPDATED_PLATFORM_SIREN);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCompanyNameIsEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCompanyName equals to DEFAULT_PLATFORM_COMPANY_NAME
        defaultPlatformShouldBeFound("platformCompanyName.equals=" + DEFAULT_PLATFORM_COMPANY_NAME);

        // Get all the platformList where platformCompanyName equals to UPDATED_PLATFORM_COMPANY_NAME
        defaultPlatformShouldNotBeFound("platformCompanyName.equals=" + UPDATED_PLATFORM_COMPANY_NAME);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCompanyNameIsInShouldWork() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCompanyName in DEFAULT_PLATFORM_COMPANY_NAME or UPDATED_PLATFORM_COMPANY_NAME
        defaultPlatformShouldBeFound("platformCompanyName.in=" + DEFAULT_PLATFORM_COMPANY_NAME + "," + UPDATED_PLATFORM_COMPANY_NAME);

        // Get all the platformList where platformCompanyName equals to UPDATED_PLATFORM_COMPANY_NAME
        defaultPlatformShouldNotBeFound("platformCompanyName.in=" + UPDATED_PLATFORM_COMPANY_NAME);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCompanyNameIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCompanyName is not null
        defaultPlatformShouldBeFound("platformCompanyName.specified=true");

        // Get all the platformList where platformCompanyName is null
        defaultPlatformShouldNotBeFound("platformCompanyName.specified=false");
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCompanyNameContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCompanyName contains DEFAULT_PLATFORM_COMPANY_NAME
        defaultPlatformShouldBeFound("platformCompanyName.contains=" + DEFAULT_PLATFORM_COMPANY_NAME);

        // Get all the platformList where platformCompanyName contains UPDATED_PLATFORM_COMPANY_NAME
        defaultPlatformShouldNotBeFound("platformCompanyName.contains=" + UPDATED_PLATFORM_COMPANY_NAME);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCompanyNameNotContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCompanyName does not contain DEFAULT_PLATFORM_COMPANY_NAME
        defaultPlatformShouldNotBeFound("platformCompanyName.doesNotContain=" + DEFAULT_PLATFORM_COMPANY_NAME);

        // Get all the platformList where platformCompanyName does not contain UPDATED_PLATFORM_COMPANY_NAME
        defaultPlatformShouldBeFound("platformCompanyName.doesNotContain=" + UPDATED_PLATFORM_COMPANY_NAME);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCommercialNameIsEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCommercialName equals to DEFAULT_PLATFORM_COMMERCIAL_NAME
        defaultPlatformShouldBeFound("platformCommercialName.equals=" + DEFAULT_PLATFORM_COMMERCIAL_NAME);

        // Get all the platformList where platformCommercialName equals to UPDATED_PLATFORM_COMMERCIAL_NAME
        defaultPlatformShouldNotBeFound("platformCommercialName.equals=" + UPDATED_PLATFORM_COMMERCIAL_NAME);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCommercialNameIsInShouldWork() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCommercialName in DEFAULT_PLATFORM_COMMERCIAL_NAME or UPDATED_PLATFORM_COMMERCIAL_NAME
        defaultPlatformShouldBeFound(
            "platformCommercialName.in=" + DEFAULT_PLATFORM_COMMERCIAL_NAME + "," + UPDATED_PLATFORM_COMMERCIAL_NAME
        );

        // Get all the platformList where platformCommercialName equals to UPDATED_PLATFORM_COMMERCIAL_NAME
        defaultPlatformShouldNotBeFound("platformCommercialName.in=" + UPDATED_PLATFORM_COMMERCIAL_NAME);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCommercialNameIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCommercialName is not null
        defaultPlatformShouldBeFound("platformCommercialName.specified=true");

        // Get all the platformList where platformCommercialName is null
        defaultPlatformShouldNotBeFound("platformCommercialName.specified=false");
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCommercialNameContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCommercialName contains DEFAULT_PLATFORM_COMMERCIAL_NAME
        defaultPlatformShouldBeFound("platformCommercialName.contains=" + DEFAULT_PLATFORM_COMMERCIAL_NAME);

        // Get all the platformList where platformCommercialName contains UPDATED_PLATFORM_COMMERCIAL_NAME
        defaultPlatformShouldNotBeFound("platformCommercialName.contains=" + UPDATED_PLATFORM_COMMERCIAL_NAME);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformCommercialNameNotContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformCommercialName does not contain DEFAULT_PLATFORM_COMMERCIAL_NAME
        defaultPlatformShouldNotBeFound("platformCommercialName.doesNotContain=" + DEFAULT_PLATFORM_COMMERCIAL_NAME);

        // Get all the platformList where platformCommercialName does not contain UPDATED_PLATFORM_COMMERCIAL_NAME
        defaultPlatformShouldBeFound("platformCommercialName.doesNotContain=" + UPDATED_PLATFORM_COMMERCIAL_NAME);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformContactOrUrlIsEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformContactOrUrl equals to DEFAULT_PLATFORM_CONTACT_OR_URL
        defaultPlatformShouldBeFound("platformContactOrUrl.equals=" + DEFAULT_PLATFORM_CONTACT_OR_URL);

        // Get all the platformList where platformContactOrUrl equals to UPDATED_PLATFORM_CONTACT_OR_URL
        defaultPlatformShouldNotBeFound("platformContactOrUrl.equals=" + UPDATED_PLATFORM_CONTACT_OR_URL);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformContactOrUrlIsInShouldWork() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformContactOrUrl in DEFAULT_PLATFORM_CONTACT_OR_URL or UPDATED_PLATFORM_CONTACT_OR_URL
        defaultPlatformShouldBeFound("platformContactOrUrl.in=" + DEFAULT_PLATFORM_CONTACT_OR_URL + "," + UPDATED_PLATFORM_CONTACT_OR_URL);

        // Get all the platformList where platformContactOrUrl equals to UPDATED_PLATFORM_CONTACT_OR_URL
        defaultPlatformShouldNotBeFound("platformContactOrUrl.in=" + UPDATED_PLATFORM_CONTACT_OR_URL);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformContactOrUrlIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformContactOrUrl is not null
        defaultPlatformShouldBeFound("platformContactOrUrl.specified=true");

        // Get all the platformList where platformContactOrUrl is null
        defaultPlatformShouldNotBeFound("platformContactOrUrl.specified=false");
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformContactOrUrlContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformContactOrUrl contains DEFAULT_PLATFORM_CONTACT_OR_URL
        defaultPlatformShouldBeFound("platformContactOrUrl.contains=" + DEFAULT_PLATFORM_CONTACT_OR_URL);

        // Get all the platformList where platformContactOrUrl contains UPDATED_PLATFORM_CONTACT_OR_URL
        defaultPlatformShouldNotBeFound("platformContactOrUrl.contains=" + UPDATED_PLATFORM_CONTACT_OR_URL);
    }

    @Test
    @Transactional
    void getAllPlatformsByPlatformContactOrUrlNotContainsSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where platformContactOrUrl does not contain DEFAULT_PLATFORM_CONTACT_OR_URL
        defaultPlatformShouldNotBeFound("platformContactOrUrl.doesNotContain=" + DEFAULT_PLATFORM_CONTACT_OR_URL);

        // Get all the platformList where platformContactOrUrl does not contain UPDATED_PLATFORM_CONTACT_OR_URL
        defaultPlatformShouldBeFound("platformContactOrUrl.doesNotContain=" + UPDATED_PLATFORM_CONTACT_OR_URL);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationBeginDateIsEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationBeginDate equals to DEFAULT_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldBeFound("registrationBeginDate.equals=" + DEFAULT_REGISTRATION_BEGIN_DATE);

        // Get all the platformList where registrationBeginDate equals to UPDATED_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldNotBeFound("registrationBeginDate.equals=" + UPDATED_REGISTRATION_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationBeginDateIsInShouldWork() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationBeginDate in DEFAULT_REGISTRATION_BEGIN_DATE or UPDATED_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldBeFound("registrationBeginDate.in=" + DEFAULT_REGISTRATION_BEGIN_DATE + "," + UPDATED_REGISTRATION_BEGIN_DATE);

        // Get all the platformList where registrationBeginDate equals to UPDATED_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldNotBeFound("registrationBeginDate.in=" + UPDATED_REGISTRATION_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationBeginDateIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationBeginDate is not null
        defaultPlatformShouldBeFound("registrationBeginDate.specified=true");

        // Get all the platformList where registrationBeginDate is null
        defaultPlatformShouldNotBeFound("registrationBeginDate.specified=false");
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationBeginDateIsGreaterThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationBeginDate is greater than or equal to DEFAULT_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldBeFound("registrationBeginDate.greaterThanOrEqual=" + DEFAULT_REGISTRATION_BEGIN_DATE);

        // Get all the platformList where registrationBeginDate is greater than or equal to UPDATED_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldNotBeFound("registrationBeginDate.greaterThanOrEqual=" + UPDATED_REGISTRATION_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationBeginDateIsLessThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationBeginDate is less than or equal to DEFAULT_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldBeFound("registrationBeginDate.lessThanOrEqual=" + DEFAULT_REGISTRATION_BEGIN_DATE);

        // Get all the platformList where registrationBeginDate is less than or equal to SMALLER_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldNotBeFound("registrationBeginDate.lessThanOrEqual=" + SMALLER_REGISTRATION_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationBeginDateIsLessThanSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationBeginDate is less than DEFAULT_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldNotBeFound("registrationBeginDate.lessThan=" + DEFAULT_REGISTRATION_BEGIN_DATE);

        // Get all the platformList where registrationBeginDate is less than UPDATED_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldBeFound("registrationBeginDate.lessThan=" + UPDATED_REGISTRATION_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationBeginDateIsGreaterThanSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationBeginDate is greater than DEFAULT_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldNotBeFound("registrationBeginDate.greaterThan=" + DEFAULT_REGISTRATION_BEGIN_DATE);

        // Get all the platformList where registrationBeginDate is greater than SMALLER_REGISTRATION_BEGIN_DATE
        defaultPlatformShouldBeFound("registrationBeginDate.greaterThan=" + SMALLER_REGISTRATION_BEGIN_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationEndDateIsEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationEndDate equals to DEFAULT_REGISTRATION_END_DATE
        defaultPlatformShouldBeFound("registrationEndDate.equals=" + DEFAULT_REGISTRATION_END_DATE);

        // Get all the platformList where registrationEndDate equals to UPDATED_REGISTRATION_END_DATE
        defaultPlatformShouldNotBeFound("registrationEndDate.equals=" + UPDATED_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationEndDateIsInShouldWork() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationEndDate in DEFAULT_REGISTRATION_END_DATE or UPDATED_REGISTRATION_END_DATE
        defaultPlatformShouldBeFound("registrationEndDate.in=" + DEFAULT_REGISTRATION_END_DATE + "," + UPDATED_REGISTRATION_END_DATE);

        // Get all the platformList where registrationEndDate equals to UPDATED_REGISTRATION_END_DATE
        defaultPlatformShouldNotBeFound("registrationEndDate.in=" + UPDATED_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationEndDateIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationEndDate is not null
        defaultPlatformShouldBeFound("registrationEndDate.specified=true");

        // Get all the platformList where registrationEndDate is null
        defaultPlatformShouldNotBeFound("registrationEndDate.specified=false");
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationEndDateIsGreaterThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationEndDate is greater than or equal to DEFAULT_REGISTRATION_END_DATE
        defaultPlatformShouldBeFound("registrationEndDate.greaterThanOrEqual=" + DEFAULT_REGISTRATION_END_DATE);

        // Get all the platformList where registrationEndDate is greater than or equal to UPDATED_REGISTRATION_END_DATE
        defaultPlatformShouldNotBeFound("registrationEndDate.greaterThanOrEqual=" + UPDATED_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationEndDateIsLessThanOrEqualToSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationEndDate is less than or equal to DEFAULT_REGISTRATION_END_DATE
        defaultPlatformShouldBeFound("registrationEndDate.lessThanOrEqual=" + DEFAULT_REGISTRATION_END_DATE);

        // Get all the platformList where registrationEndDate is less than or equal to SMALLER_REGISTRATION_END_DATE
        defaultPlatformShouldNotBeFound("registrationEndDate.lessThanOrEqual=" + SMALLER_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationEndDateIsLessThanSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationEndDate is less than DEFAULT_REGISTRATION_END_DATE
        defaultPlatformShouldNotBeFound("registrationEndDate.lessThan=" + DEFAULT_REGISTRATION_END_DATE);

        // Get all the platformList where registrationEndDate is less than UPDATED_REGISTRATION_END_DATE
        defaultPlatformShouldBeFound("registrationEndDate.lessThan=" + UPDATED_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByRegistrationEndDateIsGreaterThanSomething() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        // Get all the platformList where registrationEndDate is greater than DEFAULT_REGISTRATION_END_DATE
        defaultPlatformShouldNotBeFound("registrationEndDate.greaterThan=" + DEFAULT_REGISTRATION_END_DATE);

        // Get all the platformList where registrationEndDate is greater than SMALLER_REGISTRATION_END_DATE
        defaultPlatformShouldBeFound("registrationEndDate.greaterThan=" + SMALLER_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void getAllPlatformsByPkAddressLineIsEqualToSomething() throws Exception {
        AddressLine pkAddressLine;
        if (TestUtil.findAll(this.em, AddressLine.class).isEmpty()) {
            this.platformRepository.saveAndFlush(this.platform);
            pkAddressLine = AddressLineResourceIT.createEntity(this.em);
            Siren siren = this.sirenRepository.saveAndFlush(SirenResourceIT.createEntity(this.em));
            pkAddressLine.setFkAddressLineSiren(siren);
        } else {
            pkAddressLine = TestUtil.findAll(this.em, AddressLine.class).get(0);
        }
        this.em.persist(pkAddressLine);
        this.em.flush();
        this.platform.addPkAddressLine(pkAddressLine);
        this.platformRepository.saveAndFlush(this.platform);
        Long pkAddressLineId = pkAddressLine.getInstanceID();

        // Get all the platformList where pkAddressLine equals to pkAddressLineId
        defaultPlatformShouldBeFound("pkAddressLineId.equals=" + pkAddressLineId);

        // Get all the platformList where pkAddressLine equals to (pkAddressLineId + 1)
        defaultPlatformShouldNotBeFound("pkAddressLineId.equals=" + (pkAddressLineId + 1));
    }

    /**
     * Executes the search, and checks that the default entity is returned.
     */
    private void defaultPlatformShouldBeFound(String filter) throws Exception {
        this.restPlatformMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.platform.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].platformID").value(hasItem(DEFAULT_PLATFORM_ID)))
            .andExpect(jsonPath("$.[*].platformType").value(hasItem(DEFAULT_PLATFORM_TYPE.toString())))
            .andExpect(jsonPath("$.[*].platformStatus").value(hasItem(DEFAULT_PLATFORM_STATUS.toString())))
            .andExpect(jsonPath("$.[*].platformSiren").value(hasItem(DEFAULT_PLATFORM_SIREN)))
            .andExpect(jsonPath("$.[*].platformCompanyName").value(hasItem(DEFAULT_PLATFORM_COMPANY_NAME)))
            .andExpect(jsonPath("$.[*].platformCommercialName").value(hasItem(DEFAULT_PLATFORM_COMMERCIAL_NAME)))
            .andExpect(jsonPath("$.[*].platformContactOrUrl").value(hasItem(DEFAULT_PLATFORM_CONTACT_OR_URL)))
            .andExpect(jsonPath("$.[*].registrationBeginDate").value(hasItem(DEFAULT_REGISTRATION_BEGIN_DATE.toString())))
            .andExpect(jsonPath("$.[*].registrationEndDate").value(hasItem(DEFAULT_REGISTRATION_END_DATE.toString())));

        // Check, that the count call also returns 1
        this.restPlatformMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("1"));
    }

    /**
     * Executes the search, and checks that the default entity is not returned.
     */
    private void defaultPlatformShouldNotBeFound(String filter) throws Exception {
        this.restPlatformMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$").isEmpty());

        // Check, that the count call also returns 0
        this.restPlatformMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("0"));
    }

    @Test
    @Transactional
    void getNonExistingPlatform() throws Exception {
        // Get the platform
        this.restPlatformMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingPlatform() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        int databaseSizeBeforeUpdate = this.platformRepository.findAll().size();

        // Update the platform
        Platform updatedPlatform = this.platformRepository.findById(this.platform.getInstanceID()).get();
        // Disconnect from session so that the updates on updatedPlatform are not directly saved in db
        this.em.detach(updatedPlatform);
        updatedPlatform
            .platformID(UPDATED_PLATFORM_ID)
            .platformType(UPDATED_PLATFORM_TYPE)
            .platformStatus(UPDATED_PLATFORM_STATUS)
            .platformSiren(UPDATED_PLATFORM_SIREN)
            .platformCompanyName(UPDATED_PLATFORM_COMPANY_NAME)
            .platformCommercialName(UPDATED_PLATFORM_COMMERCIAL_NAME)
            .platformContactOrUrl(UPDATED_PLATFORM_CONTACT_OR_URL)
            .registrationBeginDate(UPDATED_REGISTRATION_BEGIN_DATE)
            .registrationEndDate(UPDATED_REGISTRATION_END_DATE);
        PlatformDTO platformDTO = this.platformMapper.toDto(updatedPlatform);

        this.restPlatformMockMvc.perform(
                put(ENTITY_API_URL_ID, platformDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isOk());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeUpdate);
        Platform testPlatform = platformList.get(platformList.size() - 1);
        assertThat(testPlatform.getPlatformID()).isEqualTo(UPDATED_PLATFORM_ID);
        assertThat(testPlatform.getPlatformType()).isEqualTo(UPDATED_PLATFORM_TYPE);
        assertThat(testPlatform.getPlatformStatus()).isEqualTo(UPDATED_PLATFORM_STATUS);
        assertThat(testPlatform.getPlatformSiren()).isEqualTo(UPDATED_PLATFORM_SIREN);
        assertThat(testPlatform.getPlatformCompanyName()).isEqualTo(UPDATED_PLATFORM_COMPANY_NAME);
        assertThat(testPlatform.getPlatformCommercialName()).isEqualTo(UPDATED_PLATFORM_COMMERCIAL_NAME);
        assertThat(testPlatform.getPlatformContactOrUrl()).isEqualTo(UPDATED_PLATFORM_CONTACT_OR_URL);
        assertThat(testPlatform.getRegistrationBeginDate()).isEqualTo(UPDATED_REGISTRATION_BEGIN_DATE);
        assertThat(testPlatform.getRegistrationEndDate()).isEqualTo(UPDATED_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void putNonExistingPlatform() throws Exception {
        int databaseSizeBeforeUpdate = this.platformRepository.findAll().size();
        this.platform.setInstanceID(count.incrementAndGet());

        // Create the Platform
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restPlatformMockMvc.perform(
                put(ENTITY_API_URL_ID, platformDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchPlatform() throws Exception {
        int databaseSizeBeforeUpdate = this.platformRepository.findAll().size();
        this.platform.setInstanceID(count.incrementAndGet());

        // Create the Platform
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restPlatformMockMvc.perform(
                put(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamPlatform() throws Exception {
        int databaseSizeBeforeUpdate = this.platformRepository.findAll().size();
        this.platform.setInstanceID(count.incrementAndGet());

        // Create the Platform
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restPlatformMockMvc.perform(
                put(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdatePlatformWithPatch() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        int databaseSizeBeforeUpdate = this.platformRepository.findAll().size();

        // Update the platform using partial update
        Platform partialUpdatedPlatform = new Platform();
        partialUpdatedPlatform.setInstanceID(this.platform.getInstanceID());

        partialUpdatedPlatform
            .platformID(UPDATED_PLATFORM_ID)
            .platformStatus(UPDATED_PLATFORM_STATUS)
            .platformSiren(UPDATED_PLATFORM_SIREN)
            .registrationBeginDate(UPDATED_REGISTRATION_BEGIN_DATE);

        this.restPlatformMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPlatform.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedPlatform))
            )
            .andExpect(status().isOk());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeUpdate);
        Platform testPlatform = platformList.get(platformList.size() - 1);
        assertThat(testPlatform.getPlatformID()).isEqualTo(UPDATED_PLATFORM_ID);
        assertThat(testPlatform.getPlatformType()).isEqualTo(DEFAULT_PLATFORM_TYPE);
        assertThat(testPlatform.getPlatformStatus()).isEqualTo(UPDATED_PLATFORM_STATUS);
        assertThat(testPlatform.getPlatformSiren()).isEqualTo(UPDATED_PLATFORM_SIREN);
        assertThat(testPlatform.getPlatformCompanyName()).isEqualTo(DEFAULT_PLATFORM_COMPANY_NAME);
        assertThat(testPlatform.getPlatformCommercialName()).isEqualTo(DEFAULT_PLATFORM_COMMERCIAL_NAME);
        assertThat(testPlatform.getPlatformContactOrUrl()).isEqualTo(DEFAULT_PLATFORM_CONTACT_OR_URL);
        assertThat(testPlatform.getRegistrationBeginDate()).isEqualTo(UPDATED_REGISTRATION_BEGIN_DATE);
        assertThat(testPlatform.getRegistrationEndDate()).isEqualTo(DEFAULT_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void fullUpdatePlatformWithPatch() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        int databaseSizeBeforeUpdate = this.platformRepository.findAll().size();

        // Update the platform using partial update
        Platform partialUpdatedPlatform = new Platform();
        partialUpdatedPlatform.setInstanceID(this.platform.getInstanceID());

        partialUpdatedPlatform
            .platformID(UPDATED_PLATFORM_ID)
            .platformType(UPDATED_PLATFORM_TYPE)
            .platformStatus(UPDATED_PLATFORM_STATUS)
            .platformSiren(UPDATED_PLATFORM_SIREN)
            .platformCompanyName(UPDATED_PLATFORM_COMPANY_NAME)
            .platformCommercialName(UPDATED_PLATFORM_COMMERCIAL_NAME)
            .platformContactOrUrl(UPDATED_PLATFORM_CONTACT_OR_URL)
            .registrationBeginDate(UPDATED_REGISTRATION_BEGIN_DATE)
            .registrationEndDate(UPDATED_REGISTRATION_END_DATE);

        this.restPlatformMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPlatform.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedPlatform))
            )
            .andExpect(status().isOk());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeUpdate);
        Platform testPlatform = platformList.get(platformList.size() - 1);
        assertThat(testPlatform.getPlatformID()).isEqualTo(UPDATED_PLATFORM_ID);
        assertThat(testPlatform.getPlatformType()).isEqualTo(UPDATED_PLATFORM_TYPE);
        assertThat(testPlatform.getPlatformStatus()).isEqualTo(UPDATED_PLATFORM_STATUS);
        assertThat(testPlatform.getPlatformSiren()).isEqualTo(UPDATED_PLATFORM_SIREN);
        assertThat(testPlatform.getPlatformCompanyName()).isEqualTo(UPDATED_PLATFORM_COMPANY_NAME);
        assertThat(testPlatform.getPlatformCommercialName()).isEqualTo(UPDATED_PLATFORM_COMMERCIAL_NAME);
        assertThat(testPlatform.getPlatformContactOrUrl()).isEqualTo(UPDATED_PLATFORM_CONTACT_OR_URL);
        assertThat(testPlatform.getRegistrationBeginDate()).isEqualTo(UPDATED_REGISTRATION_BEGIN_DATE);
        assertThat(testPlatform.getRegistrationEndDate()).isEqualTo(UPDATED_REGISTRATION_END_DATE);
    }

    @Test
    @Transactional
    void patchNonExistingPlatform() throws Exception {
        int databaseSizeBeforeUpdate = this.platformRepository.findAll().size();
        this.platform.setInstanceID(count.incrementAndGet());

        // Create the Platform
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restPlatformMockMvc.perform(
                patch(ENTITY_API_URL_ID, platformDTO.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchPlatform() throws Exception {
        int databaseSizeBeforeUpdate = this.platformRepository.findAll().size();
        this.platform.setInstanceID(count.incrementAndGet());

        // Create the Platform
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restPlatformMockMvc.perform(
                patch(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamPlatform() throws Exception {
        int databaseSizeBeforeUpdate = this.platformRepository.findAll().size();
        this.platform.setInstanceID(count.incrementAndGet());

        // Create the Platform
        PlatformDTO platformDTO = this.platformMapper.toDto(this.platform);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restPlatformMockMvc.perform(
                patch(ENTITY_API_URL)
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(platformDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the Platform in the database
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deletePlatform() throws Exception {
        // Initialize the database
        this.platformRepository.saveAndFlush(this.platform);

        int databaseSizeBeforeDelete = this.platformRepository.findAll().size();

        // Delete the platform
        this.restPlatformMockMvc.perform(
                delete(ENTITY_API_URL_ID, this.platform.getInstanceID()).with(csrf()).accept(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        List<Platform> platformList = this.platformRepository.findAll();
        assertThat(platformList).hasSize(databaseSizeBeforeDelete - 1);
    }
}
