package com.generix.legalreferential.web.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.generix.legalreferential.IntegrationTest;
import com.generix.legalreferential.domain.AddressLine;
import com.generix.legalreferential.domain.Platform;
import com.generix.legalreferential.domain.Siren;
import com.generix.legalreferential.domain.Siret;
import com.generix.legalreferential.domain.enumeration.Statut;
import com.generix.legalreferential.domain.enumeration.TypeEntiteDestinataire;
import com.generix.legalreferential.repository.SirenRepository;
import com.generix.legalreferential.security.SecurityUtils;
import com.generix.legalreferential.service.dto.SirenDTO;
import com.generix.legalreferential.service.mapper.SirenMapper;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link SirenResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser(authorities = { "LEGALREF_ADMIN" })
class SirenResourceIT {

    private static final String DEFAULT_SIREN = "AAAAAAAAA";
    private static final String UPDATED_SIREN = "BBBBBBBBB";

    private static final String DEFAULT_COMPANY_NAME = "AAAAAAAAAA";
    private static final String UPDATED_COMPANY_NAME = "BBBBBBBBBB";
    private static final String DEFAULT_ISSUER = "https://auth.staging.apps.generix.biz/auth/realms/legalreferential2";
    private static final String DEFAULT_CLIENTID = "legalref_client";
    private static final String DEFAULT_OWNER = "legalref_admin";

    private static final TypeEntiteDestinataire DEFAULT_ENTITY_TYPE = TypeEntiteDestinataire.PUBLIQUE;
    private static final TypeEntiteDestinataire UPDATED_ENTITY_TYPE = TypeEntiteDestinataire.ASSUJETTI;

    private static final Statut DEFAULT_STATUS = Statut.A;
    private static final Statut UPDATED_STATUS = Statut.I;

    private static final String ENTITY_API_URL = "/api/sirens";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong count = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private SirenRepository sirenRepository;

    @Autowired
    private SirenMapper sirenMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restSirenMockMvc;

    private Siren siren;
    private Platform platform;
    public static MockedStatic<SecurityUtils> mockedStatic;

    /**
     * Create an entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it, if they test an entity
     * which requires the current entity.
     */
    public static Siren createEntity(EntityManager em) {
        Siren siren = new Siren()
            .siren(DEFAULT_SIREN)
            .companyName(DEFAULT_COMPANY_NAME)
            .entityType(DEFAULT_ENTITY_TYPE)
            .status(DEFAULT_STATUS);
        siren.setIssuer(SirenResourceIT.DEFAULT_ISSUER);
        siren.setClientID(SirenResourceIT.DEFAULT_CLIENTID);
        siren.setOwner(SirenResourceIT.DEFAULT_OWNER);
        siren.setInstanceID(100L);
        return siren;
    }

    /**
     * Create an updated entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it, if they test an entity
     * which requires the current entity.
     */
    public static Siren createUpdatedEntity(EntityManager em) {
        Siren siren = new Siren()
            .siren(UPDATED_SIREN)
            .companyName(UPDATED_COMPANY_NAME)
            .entityType(UPDATED_ENTITY_TYPE)
            .status(UPDATED_STATUS);
        return siren;
    }

    @AfterAll
    public static void teardown() {
        SirenResourceIT.mockedStatic.close();
    }

    @BeforeAll
    public static void initForAllTest() {
        SirenResourceIT.initStaticSecurityUtils();
    }

    @BeforeEach
    public void initTest() {
        this.siren = createEntity(this.em);
    }

    private static void initStaticSecurityUtils() {
        SirenResourceIT.mockedStatic = Mockito.mockStatic(SecurityUtils.class);

        SirenResourceIT.mockedStatic
            .when(() -> SecurityUtils.getIssuerCurrentUser())
            .thenReturn("https://auth.staging.apps.generix.biz/auth/realms/legalreferential2");
        SirenResourceIT.mockedStatic.when(() -> SecurityUtils.getClientIDCurrentUser()).thenReturn("client_app");
        SirenResourceIT.mockedStatic.when(() -> SecurityUtils.getPreferredUsernameCurrentUser()).thenReturn("admin");
    }

    @Test
    @Transactional
    void createSiren() throws Exception {
        int databaseSizeBeforeCreate = this.sirenRepository.findAll().size();
        // Create the Siren
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);
        this.restSirenMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isCreated());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeCreate + 1);
        Siren testSiren = sirenList.get(sirenList.size() - 1);
        assertThat(testSiren.getSiren()).isEqualTo(DEFAULT_SIREN);
        assertThat(testSiren.getCompanyName()).isEqualTo(DEFAULT_COMPANY_NAME);
        assertThat(testSiren.getEntityType()).isEqualTo(DEFAULT_ENTITY_TYPE);
        assertThat(testSiren.getStatus()).isEqualTo(DEFAULT_STATUS);
    }

    @Test
    @Transactional
    void createSirenWithExistingId() throws Exception {
        // Create the Siren with an existing ID
        this.siren.setInstanceID(null);
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        int databaseSizeBeforeCreate = this.sirenRepository.findAll().size();

        // An entity with an existing ID cannot be created, so this API call must fail
        this.restSirenMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkSirenIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.sirenRepository.findAll().size();
        // set the field null
        this.siren.setSiren(null);

        // Create the Siren, which fails.
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        this.restSirenMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCompanyNameIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.sirenRepository.findAll().size();
        // set the field null
        this.siren.setCompanyName(null);

        // Create the Siren, which fails.
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        this.restSirenMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEntityTypeIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.sirenRepository.findAll().size();
        // set the field null
        this.siren.setEntityType(null);

        // Create the Siren, which fails.
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        this.restSirenMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        int databaseSizeBeforeTest = this.sirenRepository.findAll().size();
        // set the field null
        this.siren.setStatus(null);

        // Create the Siren, which fails.
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        this.restSirenMockMvc.perform(
                post(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isBadRequest());

        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllSirens() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList
        this.restSirenMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.siren.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].siren").value(hasItem(DEFAULT_SIREN)))
            .andExpect(jsonPath("$.[*].companyName").value(hasItem(DEFAULT_COMPANY_NAME)))
            .andExpect(jsonPath("$.[*].entityType").value(hasItem(DEFAULT_ENTITY_TYPE.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())));
    }

    @Test
    @Transactional
    void getSiren() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get the siren
        this.restSirenMockMvc.perform(get(ENTITY_API_URL_ID, this.siren.getInstanceID()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.instanceID").value(this.siren.getInstanceID().intValue()))
            .andExpect(jsonPath("$.siren").value(DEFAULT_SIREN))
            .andExpect(jsonPath("$.companyName").value(DEFAULT_COMPANY_NAME))
            .andExpect(jsonPath("$.entityType").value(DEFAULT_ENTITY_TYPE.toString()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()));
    }

    @Test
    @Transactional
    void getSirensByIdFiltering() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        Long id = this.siren.getInstanceID();

        defaultSirenShouldBeFound("instanceID.equals=" + id);
        defaultSirenShouldNotBeFound("instanceID.notEquals=" + id);

        defaultSirenShouldBeFound("instanceID.greaterThanOrEqual=" + id);
        defaultSirenShouldNotBeFound("instanceID.greaterThan=" + id);

        defaultSirenShouldBeFound("instanceID.lessThanOrEqual=" + id);
        defaultSirenShouldNotBeFound("instanceID.lessThan=" + id);
    }

    @Test
    @Transactional
    void getAllSirensBySirenIsEqualToSomething() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where siren equals to DEFAULT_SIREN
        defaultSirenShouldBeFound("siren.equals=" + DEFAULT_SIREN);

        // Get all the sirenList where siren equals to UPDATED_SIREN
        defaultSirenShouldNotBeFound("siren.equals=" + UPDATED_SIREN);
    }

    @Test
    @Transactional
    void getAllSirensBySirenIsInShouldWork() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where siren in DEFAULT_SIREN or UPDATED_SIREN
        defaultSirenShouldBeFound("siren.in=" + DEFAULT_SIREN + "," + UPDATED_SIREN);

        // Get all the sirenList where siren equals to UPDATED_SIREN
        defaultSirenShouldNotBeFound("siren.in=" + UPDATED_SIREN);
    }

    @Test
    @Transactional
    void getAllSirensBySirenIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where siren is not null
        defaultSirenShouldBeFound("siren.specified=true");

        // Get all the sirenList where siren is null
        defaultSirenShouldNotBeFound("siren.specified=false");
    }

    @Test
    @Transactional
    void getAllSirensBySirenContainsSomething() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where siren contains DEFAULT_SIREN
        defaultSirenShouldBeFound("siren.contains=" + DEFAULT_SIREN);

        // Get all the sirenList where siren contains UPDATED_SIREN
        defaultSirenShouldNotBeFound("siren.contains=" + UPDATED_SIREN);
    }

    @Test
    @Transactional
    void getAllSirensBySirenNotContainsSomething() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where siren does not contain DEFAULT_SIREN
        defaultSirenShouldNotBeFound("siren.doesNotContain=" + DEFAULT_SIREN);

        // Get all the sirenList where siren does not contain UPDATED_SIREN
        defaultSirenShouldBeFound("siren.doesNotContain=" + UPDATED_SIREN);
    }

    @Test
    @Transactional
    void getAllSirensByCompanyNameIsEqualToSomething() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where companyName equals to DEFAULT_COMPANY_NAME
        defaultSirenShouldBeFound("companyName.equals=" + DEFAULT_COMPANY_NAME);

        // Get all the sirenList where companyName equals to UPDATED_COMPANY_NAME
        defaultSirenShouldNotBeFound("companyName.equals=" + UPDATED_COMPANY_NAME);
    }

    @Test
    @Transactional
    void getAllSirensByCompanyNameIsInShouldWork() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where companyName in DEFAULT_COMPANY_NAME or UPDATED_COMPANY_NAME
        defaultSirenShouldBeFound("companyName.in=" + DEFAULT_COMPANY_NAME + "," + UPDATED_COMPANY_NAME);

        // Get all the sirenList where companyName equals to UPDATED_COMPANY_NAME
        defaultSirenShouldNotBeFound("companyName.in=" + UPDATED_COMPANY_NAME);
    }

    @Test
    @Transactional
    void getAllSirensByCompanyNameIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where companyName is not null
        defaultSirenShouldBeFound("companyName.specified=true");

        // Get all the sirenList where companyName is null
        defaultSirenShouldNotBeFound("companyName.specified=false");
    }

    @Test
    @Transactional
    void getAllSirensByCompanyNameContainsSomething() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where companyName contains DEFAULT_COMPANY_NAME
        defaultSirenShouldBeFound("companyName.contains=" + DEFAULT_COMPANY_NAME);

        // Get all the sirenList where companyName contains UPDATED_COMPANY_NAME
        defaultSirenShouldNotBeFound("companyName.contains=" + UPDATED_COMPANY_NAME);
    }

    @Test
    @Transactional
    void getAllSirensByCompanyNameNotContainsSomething() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where companyName does not contain DEFAULT_COMPANY_NAME
        defaultSirenShouldNotBeFound("companyName.doesNotContain=" + DEFAULT_COMPANY_NAME);

        // Get all the sirenList where companyName does not contain UPDATED_COMPANY_NAME
        defaultSirenShouldBeFound("companyName.doesNotContain=" + UPDATED_COMPANY_NAME);
    }

    @Test
    @Transactional
    void getAllSirensByEntityTypeIsEqualToSomething() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);
        // ✅ Confirm data is in the DB before API call
        long count = this.sirenRepository.count();
        System.out.println("Siren count in DB: " + count);
        // Get all the sirenList where entityType equals to DEFAULT_ENTITY_TYPE
        defaultSirenShouldBeFound("entityType.contain=" + DEFAULT_ENTITY_TYPE.name());

        // Get all the sirenList where entityType equals to UPDATED_ENTITY_TYPE
        defaultSirenShouldNotBeFound("entityType.equals=" + UPDATED_ENTITY_TYPE);
    }

    @Test
    @Transactional
    void getAllSirensByEntityTypeIsInShouldWork() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where entityType in DEFAULT_ENTITY_TYPE or UPDATED_ENTITY_TYPE
        defaultSirenShouldBeFound("entityType.in=" + DEFAULT_ENTITY_TYPE + "," + UPDATED_ENTITY_TYPE);

        // Get all the sirenList where entityType equals to UPDATED_ENTITY_TYPE
        defaultSirenShouldNotBeFound("entityType.in=" + UPDATED_ENTITY_TYPE);
    }

    @Test
    @Transactional
    void getAllSirensByEntityTypeIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where entityType is not null
        defaultSirenShouldBeFound("entityType.specified=true");

        // Get all the sirenList where entityType is null
        defaultSirenShouldNotBeFound("entityType.specified=false");
    }

    @Test
    @Transactional
    void getAllSirensByStatusIsEqualToSomething() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where status equals to DEFAULT_STATUS
        defaultSirenShouldBeFound("status.equals=" + DEFAULT_STATUS);

        // Get all the sirenList where status equals to UPDATED_STATUS
        defaultSirenShouldNotBeFound("status.equals=" + UPDATED_STATUS);
    }

    @Test
    @Transactional
    void getAllSirensByStatusIsInShouldWork() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where status in DEFAULT_STATUS or UPDATED_STATUS
        defaultSirenShouldBeFound("status.in=" + DEFAULT_STATUS + "," + UPDATED_STATUS);

        // Get all the sirenList where status equals to UPDATED_STATUS
        defaultSirenShouldNotBeFound("status.in=" + UPDATED_STATUS);
    }

    @Test
    @Transactional
    void getAllSirensByStatusIsNullOrNotNull() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        // Get all the sirenList where status is not null
        defaultSirenShouldBeFound("status.specified=true");

        // Get all the sirenList where status is null
        defaultSirenShouldNotBeFound("status.specified=false");
    }

    @Test
    @Transactional
    void getAllSirensByPkSiretIsEqualToSomething() throws Exception {
        Siret pkSiret;
        if (TestUtil.findAll(this.em, Siret.class).isEmpty()) {
            this.sirenRepository.saveAndFlush(this.siren);
            pkSiret = SiretResourceIT.createEntity(this.em);
        } else {
            pkSiret = TestUtil.findAll(this.em, Siret.class).get(0);
        }
        this.em.persist(pkSiret);
        this.em.flush();
        this.siren.addPkSiret(pkSiret);
        this.sirenRepository.saveAndFlush(this.siren);
        Long pkSiretId = pkSiret.getInstanceID();

        // Get all the sirenList where pkSiret equals to pkSiretId
        defaultSirenShouldBeFound("pkSiretId.equals=" + pkSiretId);

        // Get all the sirenList where pkSiret equals to (pkSiretId + 1)
        defaultSirenShouldNotBeFound("pkSiretId.equals=" + (pkSiretId + 1));
    }

    @Test
    @Transactional
    void getAllSirensByPkAddressLineIsEqualToSomething() throws Exception {
        AddressLine pkAddressLine;
        if (TestUtil.findAll(this.em, AddressLine.class).isEmpty()) {
            this.sirenRepository.saveAndFlush(this.siren);
            pkAddressLine = AddressLineResourceIT.createEntity(this.em);
            Siren siren = SirenResourceIT.createEntity(this.em);
            siren.setSiren("AAAAAAAA1");
            siren.setInstanceID(101L);
            this.em.persist(siren);
            Siret siret = SiretResourceIT.createEntity(this.em);
            siret.setSiret("AAAAAAAA2");
            siret.setFkSiretSiren(siren);
            siret.setInstanceID(101L);
            this.em.persist(siret);
            pkAddressLine.setFkAddressLineSiret(siret);
            this.platform = PlatformResourceIT.createEntity();
            this.em.persist(this.platform);
            pkAddressLine.setFkAddressLinePlatform(this.platform);
        } else {
            pkAddressLine = TestUtil.findAll(this.em, AddressLine.class).get(0);
        }
        this.em.persist(pkAddressLine);
        this.em.flush();
        pkAddressLine.setFkAddressLineSiret(null);
        this.siren.addPkAddressLine(pkAddressLine);
        this.sirenRepository.saveAndFlush(this.siren);
        Long pkAddressLineId = pkAddressLine.getInstanceID();

        // Get all the sirenList where pkAddressLine equals to pkAddressLineId
        defaultSirenShouldBeFound("pkAddressLineId.equals=" + pkAddressLineId);

        // Get all the sirenList where pkAddressLine equals to (pkAddressLineId + 1)
        defaultSirenShouldNotBeFound("pkAddressLineId.equals=" + (pkAddressLineId + 1));
    }

    /**
     * Executes the search, and checks that the default entity is returned.
     */
    private void defaultSirenShouldBeFound(String filter) throws Exception {
        this.restSirenMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].instanceID").value(hasItem(this.siren.getInstanceID().intValue())))
            .andExpect(jsonPath("$.[*].siren").value(hasItem(DEFAULT_SIREN)))
            .andExpect(jsonPath("$.[*].companyName").value(hasItem(DEFAULT_COMPANY_NAME)))
            .andExpect(jsonPath("$.[*].entityType").value(hasItem(DEFAULT_ENTITY_TYPE.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())));

        // Check, that the count call also returns 1
        this.restSirenMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("1"));
    }

    /**
     * Executes the search, and checks that the default entity is not returned.
     */
    private void defaultSirenShouldNotBeFound(String filter) throws Exception {
        this.restSirenMockMvc.perform(get(ENTITY_API_URL + "?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$").isEmpty());

        // Check, that the count call also returns 0
        this.restSirenMockMvc.perform(get(ENTITY_API_URL + "/count?sort=instanceID,desc&" + filter))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string("0"));
    }

    @Test
    @Transactional
    void getNonExistingSiren() throws Exception {
        // Get the siren
        this.restSirenMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingSiren() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        int databaseSizeBeforeUpdate = this.sirenRepository.findAll().size();

        // Update the siren
        Siren updatedSiren = this.sirenRepository.findById(this.siren.getInstanceID()).get();
        // Disconnect from session so that the updates on updatedSiren are not directly saved in db
        this.em.detach(updatedSiren);
        updatedSiren.siren(UPDATED_SIREN).companyName(UPDATED_COMPANY_NAME).entityType(UPDATED_ENTITY_TYPE).status(UPDATED_STATUS);
        SirenDTO sirenDTO = this.sirenMapper.toDto(updatedSiren);

        this.restSirenMockMvc.perform(
                put(ENTITY_API_URL_ID, sirenDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isOk());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeUpdate);
        Siren testSiren = sirenList.get(sirenList.size() - 1);
        assertThat(testSiren.getSiren()).isEqualTo(UPDATED_SIREN);
        assertThat(testSiren.getCompanyName()).isEqualTo(UPDATED_COMPANY_NAME);
        assertThat(testSiren.getEntityType()).isEqualTo(UPDATED_ENTITY_TYPE);
        assertThat(testSiren.getStatus()).isEqualTo(UPDATED_STATUS);
    }

    @Test
    @Transactional
    void putNonExistingSiren() throws Exception {
        int databaseSizeBeforeUpdate = this.sirenRepository.findAll().size();
        this.siren.setInstanceID(count.incrementAndGet());

        // Create the Siren
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restSirenMockMvc.perform(
                put(ENTITY_API_URL_ID, sirenDTO.getInstanceID())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchSiren() throws Exception {
        int databaseSizeBeforeUpdate = this.sirenRepository.findAll().size();
        this.siren.setInstanceID(count.incrementAndGet());

        // Create the Siren
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restSirenMockMvc.perform(
                put(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamSiren() throws Exception {
        int databaseSizeBeforeUpdate = this.sirenRepository.findAll().size();
        this.siren.setInstanceID(count.incrementAndGet());

        // Create the Siren
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restSirenMockMvc.perform(
                put(ENTITY_API_URL)
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateSirenWithPatch() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        int databaseSizeBeforeUpdate = this.sirenRepository.findAll().size();

        // Update the siren using partial update
        Siren partialUpdatedSiren = new Siren();
        partialUpdatedSiren.setInstanceID(this.siren.getInstanceID());

        partialUpdatedSiren.siren(UPDATED_SIREN).companyName(UPDATED_COMPANY_NAME).entityType(UPDATED_ENTITY_TYPE).status(UPDATED_STATUS);

        this.restSirenMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedSiren.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedSiren))
            )
            .andExpect(status().isOk());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeUpdate);
        Siren testSiren = sirenList.get(sirenList.size() - 1);
        assertThat(testSiren.getSiren()).isEqualTo(UPDATED_SIREN);
        assertThat(testSiren.getCompanyName()).isEqualTo(UPDATED_COMPANY_NAME);
        assertThat(testSiren.getEntityType()).isEqualTo(UPDATED_ENTITY_TYPE);
        assertThat(testSiren.getStatus()).isEqualTo(UPDATED_STATUS);
    }

    @Test
    @Transactional
    void fullUpdateSirenWithPatch() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        int databaseSizeBeforeUpdate = this.sirenRepository.findAll().size();

        // Update the siren using partial update
        Siren partialUpdatedSiren = new Siren();
        partialUpdatedSiren.setInstanceID(this.siren.getInstanceID());

        partialUpdatedSiren.siren(UPDATED_SIREN).companyName(UPDATED_COMPANY_NAME).entityType(UPDATED_ENTITY_TYPE).status(UPDATED_STATUS);

        this.restSirenMockMvc.perform(
                patch(ENTITY_API_URL_ID, partialUpdatedSiren.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(partialUpdatedSiren))
            )
            .andExpect(status().isOk());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeUpdate);
        Siren testSiren = sirenList.get(sirenList.size() - 1);
        assertThat(testSiren.getSiren()).isEqualTo(UPDATED_SIREN);
        assertThat(testSiren.getCompanyName()).isEqualTo(UPDATED_COMPANY_NAME);
        assertThat(testSiren.getEntityType()).isEqualTo(UPDATED_ENTITY_TYPE);
        assertThat(testSiren.getStatus()).isEqualTo(UPDATED_STATUS);
    }

    @Test
    @Transactional
    void patchNonExistingSiren() throws Exception {
        int databaseSizeBeforeUpdate = this.sirenRepository.findAll().size();
        this.siren.setInstanceID(count.incrementAndGet());

        // Create the Siren
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        this.restSirenMockMvc.perform(
                patch(ENTITY_API_URL_ID, sirenDTO.getInstanceID())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchSiren() throws Exception {
        int databaseSizeBeforeUpdate = this.sirenRepository.findAll().size();
        this.siren.setInstanceID(count.incrementAndGet());

        // Create the Siren
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restSirenMockMvc.perform(
                patch(ENTITY_API_URL_ID, count.incrementAndGet())
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamSiren() throws Exception {
        int databaseSizeBeforeUpdate = this.sirenRepository.findAll().size();
        this.siren.setInstanceID(count.incrementAndGet());

        // Create the Siren
        SirenDTO sirenDTO = this.sirenMapper.toDto(this.siren);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        this.restSirenMockMvc.perform(
                patch(ENTITY_API_URL)
                    .with(csrf())
                    .contentType("application/merge-patch+json")
                    .content(TestUtil.convertObjectToJsonBytes(sirenDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the Siren in the database
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteSiren() throws Exception {
        // Initialize the database
        this.sirenRepository.saveAndFlush(this.siren);

        int databaseSizeBeforeDelete = this.sirenRepository.findAll().size();

        // Delete the siren
        this.restSirenMockMvc.perform(delete(ENTITY_API_URL_ID, this.siren.getInstanceID()).with(csrf()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        List<Siren> sirenList = this.sirenRepository.findAll();
        assertThat(sirenList).hasSize(databaseSizeBeforeDelete - 1);
    }
}
