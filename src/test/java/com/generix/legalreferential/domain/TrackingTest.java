package com.generix.legalreferential.domain;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.generix.legalreferential.domain.enumeration.Flux13RequestStatus;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import org.hibernate.validator.HibernateValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TrackingTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.byProvider(HibernateValidator.class).configure().buildValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testValidTracking() throws SQLException {
        Tracking tracking = new Tracking();
        tracking.setRequestId(UUID.randomUUID());
        tracking.setRequestDate(LocalDateTime.now());
        tracking.setRequesterSiren("123456789"); // Valid 14-character SIREN
        tracking.setRequestContent(new javax.sql.rowset.serial.SerialBlob(new byte[] { 1, 2, 3 }));
        tracking.setRequestStatus(Flux13RequestStatus.Pending);
        tracking.setUpdatedDate(LocalDateTime.now());
        tracking.setComment("This is a valid comment.");

        Set<ConstraintViolation<Tracking>> violations = validator.validate(tracking);
        assertTrue(violations.isEmpty(), "There should be no validation errors for a valid Tracking object.");
    }

    @Test
    void testNullRequestDateShouldFail() {
        Tracking tracking = new Tracking();
        tracking.setRequestId(UUID.randomUUID());
        tracking.setRequesterSiren("12345678901234");
        tracking.setRequestStatus(Flux13RequestStatus.Pending);

        Set<ConstraintViolation<Tracking>> violations = validator.validate(tracking);
        assertFalse(violations.isEmpty(), "Validation should fail when requestDate is null.");
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("requestDate")));
    }

    @Test
    void testNullRequesterSirenShouldFail() {
        Tracking tracking = new Tracking();
        tracking.setRequestId(UUID.randomUUID());
        tracking.setRequestDate(LocalDateTime.now());
        tracking.setRequestStatus(Flux13RequestStatus.Pending);

        Set<ConstraintViolation<Tracking>> violations = validator.validate(tracking);
        assertFalse(violations.isEmpty(), "Validation should fail when requesterSiren is null.");
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("requesterSiren")));
    }

    @Test
    void testInvalidRequesterSirenLength() {
        Tracking tracking = new Tracking();
        tracking.setRequestId(UUID.randomUUID());
        tracking.setRequestDate(LocalDateTime.now());
        tracking.setRequesterSiren("123456789012345"); // 15 characters, invalid
        tracking.setRequestStatus(Flux13RequestStatus.Pending);

        Set<ConstraintViolation<Tracking>> violations = validator.validate(tracking);
        assertFalse(violations.isEmpty(), "Validation should fail for an invalid requesterSiren length.");
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("requesterSiren")));
    }

    @Test
    void testNullRequestStatusShouldFail() {
        Tracking tracking = new Tracking();
        tracking.setRequestId(UUID.randomUUID());
        tracking.setRequestDate(LocalDateTime.now());
        tracking.setRequesterSiren("12345678901234");

        Set<ConstraintViolation<Tracking>> violations = validator.validate(tracking);
        assertFalse(violations.isEmpty(), "Validation should fail when requestStatus is null.");
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("requestStatus")));
    }

    @Test
    void testCommentMaxLength() {
        Tracking tracking = new Tracking();
        tracking.setRequestId(UUID.randomUUID());
        tracking.setRequestDate(LocalDateTime.now());
        tracking.setRequesterSiren("12345678901234");
        tracking.setRequestStatus(Flux13RequestStatus.Pending);
        tracking.setComment("A".repeat(256)); // 256 characters, invalid

        Set<ConstraintViolation<Tracking>> violations = validator.validate(tracking);
        assertFalse(violations.isEmpty(), "Validation should fail when comment exceeds 255 characters.");
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("comment")));
    }
}
