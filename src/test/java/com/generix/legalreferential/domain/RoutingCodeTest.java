package com.generix.legalreferential.domain;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class RoutingCodeTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(RoutingCode.class);
        RoutingCode routingCode1 = new RoutingCode();
        routingCode1.setInstanceID(1L);
        RoutingCode routingCode2 = new RoutingCode();
        routingCode2.setInstanceID(routingCode1.getInstanceID());
        assertThat(routingCode1).isEqualTo(routingCode2);
        routingCode2.setInstanceID(2L);
        assertThat(routingCode1).isNotEqualTo(routingCode2);
        routingCode1.setInstanceID(null);
        assertThat(routingCode1).isNotEqualTo(routingCode2);
    }
}
