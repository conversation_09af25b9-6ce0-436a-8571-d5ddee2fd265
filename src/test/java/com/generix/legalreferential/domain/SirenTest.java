package com.generix.legalreferential.domain;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class SirenTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Siren.class);
        Siren siren1 = new Siren();
        siren1.setInstanceID(1L);
        Siren siren2 = new Siren();
        siren2.setInstanceID(siren1.getInstanceID());
        assertThat(siren1).isEqualTo(siren2);
        siren2.setInstanceID(2L);
        assertThat(siren1).isNotEqualTo(siren2);
        siren1.setInstanceID(null);
        assertThat(siren1).isNotEqualTo(siren2);
    }
}
