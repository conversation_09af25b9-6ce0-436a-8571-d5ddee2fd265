package com.generix.legalreferential.domain;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class SiretTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Siret.class);
        Siret siret1 = new Siret();
        siret1.setInstanceID(1L);
        Siret siret2 = new Siret();
        siret2.setInstanceID(siret1.getInstanceID());
        assertThat(siret1).isEqualTo(siret2);
        siret2.setInstanceID(2L);
        assertThat(siret1).isNotEqualTo(siret2);
        siret1.setInstanceID(null);
        assertThat(siret1).isNotEqualTo(siret2);
    }
}
