package com.generix.legalreferential.domain;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AddressLineTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(AddressLine.class);
        AddressLine addressLine1 = new AddressLine();
        addressLine1.setInstanceID(1L);
        AddressLine addressLine2 = new AddressLine();
        addressLine2.setInstanceID(addressLine1.getInstanceID());
        assertThat(addressLine1).isEqualTo(addressLine2);
        addressLine2.setInstanceID(2L);
        assertThat(addressLine1).isNotEqualTo(addressLine2);
        addressLine1.setInstanceID(null);
        assertThat(addressLine1).isNotEqualTo(addressLine2);
    }
}
