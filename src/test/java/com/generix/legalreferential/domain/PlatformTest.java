package com.generix.legalreferential.domain;

import static org.assertj.core.api.Assertions.assertThat;

import com.generix.legalreferential.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PlatformTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Platform.class);
        Platform platform1 = new Platform();
        platform1.setInstanceID(1L);
        Platform platform2 = new Platform();
        platform2.setInstanceID(platform1.getInstanceID());
        assertThat(platform1).isEqualTo(platform2);
        platform2.setInstanceID(2L);
        assertThat(platform1).isNotEqualTo(platform2);
        platform1.setInstanceID(null);
        assertThat(platform1).isNotEqualTo(platform2);
    }
}
