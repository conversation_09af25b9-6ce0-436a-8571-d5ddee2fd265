{"applications": "*", "changelogDate": "20240820105904", "clientRootFolder": "legalReferential", "databaseType": "sql", "dto": "mapstruct", "embedded": false, "entityTableName": "address_line", "fields": [{"fieldName": "addressLineID", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "125"}, {"fieldName": "addressSuffix", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "effectBeginDate", "fieldType": "LocalDate", "fieldValidateRules": ["required"]}, {"fieldName": "effectEndDate", "fieldType": "LocalDate", "fieldValidateRules": ["required"]}, {"fieldName": "creationDate", "fieldType": "LocalDate", "fieldValidateRules": ["required"]}, {"fieldName": "creationBy", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "lastModificationDate", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"fieldName": "modifiedBy", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "50"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "microserviceName": "legalReferential", "name": "AddressLine", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "routingCode", "otherEntityRelationshipName": "pkAddressLine", "relationshipName": "fkAddressLineRoutingCode", "relationshipType": "many-to-one"}, {"otherEntityName": "siren", "otherEntityRelationshipName": "pkAddressLine", "relationshipName": "fkAddressLineSiren", "relationshipType": "many-to-one"}, {"otherEntityName": "siret", "otherEntityRelationshipName": "pkAddressLine", "relationshipName": "fkAddressLineSiret", "relationshipType": "many-to-one"}, {"otherEntityName": "platform", "otherEntityRelationshipName": "pkAddressLine", "relationshipName": "fkAddressLinePlatform", "relationshipType": "many-to-one"}], "service": "serviceImpl"}