{"applications": "*", "changelogDate": "20240820105905", "clientRootFolder": "legalReferential", "databaseType": "sql", "dto": "mapstruct", "embedded": false, "entityTableName": "platform", "fields": [{"fieldName": "platformID", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "platformType", "fieldType": "PlatformType", "fieldValidateRules": ["required"], "fieldValues": "PPF,PDP"}, {"fieldName": "platformStatus", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "platformSiren", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "platformCompanyName", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "platformCommercialName", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "platformContactOrUrl", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "registrationBeginDate", "fieldType": "LocalDate", "fieldValidateRules": ["required"]}, {"fieldName": "registrationEndDate", "fieldType": "LocalDate", "fieldValidateRules": ["required"]}], "fluentMethods": true, "jpaMetamodelFiltering": true, "microserviceName": "legalReferential", "name": "Platform", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "addressLine", "otherEntityRelationshipName": "fkAddressLinePlatform", "relationshipName": "pkAddressLine", "relationshipType": "one-to-many"}], "service": "serviceImpl"}