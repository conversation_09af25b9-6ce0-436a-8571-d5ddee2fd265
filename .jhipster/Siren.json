{"applications": "*", "changelogDate": "20240820105901", "clientRootFolder": "legalReferential", "databaseType": "sql", "dto": "mapstruct", "embedded": false, "entityTableName": "siren", "fields": [{"fieldName": "siren", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "9"}, {"fieldName": "companyName", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "entityType", "fieldType": "TypeEntiteDestinataire", "fieldValidateRules": ["required"], "fieldValues": "PUBLIQUE,ASSUJETTI"}, {"fieldName": "status", "fieldType": "Statut", "fieldValidateRules": ["required"], "fieldValues": "A (ACTIF),I (INACTIF)"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "microserviceName": "legalReferential", "name": "<PERSON><PERSON>", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "siret", "otherEntityRelationshipName": "fkSiretSiren", "relationshipName": "pkSiret", "relationshipType": "one-to-many"}, {"otherEntityName": "addressLine", "otherEntityRelationshipName": "fkAddressLineSiren", "relationshipName": "pkAddressLine", "relationshipType": "one-to-many"}], "service": "serviceImpl"}