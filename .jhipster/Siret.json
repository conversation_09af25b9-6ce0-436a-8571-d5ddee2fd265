{"applications": "*", "changelogDate": "20240820105902", "clientRootFolder": "legalReferential", "databaseType": "sql", "dto": "mapstruct", "embedded": false, "entityTableName": "siret", "fields": [{"fieldName": "siret", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "14"}, {"fieldName": "status", "fieldType": "Statut", "fieldValidateRules": ["required"], "fieldValues": "A (ACTIF),I (INACTIF)"}, {"fieldName": "establishmentType", "fieldType": "EstablishmentType", "fieldValidateRules": ["required"], "fieldValues": "<PERSON>,<PERSON>"}, {"fieldName": "denomination", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "addressLine1", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressLine2", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressLine3", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressCity", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressCountrySubdivision", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressCountryCode", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "addressCountryLabel", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "2"}, {"fieldName": "b2gMoa", "fieldType": "Boolean"}, {"fieldName": "b2gMoaOnly", "fieldType": "Boolean"}, {"fieldName": "b2gPaymentStatusManagement", "fieldType": "Boolean"}, {"fieldName": "b2gLegalEngagementManagement", "fieldType": "Boolean"}, {"fieldName": "b2gLegalOrServiceEngagementManagement", "fieldType": "Boolean"}, {"fieldName": "b2gServiceCodeManagement", "fieldType": "Boolean"}, {"fieldName": "diffusible", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "addressPostalCode", "fieldType": "String", "fieldValidateRules": ["required", "minlength", "maxlength"], "fieldValidateRulesMaxlength": "5", "fieldValidateRulesMinlength": "2"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "microserviceName": "legalReferential", "name": "<PERSON><PERSON>", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "routingCode", "otherEntityRelationshipName": "fkRoutingCodeSiret", "relationshipName": "pkRoutingCode", "relationshipType": "one-to-many"}, {"otherEntityName": "addressLine", "otherEntityRelationshipName": "fkAddressLineSiret", "relationshipName": "pkAddressLine", "relationshipType": "one-to-many"}, {"otherEntityName": "siren", "otherEntityRelationshipName": "pkSiret", "relationshipName": "fkSiretSiren", "relationshipType": "many-to-one"}], "service": "serviceImpl"}