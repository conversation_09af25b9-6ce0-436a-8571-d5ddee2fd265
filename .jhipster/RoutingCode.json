{"applications": "*", "changelogDate": "**************", "clientRootFolder": "legalReferential", "databaseType": "sql", "dto": "mapstruct", "embedded": false, "entityTableName": "routing_code", "fields": [{"fieldName": "routingCodeID", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "status", "fieldType": "Statut", "fieldValidateRules": ["required"], "fieldValues": "A (ACTIF),I (INACTIF)"}, {"fieldName": "routingCodeLabel", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "routingCodeType", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "administrativeStatus", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "legalEngagementManagement", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"fieldName": "addressLine1", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressLine2", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressLine3", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressPostalCode", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "addressCity", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressCountrySubdivision", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "addressCountryCode", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "addressCountryLabel", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": "2"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "microserviceName": "legalReferential", "name": "RoutingCode", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "addressLine", "otherEntityRelationshipName": "fkAddressLineRoutingCode", "relationshipName": "pkAddressLine", "relationshipType": "one-to-many"}, {"otherEntityName": "siret", "otherEntityRelationshipName": "pkRoutingCode", "relationshipName": "fkRoutingCodeSiret", "relationshipType": "many-to-one"}], "service": "serviceImpl"}